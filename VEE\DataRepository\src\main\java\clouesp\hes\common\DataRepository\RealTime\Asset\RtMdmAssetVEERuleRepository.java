package clouesp.hes.common.DataRepository.RealTime.Asset;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import clouesp.hes.common.DataEntity.Asset.MdmAssetVEERule;

public interface RtMdmAssetVEERuleRepository extends JpaRepository<MdmAssetVEERule, String>{
	@Query(value = "select * from mdm_asset_vee_rule where "
			+ "rule_status = 1 "
			+ "and mg_id = :mgId "
			+ "and rule_type = :ruleType "
			+ "and id in (select rule_id from mdm_asset_vee_rule_datasource "
			+ "where key_flag = 1 and data_type = :dataType "
			+ "and scheme_type = :schemeType and "
			+ "(:sourceEventTypeFlag = 0 or source_event_type = :sourceEventType)) "
			, nativeQuery = true)
	List<MdmAssetVEERule> findMatching(
			@Param("mgId") String mgId, 
			@Param("ruleType") int ruleType, 
			@Param("dataType") int dataType,
			@Param("schemeType") int schemeType, 
			@Param("sourceEventTypeFlag") int sourceEventTypeFlag,
			@Param("sourceEventType") String sourceEventType);

	List<MdmAssetVEERule> findAllByEventId(String eventId);
}
