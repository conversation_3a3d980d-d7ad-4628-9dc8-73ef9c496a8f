/*
 * 文件名：LuceneAppender.java 版权：Copyright by Power7000 Team 描述： 修改人：jybai 修改时间：2017年10月30日 跟踪单号：
 * 修改单号： 修改内容：
 */

package clouesp.hes.common.logger.logger;


import java.io.IOException;
import java.io.Serializable;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;

import org.apache.logging.log4j.core.Filter;
import org.apache.logging.log4j.core.Layout;
import org.apache.logging.log4j.core.LogEvent;
import org.apache.logging.log4j.core.appender.AbstractAppender;
import org.apache.logging.log4j.core.config.plugins.PluginAttribute;
import org.apache.logging.log4j.core.config.plugins.PluginElement;
import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.analysis.standard.StandardAnalyzer;
import org.apache.lucene.document.Document;
import org.apache.lucene.document.Field;
import org.apache.lucene.document.FieldType;
import org.apache.lucene.document.FieldType.NumericType;
import org.apache.lucene.document.LongField;
import org.apache.lucene.document.StringField;
import org.apache.lucene.index.DocValuesType;
import org.apache.lucene.index.IndexOptions;
import org.apache.lucene.index.IndexWriter;
import org.apache.lucene.index.IndexWriterConfig;
import org.apache.lucene.store.Directory;
import org.apache.lucene.store.FSDirectory;
import org.apache.lucene.store.RAMDirectory;


class LuceneAppender extends AbstractAppender {

    protected final LinkedBlockingQueue<LogInfo> logInfoSet =new LinkedBlockingQueue<LogInfo>();

    private Directory ramDir = new RAMDirectory();

    private Analyzer ramAnalyzer = new StandardAnalyzer();

    private IndexWriterConfig ramConfig = null;

    private IndexWriter ramWriter = null;

    private Directory fsDir = null;

    private Analyzer fsAnalyzer = new StandardAnalyzer();

    private IndexWriterConfig fsConfig = null;

    private IndexWriter fsWriter = null;

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
    
    private long lastAppendLogger = System.currentTimeMillis();
    
    //private long indexLogger = 0;
    
    protected LuceneAppender(String name, Filter filter, Layout<? extends Serializable> layout) {
        super(name, filter, layout);
    }

    @Override
    public void append(LogEvent event) {
        Object[] parameters = event.getMessage().getParameters();
        final byte[] bytes = getLayout().toByteArray(event);
        if(bytes.length > 1){
            if(bytes[bytes.length - 2] == 0x0D
                && bytes[bytes.length - 1] == 0x0A){
                bytes[bytes.length - 2] = 0x20;
                bytes[bytes.length - 1] = 0x20;
            }
        }
        String info = new String(bytes);
        LogInfo logInfo = new LogInfo(LoggerUtils.getInstance().Level2LoggerLevel(event.getLevel()), (Date)parameters[0], /*indexLogger++,*/ (String)parameters[1],
            (String)parameters[2], (String)parameters[3], info);
        try {
            logInfoSet.put(logInfo);
        }
        catch (InterruptedException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    public static LuceneAppender createAppender(@PluginAttribute("name")
    String name, @PluginElement("Filter")
    final Filter filter, @PluginElement("Layout")
    Layout<? extends Serializable> layout) {

        return new LuceneAppender(name, filter, layout);
    }

    private boolean isPermitAppendLogger(){
        long curTime = System.currentTimeMillis();
        if(curTime - lastAppendLogger < 60 * 1000
            && logInfoSet.size() < 50000)
            return false;   
        lastAppendLogger = curTime;
        return true;
    }
    
    public void appendLogger() {
        if(!isPermitAppendLogger())
            return;
        directAppendLogger();
    }
    
    public void directAppendLogger() {  
     
        List<LogInfo> docCache = new ArrayList<LogInfo>();
        logInfoSet.drainTo(docCache);
        if(docCache.size() == 0){
            return;
        }
        
        try {
            ramConfig = null;
            ramConfig = new IndexWriterConfig(ramAnalyzer);
            ramWriter = new IndexWriter(ramDir, ramConfig);

            String strCurDay = null;
            String strLastDay = null;

            for (LogInfo logInfo : docCache) {
               
                Document ramDoc = new Document();
               
                if(logInfo != null){
                    ramDoc.add(new StringField("level", logInfo.level.name(), Field.Store.YES));
                }

                strCurDay = sdf.format(logInfo.date);
                if (strLastDay == null) strLastDay = strCurDay;
                
                FieldType orderByTime = new FieldType();
                orderByTime.setStored(true);
                orderByTime.setIndexOptions(IndexOptions.DOCS);//设置索引类型
                orderByTime.setNumericType(NumericType.LONG);//数值类型  
                orderByTime.setDocValuesType(DocValuesType.NUMERIC);//DocValue类型
                ramDoc.add(new LongField("time", logInfo.date.getTime(), orderByTime));
                
                //FieldType orderByIndex = new FieldType();
                //orderByIndex.setStored(false);
                //orderByIndex.setIndexOptions(IndexOptions.DOCS);//设置索引类型
                //orderByIndex.setNumericType(NumericType.LONG);//数值类型  
                //orderByIndex.setDocValuesType(DocValuesType.NUMERIC);//        
                //ramDoc.add(new LongField("indexLogger", logInfo.index, orderByIndex));
                
                if (logInfo.serviceId != null)
                    ramDoc.add(new StringField("serviceId", logInfo.serviceId, Field.Store.YES));

                if (logInfo.deviceId != null)
                    ramDoc.add(new StringField("deviceId", logInfo.deviceId, Field.Store.YES));

                if (logInfo.infoType != null)
                    ramDoc.add(new StringField("infoType", logInfo.infoType, Field.Store.YES));

  
                if (logInfo.info != null)
                    ramDoc.add(new StringField("info", logInfo.info, Field.Store.YES));

                if (!strCurDay.equals(strLastDay)) {

                    if (ramWriter.numDocs() > 0) {
                        if (strCurDay != null) {
                            ramWriter.commit();
                            ramWriter.close();

                            fsDir = FSDirectory.open(Paths.get(Logger.getInstance().getIndexPath() + strLastDay));
                            if (!IndexWriter.isLocked(fsDir)) {
                                fsConfig = new IndexWriterConfig(fsAnalyzer);
                                fsWriter = new IndexWriter(fsDir, fsConfig);
                                fsWriter.addIndexes(ramDir);
                                fsWriter.commit();
                                fsWriter.close();

                                fsWriter = null;
                                ramDir = null;
                                ramDir = new RAMDirectory();
                            }

                            ramWriter = null;
                            ramConfig = null;
                            ramConfig = new IndexWriterConfig(ramAnalyzer);
                            ramWriter = new IndexWriter(ramDir, ramConfig);

                        }
                    }
                    strLastDay = strCurDay;
                }

                ramWriter.addDocument(ramDoc);
            }

            if (ramWriter.numDocs() > 0) {

                if (strCurDay != null) {

                    ramWriter.commit();
                    ramWriter.close();

                    fsDir = FSDirectory.open(Paths.get(Logger.getInstance().getIndexPath() + strLastDay));
                    if (!IndexWriter.isLocked(fsDir)) {
                        fsConfig = new IndexWriterConfig(fsAnalyzer);
                        fsWriter = new IndexWriter(fsDir, fsConfig);
                        fsWriter.addIndexes(ramDir);
                        fsWriter.commit();
                        fsWriter.close();

                        fsWriter = null;
                        ramDir = null;
                        ramDir = new RAMDirectory();
                    }
                }
            }

        }
        catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        finally {
            try {
                if (fsWriter != null) fsWriter.close();
            }
            catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
    }
}
