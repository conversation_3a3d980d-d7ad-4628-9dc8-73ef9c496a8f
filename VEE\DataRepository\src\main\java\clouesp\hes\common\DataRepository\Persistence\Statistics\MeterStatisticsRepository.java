package clouesp.hes.common.DataRepository.Persistence.Statistics;

import clouesp.hes.common.DataEntity.Statistics.MeterStatistics;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface MeterStatisticsRepository extends JpaRepository<MeterStatistics, String> {

     @Query(value ="select inner_value from MDM_DICT_DETAIL where is_default =1 and dict_id = :dict_id ",nativeQuery = true)
          List<Integer> findDictInnerValueByDictId(@Param("dict_id") String dictId);

    // @Query(value ="select org_id ,count(*) as meter_count from asset_meter  GROUP BY org_id",nativeQuery = true)
   @Query(value ="select org_id ,count(*) as meter_count from asset_meter where METER_STATUS=1 and  create_time >= trunc(sysdate)-1  and create_time < trunc(sysdate) GROUP BY org_id",nativeQuery = true)
     List<MeterStatistics> findOracleYesterdayNewMeterCount();

    @Query(value ="select org_id ,count(*) as meter_count from asset_meter where METER_STATUS=1 and  create_time >= DATE(NOW() - INTERVAL 1 DAY)  and create_time < DATE(NOW())   GROUP BY org_id",nativeQuery = true)
    List<MeterStatistics> findMysqlYesterdayNewMeterCount();


    // @Query(value ="select org_id , count(*) as meter_count from (select b.org_id ,  a.* from MDM_ASSET_METER_REPLACEMENT a, MDM_ASSET_SERVICE_POINT b  where a.sdp_id = b.id  and (a.OPERATION_TYPE = 1 or a.OPERATION_TYPE = 2) )  group by org_id",nativeQuery = true)
    @Query(value ="select org_id , count(*) as meter_count from (select b.org_id ,  a.* from MDM_ASSET_METER_REPLACEMENT a, MDM_ASSET_SERVICE_POINT b  where a.sdp_id = b.id and     a.tv >= trunc(sysdate)-1  and a.tv < trunc(sysdate) and (a.OPERATION_TYPE = 1 or a.OPERATION_TYPE = 2) )  group by org_id",nativeQuery = true)
     List<MeterStatistics> findOracleYesterdayInstallMeterCount();

    @Query(value ="select org_id , count(*) as meter_count from (select b.org_id ,  a.* from MDM_ASSET_METER_REPLACEMENT a, MDM_ASSET_SERVICE_POINT b  where a.sdp_id = b.id and     a.tv >= DATE(NOW() - INTERVAL 1 DAY)  and a.tv <  DATE(NOW()) and (a.OPERATION_TYPE = 1 or a.OPERATION_TYPE = 2) )  c group by org_id",nativeQuery = true)
    List<MeterStatistics> findMysqlYesterdayInstallMeterCount();

   //  @Query(value ="select org_id , count(*) as meter_count from (select b.org_id ,  a.* from MDM_ASSET_METER_REPLACEMENT a, MDM_ASSET_SERVICE_POINT b  where a.sdp_id = b.id  and (a.OPERATION_TYPE = 3 or a.OPERATION_TYPE = 2) )  group by org_id",nativeQuery = true)
      @Query(value ="select org_id , count(*) as meter_count from (select b.org_id ,  a.* from MDM_ASSET_METER_REPLACEMENT a, MDM_ASSET_SERVICE_POINT b  where a.sdp_id = b.id and a.tv >= trunc(sysdate)-1  and a.tv < trunc(sysdate) and (a.OPERATION_TYPE = 3 or a.OPERATION_TYPE = 2) )  group by org_id",nativeQuery = true)
     List<MeterStatistics> findOracleYesterdayRemoveMeterCount();

    @Query(value ="select org_id , count(*) as meter_count from (select b.org_id ,  a.* from MDM_ASSET_METER_REPLACEMENT a, MDM_ASSET_SERVICE_POINT b  where a.sdp_id = b.id and a.tv >= DATE(NOW() - INTERVAL 1 DAY)  and a.tv < DATE(NOW()) and (a.OPERATION_TYPE = 3 or a.OPERATION_TYPE = 2) )  c group by org_id",nativeQuery = true)
    List<MeterStatistics> findMysqlYesterdayRemoveMeterCount();

     @Query(value ="select org_id , count(*) as meter_count from (select b.org_id ,  a.* from mdm_data_update_log a, MDM_ASSET_SERVICE_POINT b  where a.data_type = :data_type and a.sdp_id = b.id  and  a.scheme_type = :scheme_type and    (  a.last_data_time >= trunc(sysdate) - :noDay  or :noDay >= 6 )  and a.last_data_time < trunc(sysdate) + 1 - :noDay )  group by org_id",nativeQuery = true)
     List<MeterStatistics> findOraclePrgressCountByDataTypeAndDay(
             @Param("scheme_type") int schemeType,
             @Param("data_type") int dataType,
             @Param("noDay") int noDay);

    @Query(value ="select org_id , count(*) as meter_count from (select b.org_id ,  a.* from mdm_data_update_log a, MDM_ASSET_SERVICE_POINT b  where a.data_type = :data_type and a.sdp_id = b.id  and  a.scheme_type = :scheme_type and    (  a.last_data_time >= DATE(NOW() - INTERVAL :noDay DAY)  or :noDay >= 6 )  and a.last_data_time < DATE(NOW()   - INTERVAL ( :noDay -1 ) DAY )) c  group by org_id",nativeQuery = true)
    List<MeterStatistics> findMysqlPrgressCountByDataTypeAndDay(
            @Param("scheme_type") int schemeType,
            @Param("data_type") int dataType,
            @Param("noDay") int noDay);



}
