package clouesp.hes.core.DataImport.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.apache.ibatis.mapping.VendorDatabaseIdProvider;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.Properties;

@Slf4j
@Configuration
@MapperScan(basePackages = {"clouesp.hes.core.DataImport.dao.Persistence"}, sqlSessionFactoryRef = "persistenceSqlSessionFactory")
public class PersistenceConfig {	
	@Bean(name = "persistenceManager")
    @Resource
    public PlatformTransactionManager persistenceManager(@Qualifier("persistenceDS")DataSource dataSource){
        return new DataSourceTransactionManager(dataSource);
    }
	
	 /**
     * 配置Mapper路径
     * @param dataSource
     * @return
     * @throws Exception
     */
    @Bean(name = "persistenceSqlSessionFactory")
    public  SqlSessionFactory persistenceSqlSessionFactory(@Qualifier("persistenceDS") DataSource dataSource) throws Exception  {
        SqlSessionFactoryBean  bean  =  new  SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setDatabaseIdProvider(getDatabaseIdProvider());
        //添加XML目录
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        try{
            bean.setMapperLocations(resolver.getResources("classpath:mybatis/Persistence/*/*.xml"));
            bean.setConfigLocation(resolver.getResource("classpath:mybatis/Persistence/mybatis-config.xml"));
            bean.setTypeAliasesPackage("clouesp.hes.common.DataEntity");
            bean.getObject().getConfiguration().setMapUnderscoreToCamelCase(true);
            return  bean.getObject();
        }catch(Exception  e){
           // e.printStackTrace();
            log.error("DataImport Error : " , e);
            throw new RuntimeException(e);
        }
    }
    
    @Bean(name = "persistenceSqlSessionTemplate")
    public SqlSessionTemplate persistenceSqlSessionTemplate(@Qualifier("persistenceSqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        SqlSessionTemplate template = new SqlSessionTemplate(sqlSessionFactory); // 使用上面配置的Factory
        return template;
    }

    @Bean
    public DatabaseIdProvider getDatabaseIdProvider() {
        DatabaseIdProvider databaseIdProvider = new VendorDatabaseIdProvider();
        Properties properties = new Properties();
        properties.setProperty("MySQL", "mysql");
        properties.setProperty("Oracle", "oracle");
        databaseIdProvider.setProperties(properties);
        return databaseIdProvider;
    }
}
