package clouesp.hes.common.DataRepository.RealTime.Data;

import java.util.Date;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import clouesp.hes.common.DataEntity.Data.DataVEEEvent;

public interface RtDataVEEEventRepository extends JpaRepository<DataVEEEvent, String>{
	@Query(value = "select * from mdm_data_vee_event where "
			+ "object_id = :objectId "
			+ "and event_id = :eventId "
			+ "and tv < :tv order by tv desc limit 1 "
			, nativeQuery = true)
	DataVEEEvent findEvent(
			@Param("objectId") String objectId,
			@Param("eventId") String eventId,
			@Param("tv") String tv
			);
	
	@Modifying
	@Transactional
	@Query(value = "delete from mdm_data_vee_event where tv < :tv"
			, nativeQuery = true)
	void clearExpired(
			@Param("tv")Date tv
			);		
}
