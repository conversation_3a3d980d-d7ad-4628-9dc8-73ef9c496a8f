<?xml version="1.0" encoding="UTF-8"?>
<module org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_8">
    <output url="file://$MODULE_DIR$/target/classes" />
    <output-test url="file://$MODULE_DIR$/target/test-classes" />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/java" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-core:2.9.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-api:2.9.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-queryparser:5.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-sandbox:5.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-queries:5.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-core:5.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-highlighter:5.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-join:5.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-memory:5.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.lucene:lucene-analyzers-common:5.0.0" level="project" />
  </component>
</module>