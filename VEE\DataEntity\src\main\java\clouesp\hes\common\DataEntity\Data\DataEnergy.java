package clouesp.hes.common.DataEntity.Data;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.MappedSuperclass;

@MappedSuperclass
public class DataEnergy {
	@EmbeddedId
	private DataEnergyPK dataEnergyPK = new DataEnergyPK();
	@Column(name = "update_tv")
	private Date updateTv;
	@Column(name = "R0P1")
	private Double R0P1 = 0d;
	@Column(name = "R0P2")
	private Double R0P2 = 0d;
	@Column(name = "R0P3")
	private Double R0P3 = 0d;
	@Column(name = "R0P4")
	private Double R0P4 = 0d;
	
	@Column(name = "R1P1")
	private Double R1P1 = 0d;
	@Column(name = "R1P2")
	private Double R1P2 = 0d;
	@Column(name = "R1P3")
	private Double R1P3 = 0d;
	@Column(name = "R1P4")
	private Double R1P4 = 0d;
	
	@Column(name = "R2P1")
	private Double R2P1 = 0d;
	@Column(name = "R2P2")
	private Double R2P2 = 0d;
	@Column(name = "R2P3")
	private Double R2P3 = 0d;
	@Column(name = "R2P4")
	private Double R2P4 = 0d;	
	
	@Column(name = "R3P1")
	private Double R3P1 = 0d;
	@Column(name = "R3P2")
	private Double R3P2 = 0d;
	@Column(name = "R3P3")
	private Double R3P3 = 0d;
	@Column(name = "R3P4")
	private Double R3P4 = 0d;	
	
	@Column(name = "R4P1")
	private Double R4P1 = 0d;
	@Column(name = "R4P2")
	private Double R4P2 = 0d;
	@Column(name = "R4P3")
	private Double R4P3 = 0d;
	@Column(name = "R4P4")
	private Double R4P4 = 0d;	
	
	@Column(name = "R0P1A1", columnDefinition = "decimal(21,6)")
	private Double R0P1A1;
	@Column(name = "R0P1A2", columnDefinition = "decimal(21,6)")
	private Double R0P1A2;
	@Column(name = "R0P1A3", columnDefinition = "decimal(21,6)")
	private Double R0P1A3;
	@Column(name = "R0P1A4", columnDefinition = "decimal(21,6)")
	private Double R0P1A4;
	
	@Column(name = "R1P1A1", columnDefinition = "decimal(21,6)")
	private Double R1P1A1;
	@Column(name = "R1P1A2", columnDefinition = "decimal(21,6)")
	private Double R1P1A2;
	@Column(name = "R1P1A3", columnDefinition = "decimal(21,6)")
	private Double R1P1A3;
	@Column(name = "R1P1A4", columnDefinition = "decimal(21,6)")
	private Double R1P1A4;
	
	
	@Column(name = "R2P1A1", columnDefinition = "decimal(21,6)")
	private Double R2P1A1;
	@Column(name = "R2P1A2", columnDefinition = "decimal(21,6)")
	private Double R2P1A2;
	@Column(name = "R2P1A3", columnDefinition = "decimal(21,6)")
	private Double R2P1A3;
	@Column(name = "R2P1A4", columnDefinition = "decimal(21,6)")
	private Double R2P1A4;
	
	@Column(name = "R3P1A1", columnDefinition = "decimal(21,6)")
	private Double R3P1A1;
	@Column(name = "R3P1A2", columnDefinition = "decimal(21,6)")
	private Double R3P1A2;
	@Column(name = "R3P1A3", columnDefinition = "decimal(21,6)")
	private Double R3P1A3;
	@Column(name = "R3P1A4", columnDefinition = "decimal(21,6)")
	private Double R3P1A4;
	
	@Column(name = "R4P1A1", columnDefinition = "decimal(21,6)")
	private Double R4P1A1;
	@Column(name = "R4P1A2", columnDefinition = "decimal(21,6)")
	private Double R4P1A2;
	@Column(name = "R4P1A3", columnDefinition = "decimal(21,6)")
	private Double R4P1A3;
	@Column(name = "R4P1A4", columnDefinition = "decimal(21,6)")
	private Double R4P1A4;

	@Column(name = "data_source")
	private Integer dataSource = 0;
	
	@Column(name = "data_version")
	private Integer dataVersion = 0;
	
	public DataEnergyPK getDataEnergyPK() {
		return dataEnergyPK;
	}
	public void setDataEnergyPK(DataEnergyPK dataEnergyPK) {
		this.dataEnergyPK = dataEnergyPK;
	}
	public Date getUpdateTv() {
		return updateTv;
	}
	public void setUpdateTv(Date updateTv) {
		this.updateTv = updateTv;
	}	
	public Double getR0P1() {
		return R0P1;
	}
	public void setR0P1(Double r0p1) {
		R0P1 = r0p1;
	}
	public Double getR0P2() {
		return R0P2;
	}
	public void setR0P2(Double r0p2) {
		R0P2 = r0p2;
	}
	public Double getR0P3() {
		return R0P3;
	}
	public void setR0P3(Double r0p3) {
		R0P3 = r0p3;
	}
	public Double getR0P4() {
		return R0P4;
	}
	public void setR0P4(Double r0p4) {
		R0P4 = r0p4;
	}	
	public Double getR1P1() {
		return R1P1;
	}
	public void setR1P1(Double r1p1) {
		R1P1 = r1p1;
	}
	public Double getR1P2() {
		return R1P2;
	}
	public void setR1P2(Double r1p2) {
		R1P2 = r1p2;
	}
	public Double getR1P3() {
		return R1P3;
	}
	public void setR1P3(Double r1p3) {
		R1P3 = r1p3;
	}
	public Double getR1P4() {
		return R1P4;
	}
	public void setR1P4(Double r1p4) {
		R1P4 = r1p4;
	}
	public Double getR2P1() {
		return R2P1;
	}
	public void setR2P1(Double r2p1) {
		R2P1 = r2p1;
	}
	public Double getR2P2() {
		return R2P2;
	}
	public void setR2P2(Double r2p2) {
		R2P2 = r2p2;
	}
	public Double getR2P3() {
		return R2P3;
	}
	public void setR2P3(Double r2p3) {
		R2P3 = r2p3;
	}
	public Double getR2P4() {
		return R2P4;
	}
	public void setR2P4(Double r2p4) {
		R2P4 = r2p4;
	}
	public Double getR3P1() {
		return R3P1;
	}
	public void setR3P1(Double r3p1) {
		R3P1 = r3p1;
	}
	public Double getR3P2() {
		return R3P2;
	}
	public void setR3P2(Double r3p2) {
		R3P2 = r3p2;
	}
	public Double getR3P3() {
		return R3P3;
	}
	public void setR3P3(Double r3p3) {
		R3P3 = r3p3;
	}
	public Double getR3P4() {
		return R3P4;
	}
	public void setR3P4(Double r3p4) {
		R3P4 = r3p4;
	}
	public Double getR4P1() {
		return R4P1;
	}
	public void setR4P1(Double r4p1) {
		R4P1 = r4p1;
	}
	public Double getR4P2() {
		return R4P2;
	}
	public void setR4P2(Double r4p2) {
		R4P2 = r4p2;
	}
	public Double getR4P3() {
		return R4P3;
	}
	public void setR4P3(Double r4p3) {
		R4P3 = r4p3;
	}
	public Double getR4P4() {
		return R4P4;
	}
	public void setR4P4(Double r4p4) {
		R4P4 = r4p4;
	}
	
	public Double getR0P1A1() {
		return R0P1A1;
	}
	public void setR0P1A1(Double r0p1a1) {
		R0P1A1 = r0p1a1;
	}
	public Double getR0P1A2() {
		return R0P1A2;
	}
	public void setR0P1A2(Double r0p1a2) {
		R0P1A2 = r0p1a2;
	}
	public Double getR0P1A3() {
		return R0P1A3;
	}
	public void setR0P1A3(Double r0p1a3) {
		R0P1A3 = r0p1a3;
	}
	public Double getR0P1A4() {
		return R0P1A4;
	}
	public void setR0P1A4(Double r0p1a4) {
		R0P1A4 = r0p1a4;
	}
	public Double getR1P1A1() {
		return R1P1A1;
	}
	public void setR1P1A1(Double r1p1a1) {
		R1P1A1 = r1p1a1;
	}
	public Double getR1P1A2() {
		return R1P1A2;
	}
	public void setR1P1A2(Double r1p1a2) {
		R1P1A2 = r1p1a2;
	}
	public Double getR1P1A3() {
		return R1P1A3;
	}
	public void setR1P1A3(Double r1p1a3) {
		R1P1A3 = r1p1a3;
	}
	public Double getR1P1A4() {
		return R1P1A4;
	}
	public void setR1P1A4(Double r1p1a4) {
		R1P1A4 = r1p1a4;
	}
	public Double getR2P1A1() {
		return R2P1A1;
	}
	public void setR2P1A1(Double r2p1a1) {
		R2P1A1 = r2p1a1;
	}
	public Double getR2P1A2() {
		return R2P1A2;
	}
	public void setR2P1A2(Double r2p1a2) {
		R2P1A2 = r2p1a2;
	}
	public Double getR2P1A3() {
		return R2P1A3;
	}
	public void setR2P1A3(Double r2p1a3) {
		R2P1A3 = r2p1a3;
	}
	public Double getR2P1A4() {
		return R2P1A4;
	}
	public void setR2P1A4(Double r2p1a4) {
		R2P1A4 = r2p1a4;
	}
	public Double getR3P1A1() {
		return R3P1A1;
	}
	public void setR3P1A1(Double r3p1a1) {
		R3P1A1 = r3p1a1;
	}
	public Double getR3P1A2() {
		return R3P1A2;
	}
	public void setR3P1A2(Double r3p1a2) {
		R3P1A2 = r3p1a2;
	}
	public Double getR3P1A3() {
		return R3P1A3;
	}
	public void setR3P1A3(Double r3p1a3) {
		R3P1A3 = r3p1a3;
	}
	public Double getR3P1A4() {
		return R3P1A4;
	}
	public void setR3P1A4(Double r3p1a4) {
		R3P1A4 = r3p1a4;
	}
	public Double getR4P1A1() {
		return R4P1A1;
	}
	public void setR4P1A1(Double r4p1a1) {
		R4P1A1 = r4p1a1;
	}
	public Double getR4P1A2() {
		return R4P1A2;
	}
	public void setR4P1A2(Double r4p1a2) {
		R4P1A2 = r4p1a2;
	}
	public Double getR4P1A3() {
		return R4P1A3;
	}
	public void setR4P1A3(Double r4p1a3) {
		R4P1A3 = r4p1a3;
	}
	public Double getR4P1A4() {
		return R4P1A4;
	}
	public void setR4P1A4(Double r4p1a4) {
		R4P1A4 = r4p1a4;
	}
	public Integer getDataSource() {
		return dataSource;
	}
	public void setDataSource(Integer dataSource) {
		this.dataSource = dataSource;
	}
	public Integer getDataVersion() {
		return dataVersion;
	}
	public void setDataVersion(Integer dataVersion) {
		this.dataVersion = dataVersion;
	}
}
