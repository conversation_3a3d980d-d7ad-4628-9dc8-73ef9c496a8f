package clouesp.hes.common.DataModel.api;

import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value="reqEstimateMsg",description="需修补数据信息")
public class ReqEstimateMsg {
	@ApiModelProperty(value="规则ID", position=1)
	private String ruleId;
	@ApiModelProperty(value="计量点ID", position=2)
	private String sdpId;
	@ApiModelProperty(value="SCHEME_ID", position=3)
	private String schemeId;	
	@ApiModelProperty(value="DATA_TYPE", position=4)
	private Integer dataType;	
	@ApiModelProperty(value="需修补数据时间集", position=5)
	private List<Date> dataTvs;
	public String getRuleId() {
		return ruleId;
	}
	public void setRuleId(String ruleId) {
		this.ruleId = ruleId;
	}
	public String getSdpId() {
		return sdpId;
	}
	public void setSdpId(String sdpId) {
		this.sdpId = sdpId;
	}
	public String getSchemeId() {
		return schemeId;
	}
	public void setSchemeId(String schemeId) {
		this.schemeId = schemeId;
	}
	public Integer getDataType() {
		return dataType;
	}
	public void setDataType(Integer dataType) {
		this.dataType = dataType;
	}
	public List<Date> getDataTvs() {
		return dataTvs;
	}
	public void setDataTvs(List<Date> dataTvs) {
		this.dataTvs = dataTvs;
	}
}
