package clouesp.hes.common.DataEntity.Asset;

import javax.persistence.*;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value="mdmAssetServicePoint",description="计量点信息")
@Entity
@Table(name= "mdm_asset_service_point",
indexes = {@Index(name = "INDEX_METERID",  columnList="meter_id", unique = false)})
public class MdmAssetServicePoint {
	@ApiModelProperty(value="计量点ID", position=1)
	@Id
	@Column(name = "id", nullable = false, columnDefinition = "varchar(32)")
	private String id;
	
	@ApiModelProperty(value="表计编码", position=2)
	@Column(name = "meter_id", columnDefinition = "varchar(32)")
	private String meterId;
	
	@ApiModelProperty(value="结算方案编码组，以逗号分隔", position=3)
	@Column(name = "scheme_ids", columnDefinition = "varchar(32)")
	private String schemeIds;
	
	@ApiModelProperty(value="VEE校验规则分组ID，取自MDM_ASSET_VEE_GROUP", position=4)
	@Column(name = "vee_validation_group_id", columnDefinition = "varchar(32)")
	private String veeValidationGroupId;
	
	@ApiModelProperty(value="VEE估算规则分组ID，取自MDM_ASSET_VEE_GROUP", position=5)
	@Column(name = "vee_estimation_group_id", columnDefinition = "varchar(32)")
	private String veeEstimationGroupId;
	
	@ApiModelProperty(value="VEE计算规则分组ID，取自MDM_ASSET_VEE_GROUP", position=6)
	@Column(name = "vee_calculation_group_id", columnDefinition = "varchar(32)")
	private String veeCalculationGroupId;
	
	@ApiModelProperty(value="表码数据的时间密度（分钟）", position=7)
	@Column(name = "register_scheme")
	private Integer registerScheme;
	
	@ApiModelProperty(value="增量数据的时间密度(分钟)", position=8)
	@Column(name = "interval_scheme")
	private Integer intervalScheme;	
	
	@ApiModelProperty(value="瞬时量数据的时间密度(分钟)", position=9)
	@Column(name = "inst_scheme")
	private Integer instScheme;		
	
	@ApiModelProperty(value="CT字典值, 字典值1102", position=10)
	@Column(name = "ct")
	private Integer ct;
	
	@ApiModelProperty(value="PT字典值, 字典值1101", position=11)
	@Column(name = "pt")
	private Integer pt;


	@ApiModelProperty(value="sn", position=12)
	@Column(name = "sn")
	private String sn;





	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getMeterId() {
		return meterId;
	}

	public void setMeterId(String meterId) {
		this.meterId = meterId;
	}

	public String getSchemeIds() {
		return schemeIds;
	}

	public void setSchemeIds(String schemeIds) {
		this.schemeIds = schemeIds;
	}

	public String getVeeValidationGroupId() {
		return veeValidationGroupId;
	}

	public void setVeeValidationGroupId(String veeValidationGroupId) {
		this.veeValidationGroupId = veeValidationGroupId;
	}

	public String getVeeEstimationGroupId() {
		return veeEstimationGroupId;
	}

	public void setVeeEstimationGroupId(String veeEstimationGroupId) {
		this.veeEstimationGroupId = veeEstimationGroupId;
	}

	public String getVeeCalculationGroupId() {
		return veeCalculationGroupId;
	}

	public void setVeeCalculationGroupId(String veeCalculationGroupId) {
		this.veeCalculationGroupId = veeCalculationGroupId;
	}

	public Integer getRegisterScheme() {
		return registerScheme;
	}

	public void setRegisterScheme(Integer registerScheme) {
		this.registerScheme = registerScheme;
	}

	public Integer getIntervalScheme() {
		return intervalScheme;
	}

	public void setIntervalScheme(Integer intervalScheme) {
		this.intervalScheme = intervalScheme;
	}

	public Integer getInstScheme() {
		return instScheme;
	}

	public void setInstScheme(Integer instScheme) {
		this.instScheme = instScheme;
	}

	public Integer getCt() {
		return ct;
	}

	public void setCt(Integer ct) {
		this.ct = ct;
	}

	public Integer getPt() {
		return pt;
	}

	public void setPt(Integer pt) {
		this.pt = pt;
	}

	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}



	public Integer getLineLossInOut() {
		return lineLossInOut;
	}

	public void setLineLossInOut(Integer lineLossInOut) {
		this.lineLossInOut = lineLossInOut;
	}


	@Transient
	private Integer lineLossInOut;

	public Integer getLinLostCalFormula() {
		return linLostCalFormula;
	}

	public void setLinLostCalFormula(Integer linLostCalFormula) {
		this.linLostCalFormula = linLostCalFormula;
	}


	@Transient
	private Integer linLostCalFormula;


	public boolean isCalcFlag() {
		return calcFlag;
	}

	public void setCalcFlag(boolean calcFlag) {
		this.calcFlag = calcFlag;
	}

	//是否参与整个线损计算
	@Transient
	private boolean calcFlag;


}
