<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="UserLogMapper">

    <select id="getUserLogList" resultType="UserLogRsp"  parameterType="PagingRequest">
        select
        su.name as  userName,
        dul.*
        from
        data_user_log dul,
        sys_user su
        where
        dul.user_id = su.id

        <if test="params.orgIdList != null and params.orgIdList.size > 0">
            and su.org_id in
            <foreach collection="params.orgIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="entity">
            <if test="entity.sn != null and  entity.sn != ''">
                and dul.detail like concat(concat('%', #{entity.sn}),'%')
            </if>
            <if test="entity.userIdList != null and entity.userIdList.size > 0">
                and su.id in
                <foreach collection="entity.userIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="entity.logSubTypeList != null and entity.logSubTypeList.size > 0">
                and
                <foreach collection="entity.logSubTypeList" item="item" separator="or" open="(" close=")">
                    <if test="_databaseId == 'mysql'">
                        (dul.LOG_TYPE = SUBSTRING_INDEX(#{item}, ',', 1) and dul.LOG_SUB_TYPE = SUBSTRING_INDEX(SUBSTRING_INDEX(#{item}, ',', 2), ',', -1))
                    </if>
                    <if test="_databaseId == 'oracle'">
                        (dul.LOG_TYPE = SUBSTR(#{item}, 1, INSTR(#{item}, ',')-1) and dul.LOG_SUB_TYPE =  SUBSTR(#{item}, INSTR(#{item}, ',') + 1))
                    </if>

                </foreach>


            </if>
            <if test="entity.startTv != null">
                and dul.tv &gt;= #{entity.startTv}
            </if>
            <if test="entity.endTv != null">
                and dul.tv &lt; #{entity.endTv}
            </if>
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

</mapper>