-- ============================================================================
-- Summary Object Statistics 缺失数据补充脚本
-- 专门为 Summary Object Statistics 图表和表格提供完整的演示数据
-- 目标ORG_ID: 89344c8b02004e5692baa58a7bfabab7
-- 生成时间: 2025-01-11
--
-- 重要说明：
-- 1. 本脚本使用与 total.sql 中相同的计算对象ID格式
-- 2. 必须先执行 total.sql，再执行本脚本
-- 3. 计算对象ID必须与 MDM_ASSET_CALC_OBJ 表中的ID匹配
-- 4. total.sql 中使用了 CALC_OBJ_001 到 CALC_OBJ_006 的ID格式
-- 5. 本脚本为这些现有对象补充历史数据，并新增更多对象
-- 6. 支持多次执行，会自动清理和重新插入数据
-- ============================================================================

SET SERVEROUTPUT ON;
SET ECHO OFF;
WHENEVER SQLERROR EXIT SQL.SQLCODE;

PROMPT '正在补充 Summary Object Statistics 缺失数据...';

-- 检查前置条件：确保 total.sql 已经执行
DECLARE
    calc_obj_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO calc_obj_count
    FROM MDM_ASSET_CALC_OBJ
    WHERE ID IN ('CALC_OBJ_001', 'CALC_OBJ_002', 'CALC_OBJ_003', 'CALC_OBJ_004', 'CALC_OBJ_005', 'CALC_OBJ_006')
    AND ORG_ID = '89344c8b02004e5692baa58a7bfabab7';

    IF calc_obj_count < 6 THEN
        DBMS_OUTPUT.PUT_LINE('错误：请先执行 total.sql 脚本！');
        DBMS_OUTPUT.PUT_LINE('当前只找到 ' || calc_obj_count || ' 个基础计算对象，需要6个。');
        RAISE_APPLICATION_ERROR(-20001, '前置条件检查失败：请先执行 total.sql');
    ELSE
        DBMS_OUTPUT.PUT_LINE('前置条件检查通过：找到 ' || calc_obj_count || ' 个基础计算对象');
    END IF;
END;
/

-- ============================================================================
-- 第一步：清理本脚本相关的数据，确保可以多次执行
-- ============================================================================

PROMPT '正在清理现有的补充数据...';

-- 清理新增计算对象的能源数据
DELETE FROM MDM_DATA_CALC_OBJ_ENERGY
WHERE CALC_OBJ_ID IN ('CALC_OBJ_007', 'CALC_OBJ_008', 'CALC_OBJ_009', 'CALC_OBJ_010')
AND SCHEME_ID = '7';

-- 清理新增的计算对象
DELETE FROM MDM_ASSET_CALC_OBJ
WHERE ID IN ('CALC_OBJ_007', 'CALC_OBJ_008', 'CALC_OBJ_009', 'CALC_OBJ_010')
AND ORG_ID = '89344c8b02004e5692baa58a7bfabab7';

-- 清理现有计算对象的补充历史数据（SYSDATE-3 到 SYSDATE-7）
DELETE FROM MDM_DATA_CALC_OBJ_ENERGY
WHERE CALC_OBJ_ID IN ('CALC_OBJ_001', 'CALC_OBJ_002', 'CALC_OBJ_003', 'CALC_OBJ_004', 'CALC_OBJ_005', 'CALC_OBJ_006')
AND SCHEME_ID = '7'
AND TV >= TRUNC(SYSDATE-7)
AND TV <= TRUNC(SYSDATE-3);

PROMPT '清理完成，开始插入新数据...';

-- ============================================================================
-- 第三步：补充最近7天的完整历史数据
-- 确保 getSummaryObjectStatisticsLine 有足够的数据显示图表
-- 使用 total.sql 中已创建的计算对象ID
-- ============================================================================

PROMPT '正在补充现有计算对象的历史数据...';

-- 补充 SYSDATE-3 到 SYSDATE-7 的数据
-- 使用 total.sql 中定义的计算对象ID：CALC_OBJ_001 到 CALC_OBJ_006
-- 注意：这些ID在 total.sql 中已经创建，这里只是补充历史数据

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_001', '7', TRUNC(SYSDATE-3), 1120.8, 890.6, 1050.2, 820.4);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_002', '7', TRUNC(SYSDATE-3), 850.6, 650.8, 790.3, 610.5);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_003', '7', TRUNC(SYSDATE-3), 610.5, 450.7, 580.2, 420.8);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_004', '7', TRUNC(SYSDATE-3), 380.8, 320.5, 350.6, 290.3);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_005', '7', TRUNC(SYSDATE-3), 250.6, 200.8, 230.4, 180.6);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_006', '7', TRUNC(SYSDATE-3), 170.4, 140.6, 160.2, 130.8);

-- SYSDATE-4 数据
INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_001', '7', TRUNC(SYSDATE-4), 1080.5, 860.3, 1020.7, 790.2);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_002', '7', TRUNC(SYSDATE-4), 820.3, 630.5, 760.8, 580.4);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_003', '7', TRUNC(SYSDATE-4), 580.4, 420.6, 550.3, 400.2);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_004', '7', TRUNC(SYSDATE-4), 360.2, 300.8, 330.5, 270.6);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_005', '7', TRUNC(SYSDATE-4), 230.8, 180.4, 210.6, 160.2);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_006', '7', TRUNC(SYSDATE-4), 150.6, 120.8, 140.4, 110.6);

-- SYSDATE-5 数据
INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_001', '7', TRUNC(SYSDATE-5), 1050.2, 830.8, 990.5, 760.3);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_002', '7', TRUNC(SYSDATE-5), 790.8, 610.2, 730.5, 550.8);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_003', '7', TRUNC(SYSDATE-5), 550.8, 390.4, 520.6, 380.5);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_004', '7', TRUNC(SYSDATE-5), 340.5, 280.6, 310.8, 250.4);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_005', '7', TRUNC(SYSDATE-5), 210.4, 160.8, 190.2, 140.6);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_006', '7', TRUNC(SYSDATE-5), 130.8, 100.4, 120.6, 90.8);

-- SYSDATE-6 数据
INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_001', '7', TRUNC(SYSDATE-6), 1020.7, 800.5, 960.8, 730.6);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_002', '7', TRUNC(SYSDATE-6), 760.5, 580.8, 700.2, 520.5);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_003', '7', TRUNC(SYSDATE-6), 520.5, 360.8, 490.4, 350.6);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_004', '7', TRUNC(SYSDATE-6), 320.6, 260.4, 290.5, 230.8);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_005', '7', TRUNC(SYSDATE-6), 190.8, 140.5, 170.4, 120.8);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_006', '7', TRUNC(SYSDATE-6), 110.5, 80.6, 100.8, 70.4);

-- SYSDATE-7 数据
INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_001', '7', TRUNC(SYSDATE-7), 990.8, 770.6, 930.5, 700.8);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_002', '7', TRUNC(SYSDATE-7), 730.6, 550.4, 670.8, 490.6);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_003', '7', TRUNC(SYSDATE-7), 490.6, 330.5, 460.8, 320.4);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_004', '7', TRUNC(SYSDATE-7), 300.4, 240.6, 270.8, 210.5);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_005', '7', TRUNC(SYSDATE-7), 170.6, 120.4, 150.8, 100.6);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_006', '7', TRUNC(SYSDATE-7), 90.4, 60.8, 80.6, 50.4);

-- ============================================================================
-- 第二步：添加更多计算对象以丰富表格显示
-- 使用与 total.sql 相同的ID格式，保持一致性
-- ============================================================================

PROMPT '正在添加新的计算对象...';

-- 添加更多计算对象（继续使用 CALC_OBJ_xxx 格式）
-- 使用 MERGE 语句确保幂等性
MERGE INTO MDM_ASSET_CALC_OBJ t1
USING (SELECT 'CALC_OBJ_007' as id, 'Substation A' as name FROM dual) t2
ON (t1.ID = t2.id)
WHEN NOT MATCHED THEN
INSERT (ID, NAME, ENTITY_TYPE, ENTITY_ID, TYPE, ORG_ID)
VALUES ('CALC_OBJ_007', 'Substation A', 5, '89344c8b02004e5692baa58a7bfabab7', 2, '89344c8b02004e5692baa58a7bfabab7');

MERGE INTO MDM_ASSET_CALC_OBJ t1
USING (SELECT 'CALC_OBJ_008' as id, 'Substation B' as name FROM dual) t2
ON (t1.ID = t2.id)
WHEN NOT MATCHED THEN
INSERT (ID, NAME, ENTITY_TYPE, ENTITY_ID, TYPE, ORG_ID)
VALUES ('CALC_OBJ_008', 'Substation B', 5, '89344c8b02004e5692baa58a7bfabab7', 2, '89344c8b02004e5692baa58a7bfabab7');

MERGE INTO MDM_ASSET_CALC_OBJ t1
USING (SELECT 'CALC_OBJ_009' as id, 'Industrial Zone A' as name FROM dual) t2
ON (t1.ID = t2.id)
WHEN NOT MATCHED THEN
INSERT (ID, NAME, ENTITY_TYPE, ENTITY_ID, TYPE, ORG_ID)
VALUES ('CALC_OBJ_009', 'Industrial Zone A', 5, '89344c8b02004e5692baa58a7bfabab7', 2, '89344c8b02004e5692baa58a7bfabab7');

MERGE INTO MDM_ASSET_CALC_OBJ t1
USING (SELECT 'CALC_OBJ_010' as id, 'Residential Area B' as name FROM dual) t2
ON (t1.ID = t2.id)
WHEN NOT MATCHED THEN
INSERT (ID, NAME, ENTITY_TYPE, ENTITY_ID, TYPE, ORG_ID)
VALUES ('CALC_OBJ_010', 'Residential Area B', 5, '89344c8b02004e5692baa58a7bfabab7', 2, '89344c8b02004e5692baa58a7bfabab7');

-- ============================================================================
-- 第四步：为新增计算对象添加完整的能源数据
-- ============================================================================

PROMPT '正在为新增计算对象添加能源数据...';

-- 为新增计算对象添加昨天的数据（用于表格显示）
INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_007', '7', TRUNC(SYSDATE-1), 850.6, 680.4, 790.2, 620.8);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_008', '7', TRUNC(SYSDATE-1), 720.8, 580.6, 670.4, 520.2);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_009', '7', TRUNC(SYSDATE-1), 1150.4, 920.6, 1080.2, 850.8);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_010', '7', TRUNC(SYSDATE-1), 480.2, 380.8, 440.6, 340.4);

-- 为新增计算对象添加历史数据（用于图表显示）
-- SYSDATE-2 到 SYSDATE-7 的数据
INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_007', '7', TRUNC(SYSDATE-2), 820.4, 650.8, 760.6, 590.2);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_008', '7', TRUNC(SYSDATE-2), 690.6, 550.4, 640.8, 490.6);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_009', '7', TRUNC(SYSDATE-2), 1120.8, 890.4, 1050.6, 820.2);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_010', '7', TRUNC(SYSDATE-2), 450.8, 350.6, 410.4, 310.2);

-- SYSDATE-3
INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_007', '7', TRUNC(SYSDATE-3), 790.2, 620.6, 730.8, 560.4);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_008', '7', TRUNC(SYSDATE-3), 660.4, 520.8, 610.6, 460.2);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_009', '7', TRUNC(SYSDATE-3), 1090.6, 860.2, 1020.4, 790.8);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_010', '7', TRUNC(SYSDATE-3), 420.6, 320.4, 380.8, 280.6);

-- SYSDATE-4
INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_007', '7', TRUNC(SYSDATE-4), 760.8, 590.4, 700.6, 530.8);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_008', '7', TRUNC(SYSDATE-4), 630.8, 490.6, 580.4, 430.8);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_009', '7', TRUNC(SYSDATE-4), 1060.4, 830.6, 990.8, 760.4);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_010', '7', TRUNC(SYSDATE-4), 390.4, 290.8, 350.6, 250.4);

-- SYSDATE-5
INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_007', '7', TRUNC(SYSDATE-5), 730.6, 560.8, 670.4, 500.6);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_008', '7', TRUNC(SYSDATE-5), 600.6, 460.4, 550.8, 400.6);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_009', '7', TRUNC(SYSDATE-5), 1030.8, 800.4, 960.6, 730.8);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_010', '7', TRUNC(SYSDATE-5), 360.8, 260.6, 320.4, 220.8);

-- SYSDATE-6
INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_007', '7', TRUNC(SYSDATE-6), 700.4, 530.6, 640.8, 470.4);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_008', '7', TRUNC(SYSDATE-6), 570.4, 430.8, 520.6, 370.4);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_009', '7', TRUNC(SYSDATE-6), 1000.6, 770.8, 930.4, 700.6);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_010', '7', TRUNC(SYSDATE-6), 330.6, 230.4, 290.8, 190.6);

-- SYSDATE-7
INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_007', '7', TRUNC(SYSDATE-7), 670.8, 500.4, 610.6, 440.8);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_008', '7', TRUNC(SYSDATE-7), 540.6, 400.8, 490.4, 340.6);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_009', '7', TRUNC(SYSDATE-7), 970.4, 740.6, 900.8, 670.4);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_010', '7', TRUNC(SYSDATE-7), 300.4, 200.8, 260.6, 160.4);

-- ============================================================================
-- 第五步：提交事务并验证数据完整性
-- ============================================================================

PROMPT '正在提交事务...';
COMMIT;

PROMPT '事务提交成功，开始验证数据完整性...';

-- ============================================================================
-- 验证数据完整性
-- ============================================================================

PROMPT '正在验证 Summary Object Statistics 数据...';

-- 验证表格数据（昨天的数据）
DECLARE
    data_count NUMBER;
BEGIN
    SELECT COUNT(*)
    INTO data_count
    FROM MDM_DATA_CALC_OBJ_ENERGY t
    INNER JOIN MDM_ASSET_CALC_OBJ t1 ON t.calc_obj_id = t1.id AND t1.ENTITY_TYPE = 5
    WHERE t.scheme_id = '7'
    AND TO_CHAR(t.TV,'yyyy-mm-dd') = TO_CHAR(SYSDATE-1,'yyyy-mm-dd')
    AND t1.entity_id = '89344c8b02004e5692baa58a7bfabab7';

    DBMS_OUTPUT.PUT_LINE('Summary Object Statistics 表格数据条数: ' || data_count);

    IF data_count > 0 THEN
        DBMS_OUTPUT.PUT_LINE('表格数据示例:');
        FOR rec IN (
            SELECT t.R0P1, t1.NAME as calcObjName, t.TV
            FROM MDM_DATA_CALC_OBJ_ENERGY t
            INNER JOIN MDM_ASSET_CALC_OBJ t1 ON t.calc_obj_id = t1.id AND t1.ENTITY_TYPE = 5
            WHERE t.scheme_id = '7'
            AND TO_CHAR(t.TV,'yyyy-mm-dd') = TO_CHAR(SYSDATE-1,'yyyy-mm-dd')
            AND t1.entity_id = '89344c8b02004e5692baa58a7bfabab7'
            ORDER BY t.R0P1 DESC
            FETCH FIRST 5 ROWS ONLY
        ) LOOP
            DBMS_OUTPUT.PUT_LINE('  ' || rec.calcObjName || ': ' || rec.R0P1 || ' kWh');
        END LOOP;
    END IF;
END;
/

-- 验证图表数据（最近7天的数据）
DECLARE
    data_count NUMBER;
BEGIN
    SELECT COUNT(*)
    INTO data_count
    FROM MDM_DATA_CALC_OBJ_ENERGY t
    INNER JOIN MDM_ASSET_CALC_OBJ t1 ON t.calc_obj_id = t1.id AND t1.ENTITY_TYPE = 5
    WHERE t.scheme_id = '7'
    AND t.TV >= TRUNC(SYSDATE-7)
    AND t.TV <= TRUNC(SYSDATE-1)
    AND t1.entity_id = '89344c8b02004e5692baa58a7bfabab7';

    DBMS_OUTPUT.PUT_LINE('Summary Object Statistics 图表数据条数: ' || data_count);

    -- 按日期统计数据分布
    DBMS_OUTPUT.PUT_LINE('按日期统计:');
    FOR rec IN (
        SELECT TO_CHAR(t.TV,'yyyy-mm-dd') as date_str, COUNT(*) as cnt
        FROM MDM_DATA_CALC_OBJ_ENERGY t
        INNER JOIN MDM_ASSET_CALC_OBJ t1 ON t.calc_obj_id = t1.id AND t1.ENTITY_TYPE = 5
        WHERE t.scheme_id = '7'
        AND t.TV >= TRUNC(SYSDATE-7)
        AND t.TV <= TRUNC(SYSDATE-1)
        AND t1.entity_id = '89344c8b02004e5692baa58a7bfabab7'
        GROUP BY TO_CHAR(t.TV,'yyyy-mm-dd')
        ORDER BY TO_CHAR(t.TV,'yyyy-mm-dd')
    ) LOOP
        DBMS_OUTPUT.PUT_LINE('  ' || rec.date_str || ': ' || rec.cnt || ' 条记录');
    END LOOP;
END;
/

-- 最终状态检查
DECLARE
    total_calc_objs NUMBER;
    total_energy_data NUMBER;
    date_coverage NUMBER;
BEGIN
    -- 检查计算对象总数
    SELECT COUNT(*) INTO total_calc_objs
    FROM MDM_ASSET_CALC_OBJ
    WHERE ID LIKE 'CALC_OBJ_%'
    AND ORG_ID = '89344c8b02004e5692baa58a7bfabab7';

    -- 检查能源数据总数
    SELECT COUNT(*) INTO total_energy_data
    FROM MDM_DATA_CALC_OBJ_ENERGY t
    INNER JOIN MDM_ASSET_CALC_OBJ t1 ON t.calc_obj_id = t1.id AND t1.ENTITY_TYPE = 5
    WHERE t.scheme_id = '7'
    AND t1.entity_id = '89344c8b02004e5692baa58a7bfabab7';

    -- 检查日期覆盖范围
    SELECT COUNT(DISTINCT TO_CHAR(t.TV,'yyyy-mm-dd')) INTO date_coverage
    FROM MDM_DATA_CALC_OBJ_ENERGY t
    INNER JOIN MDM_ASSET_CALC_OBJ t1 ON t.calc_obj_id = t1.id AND t1.ENTITY_TYPE = 5
    WHERE t.scheme_id = '7'
    AND t.TV >= TRUNC(SYSDATE-7)
    AND t.TV <= TRUNC(SYSDATE-1)
    AND t1.entity_id = '89344c8b02004e5692baa58a7bfabab7';

    DBMS_OUTPUT.PUT_LINE('============================================================================');
    DBMS_OUTPUT.PUT_LINE('Summary Object Statistics 缺失数据补充完成！');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('执行结果统计:');
    DBMS_OUTPUT.PUT_LINE('- 计算对象总数: ' || total_calc_objs || ' 个');
    DBMS_OUTPUT.PUT_LINE('- 能源数据总数: ' || total_energy_data || ' 条');
    DBMS_OUTPUT.PUT_LINE('- 日期覆盖范围: ' || date_coverage || ' 天');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('补充内容:');
    DBMS_OUTPUT.PUT_LINE('1. 完整的最近7天历史数据 (SYSDATE-3 到 SYSDATE-7)');
    DBMS_OUTPUT.PUT_LINE('2. 新增4个计算对象以丰富表格显示');
    DBMS_OUTPUT.PUT_LINE('3. 为所有计算对象提供完整的时间序列数据');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('现在 Summary Object Statistics 应该能够正常显示:');
    DBMS_OUTPUT.PUT_LINE('- 表格: 显示昨天所有计算对象的能耗数据');
    DBMS_OUTPUT.PUT_LINE('- 图表: 显示最近7天的能耗趋势');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('目标ORG_ID: 89344c8b02004e5692baa58a7bfabab7');
    DBMS_OUTPUT.PUT_LINE('SCHEME_ID: 7');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('脚本执行成功！可以安全地多次执行此脚本。');
    DBMS_OUTPUT.PUT_LINE('============================================================================');
END;
/

-- 重置错误处理
WHENEVER SQLERROR CONTINUE;
