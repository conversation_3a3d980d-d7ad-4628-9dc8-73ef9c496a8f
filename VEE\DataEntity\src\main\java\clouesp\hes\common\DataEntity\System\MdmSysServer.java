package clouesp.hes.common.DataEntity.System;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name= "mdm_sys_server")
public class MdmSysServer {
	@Id
	@Column(name = "id", nullable = false, columnDefinition = "varchar(32)")
	private String id;
	@Column(name = "ip", columnDefinition = "varchar(32)")
	private String ip;
	@Column(name = "ha_state")
	private Integer haState;
	@Column(name = "is_online")
	private Integer isOnline;
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getIp() {
		return ip;
	}
	public void setIp(String ip) {
		this.ip = ip;
	}
	public Integer getHaState() {
		return haState;
	}
	public void setHaState(Integer haState) {
		this.haState = haState;
	}
	public Integer getIsOnline() {
		return isOnline;
	}
	public void setIsOnline(Integer isOnline) {
		this.isOnline = isOnline;
	}
}
