import clouesp.hes.common.CommonUtils.ObjectUtils
import clouesp.hes.common.DataEntity.Data.DataReg
import clouesp.hes.common.DataEntity.Data.DataVEEEvent

def dialRollover(Map<String, Object> ruleInfo) {
    Integer result = 0;
    DataVEEEvent event = null;
    if (ruleInfo == null ||
            !ruleInfo.containsKey("datas") ||
            !ruleInfo.containsKey("params")) {
        return null;
    }

    List<Map<String, Object>> ruleDatas = ruleInfo.get("datas");

    for(Map<String, Object> ruleData : ruleDatas) {
        String targetClass = ruleData.get("targetClass");
        List<Map<String, Object>> dataList = ruleData.get("dataListDay");
        if(dataList == null) {
            return null;
        }
        if("REG" == targetClass) {
            if(dataList.size() > 1) {
                boolean isRollover = false;
                Double lastR0p1 = null;
                for(Map<String, Object> data : dataList) {
                    DataReg dataReg = ObjectUtils.convertMapToObject(data, DataReg.class);
                    Double r0p1 = dataReg.getR0P1();
                    if(r0p1 == null) {
                        continue;
                    }
                    if(lastR0p1 == null) {
                        lastR0p1 = r0p1;
                        continue;
                    }
                    if(r0p1 < lastR0p1) {
                        isRollover = true;
                        break;
                    }
                }
                if(isRollover) {
                    result = 1;
                }
            }
        }
    }

    if(result.intValue() == 1) {
        event = (DataVEEEvent) ruleInfo.get("event");
    }
    return event;
}