package clouesp.hes.tools.datagenerator.controller;


import clouesp.hes.tools.datagenerator.service.VeeDataGeneratorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@Api(tags={"vee数据生成控制器"})
@RestController
@RequestMapping("veeDataGenerator")
public class VeeDataGeneratorController {

    @Autowired
    private VeeDataGeneratorService veeDataGeneratorService;

    /**
     * 生成vee校验测试数据
     * @param veeGroupName vee组名称(默认组名称唯一)
     * @param eventId 事件id
     * @param startTv 开始生成时间
     * @param errCount 生成校验
     * @return
     */
    @ApiOperation(value = "生成vee数据,当线损时，多个计算对象所用到的sdp不要有交集（建议一个线损校验组用于一个计算对象），构造每个计算对象数据时，会先删除当前计算对象的值，产生交集会删掉之前构造号好的值",  notes = "", produces = "application/json")
    @RequestMapping(value = "/generateVeeEventData", method = RequestMethod.POST)
    public String GenerateVeeEventData(String veeGroupName, String eventId, String startTv, int errCount)
    {
        return veeDataGeneratorService.GenerateVeeEventData(veeGroupName,eventId,startTv,errCount);

    }
}
