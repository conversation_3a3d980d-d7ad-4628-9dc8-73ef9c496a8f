package clouesp.hes.common.DataEntity.Data;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Embeddable;

@Embeddable
public class DataRegPK implements Serializable{
	private static final long serialVersionUID = 4766766715568434419L;
	@Column(name = "sdp_id", nullable = false, columnDefinition = "varchar(32)")
	private String sdpId;
	@Column(name = "tv")
	private Date tv;
	public String getSdpId() {
		return sdpId;
	}
	public void setSdpId(String sdpId) {
		this.sdpId = sdpId;
	}
	public Date getTv() {
		return tv;
	}
	public void setTv(Date tv) {
		this.tv = tv;
	}
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((sdpId == null) ? 0 : sdpId.hashCode());
		result = prime * result
				+ ((tv == null) ? 0 : tv.hashCode());
		return result;
	}
	
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		DataRegPK other = (DataRegPK) obj;
		if (sdpId == null) {
			if (other.sdpId != null)
				return false;
		} else if (!sdpId.equals(other.sdpId))
			return false;
		if (tv == null) {
			if (other.tv != null)
				return false;
		} else if (!tv.equals(other.tv))
			return false;
		return true;
	}					
}
