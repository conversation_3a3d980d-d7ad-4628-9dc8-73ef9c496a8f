<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="StatisticsAccessMapper">
    <insert id="batchSaveMdmStatisticsObj" parameterType="java.util.List" databaseId="oracle">
        merge into mdm_Data_Statistics T1 USING (
        <foreach collection="list" item="statisObj" index="index" separator="UNION">
            select
            #{statisObj.mdmStatisticsObjPK.orgId, jdbcType=VARCHAR} org_id,
            #{statisObj.mdmStatisticsObjPK.id, jdbcType=VARCHAR} id,
            #{statisObj.mdmStatisticsObjPK.idType, jdbcType=VARCHAR} id_type,
            #{statisObj.mdmStatisticsObjPK.tv, jdbcType=TIMESTAMP} tv,

            #{statisObj.countCurrent, jdbcType=NUMERIC} count_current,
            #{statisObj.percent, jdbcType=NUMERIC} percent

            from dual
        </foreach>
        ) T2 ON (
        T1.org_id = T2.org_id
        and T1.id = T2.id
        and T1.id_type = T2.id_type
        and T1.tv = T2.tv
        )
        when not matched then
        insert (
        org_id,
        id,
        id_type,
        tv,
        count_current,
        percent
        )
        values (
        T2.org_id,
        T2.id,
        T2.id_type,
        T2.tv,
        T2.count_current,
        T2.percent

        )
        when matched then
        update
        set
        T1.count_current = T2.count_current,
        T1.percent = T2.percent
    </insert>

    <insert id="batchSaveMdmStatisticsObj" parameterType="java.util.List" databaseId="mysql">
        replace into mdm_data_statistics(
        org_id,
        id,
        id_type,
        tv,
        count_current,
        percent
        )
        values
        <foreach collection="list" item="statisObj" index="index" separator=",">
            (
            #{statisObj.mdmStatisticsObjPK.orgId, jdbcType=VARCHAR},
            #{statisObj.mdmStatisticsObjPK.id, jdbcType=VARCHAR},
            #{statisObj.mdmStatisticsObjPK.idType, jdbcType=VARCHAR},
            #{statisObj.mdmStatisticsObjPK.tv, jdbcType=TIMESTAMP},

            #{statisObj.countCurrent, jdbcType=NUMERIC},
            #{statisObj.percent, jdbcType=NUMERIC}
            )
        </foreach>
    </insert>

    <insert id="batchSaveOutageStatisticsObj" parameterType="java.util.List" databaseId="oracle">
        merge into MDM_DATA_OUTAGE_STATISTICS T1 USING (
        <foreach collection="list" item="statisOutageObj" index="index" separator="UNION">
            select
            #{statisOutageObj.mdmDataOutageStatisticsPK.orgId, jdbcType=VARCHAR} org_id,
            #{statisOutageObj.mdmDataOutageStatisticsPK.lineId, jdbcType=VARCHAR} line_id,
            #{statisOutageObj.mdmDataOutageStatisticsPK.sdpType, jdbcType=NUMERIC} sdp_type,
            #{statisOutageObj.mdmDataOutageStatisticsPK.timeType, jdbcType=NUMERIC} time_type,
            #{statisOutageObj.mdmDataOutageStatisticsPK.tv, jdbcType=TIMESTAMP} tv,
            #{statisOutageObj.sdpTotalNum, jdbcType=NUMERIC} sdp_total_num,
            #{statisOutageObj.totalTime, jdbcType=NUMERIC} total_time ,
            #{statisOutageObj.totalNum, jdbcType=NUMERIC} total_num,
            #{statisOutageObj.avgTime, jdbcType=NUMERIC} avg_time ,
            #{statisOutageObj.avgNum, jdbcType=NUMERIC} avg_num
            from dual
        </foreach>
        ) T2 ON (
        T1.org_id = T2.org_id
        and T1.line_id = T2.line_id
        and T1.sdp_type = T2.sdp_type
        and T1.time_type = T2.time_type
        and T1.tv = T2.tv
        )
        when not matched then
        insert (
        org_id,
        line_id,
        sdp_type,
        time_type,
        tv,
        sdp_total_num,
        total_time,
        total_num,
        avg_time ,
        avg_num
        )
        values (
        T2.org_id,
        T2.line_id,
        T2.sdp_type,
        T2.time_type,
        T2.tv,
        T2.sdp_total_num,
        T2.total_time,
        T2.total_num,
        T2.avg_time ,
        T2.avg_num

        )
        when matched then
        update
        set
        T1.sdp_total_num = T2.sdp_total_num,
        T1.total_time = T2.total_time,
        T1.total_num = T2.total_num,
        T1.avg_time = T2.avg_time,
        T1.avg_num = T2.avg_num
    </insert>

    <insert id="batchSaveOutageStatisticsObj" parameterType="java.util.List" databaseId="mysql">
        replace into MDM_DATA_OUTAGE_STATISTICS (
        org_id,
        line_id,
        sdp_type,
        time_type,
        tv,
        sdp_total_num,
        total_time,
        total_num,
        avg_time ,
        avg_num
        )
        values
        <foreach collection="list" item="statisOutageObj" index="index" separator=",">
            (
            #{statisOutageObj.mdmDataOutageStatisticsPK.orgId, jdbcType=VARCHAR},
            #{statisOutageObj.mdmDataOutageStatisticsPK.lineId, jdbcType=VARCHAR},
            #{statisOutageObj.mdmDataOutageStatisticsPK.sdpType, jdbcType=NUMERIC},
            #{statisOutageObj.mdmDataOutageStatisticsPK.timeType, jdbcType=NUMERIC},
            #{statisOutageObj.mdmDataOutageStatisticsPK.tv, jdbcType=TIMESTAMP},
            #{statisOutageObj.sdpTotalNum, jdbcType=NUMERIC},
            #{statisOutageObj.totalTime, jdbcType=NUMERIC},
            #{statisOutageObj.totalNum, jdbcType=NUMERIC},
            #{statisOutageObj.avgTime, jdbcType=NUMERIC},
            #{statisOutageObj.avgNum, jdbcType=NUMERIC}
            )
        </foreach>

    </insert>

    <delete id="deleteAllProgessStatistics">
        delete from mdm_data_statistics where id = '1145020'
    </delete>


    <select id="findCurMonthOutageStatics" resultType="clouesp.hes.common.DataMode.Statistics.OutageEvtStatics"
            databaseId="oracle">
        select  m.org_id ,m.line_id , n.sdp_total_num ,m.total_time , m.total_num ,m.total_time / n.sdp_total_num as avg_time , m.total_num/n.sdp_total_num as avg_num from
         ( select  c.org_id , b.parent_id as line_id ,  sum(duration_time) as total_time , count(*) as total_num     from mdm_data_vee_event  a , ASSET_ENTITY_RELATIONSHIP b , ASSET_LINE c
         where a.event_id = '1007003'  and a.OBJECT_TYPE = 7 and b.type = 7 and b.parent_type = 3 and  a.end_tv <![CDATA[>=]]> trunc(sysdate ,'mm')
         and   a.OBJECT_ID = b.id  and c.id = b.parent_id   group by  c.org_id , b.parent_id  ) m , ( select  org_id ,  count(*) as sdp_total_num  from MDM_ASSET_SERVICE_POINT group by  org_id ) n where n.org_id = m.org_id
    </select>


    <select id="findCurMonthOutageStatics" resultType="clouesp.hes.common.DataMode.Statistics.OutageEvtStatics"
            databaseId="mysql">
        select  m.org_id ,m.line_id , n.sdp_total_num ,m.total_time , m.total_num ,m.total_time / n.sdp_total_num as avg_time , m.total_num/n.sdp_total_num as avg_num from
   ( select  c.org_id , b.parent_id as line_id ,  sum(duration_time) as total_time , count(*) as total_num     from mdm_data_vee_event  a , ASSET_ENTITY_RELATIONSHIP b , ASSET_LINE c
     where a.event_id = '1007003'  and a.OBJECT_TYPE = 7 and b.type = 7 and b.parent_type = 3 and  a.end_tv <![CDATA[>=]]> DATE(date_add(curdate(), interval - day(curdate()) + 1 day))
       and   a.OBJECT_ID = b.id  and c.id = b.parent_id   group by  c.org_id , b.parent_id  ) m , ( select  org_id ,  count(*) as sdp_total_num  from MDM_ASSET_SERVICE_POINT group by  org_id ) n
        where n.org_id = m.org_id
    </select>


    <select id="findYesterdayOutageStatics" resultType="clouesp.hes.common.DataMode.Statistics.OutageEvtStatics"
            databaseId="oracle">
        select  m.org_id ,m.line_id , n.sdp_total_num ,m.total_time , m.total_num ,m.total_time / n.sdp_total_num as avg_time , m.total_num/n.sdp_total_num as avg_num from
        ( select  c.org_id , b.parent_id as line_id ,  sum(duration_time) as total_time , count(*) as total_num     from mdm_data_vee_event  a , ASSET_ENTITY_RELATIONSHIP b , ASSET_LINE c
        where a.event_id = '1007003'  and a.OBJECT_TYPE = 7 and b.type = 7 and b.parent_type = 3 and  a.end_tv <![CDATA[>=]]> trunc(sysdate - 1 )  and a.end_tv <![CDATA[<]]> trunc(sysdate) and a.OBJECT_ID = b.id  and c.id = b.parent_id
        group by  c.org_id , b.parent_id  ) m , (	 select  org_id ,  count(*) as sdp_total_num  from MDM_ASSET_SERVICE_POINT group by  org_id ) n where n.org_id = m.org_id
    </select>


    <select id="findYesterdayOutageStatics" resultType="clouesp.hes.common.DataMode.Statistics.OutageEvtStatics"
            databaseId="mysql">
        select  m.org_id ,m.line_id , n.sdp_total_num ,m.total_time , m.total_num ,m.total_time / n.sdp_total_num as avg_time , m.total_num/n.sdp_total_num as avg_num from
   ( select  c.org_id , b.parent_id as line_id ,  sum(duration_time) as total_time , count(*) as total_num     from mdm_data_vee_event  a , ASSET_ENTITY_RELATIONSHIP b , ASSET_LINE c
     where a.event_id = '1007003'  and a.OBJECT_TYPE = 7 and b.type = 7 and b.parent_type = 3 and  a.end_tv <![CDATA[>=]]> DATE(NOW() - INTERVAL 1 DAY)   and a.end_tv <![CDATA[<]]> DATE(NOW())
       and a.OBJECT_ID = b.id  and c.id = b.parent_id
     group by  c.org_id , b.parent_id  ) m , (	 select  org_id ,  count(*) as sdp_total_num  from MDM_ASSET_SERVICE_POINT group by  org_id ) n where n.org_id = m.org_id
    </select>


</mapper>