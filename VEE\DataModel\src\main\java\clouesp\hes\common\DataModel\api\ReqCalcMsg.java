package clouesp.hes.common.DataModel.api;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

@ApiModel(value="reqCalcMsg",description="需计算的对象信息")
public class ReqCalcMsg {
	@ApiModelProperty(value="计算对象ID集", position=1)
	private List<String> ids;

	@ApiModelProperty(value="结算方案编码(1: 1 Minute; 2: 5 Minute; 8: Default Month; 7: Default Day; 6: 60 Minute; 5: 30 Minute;4: 15 Minute)", position=2)
	private String schemeId;

	@ApiModelProperty(value="开始时间", position=3)
	private Date startTv;
	@ApiModelProperty(value="结束时间", position=4)
	private Date endTv;

	public String getSchemeId() {return schemeId;	}
	public void setSchemeId(String schemeId) {this.schemeId = schemeId;	}
	public List<String> getIds() {
		return ids;
	}
	public void setIds(List<String> ids) {
		this.ids = ids;
	}
	public Date getStartTv() {
		return startTv;
	}
	public void setStartTv(Date startTv) {
		this.startTv = startTv;
	}
	public Date getEndTv() {
		return endTv;
	}
	public void setEndTv(Date endTv) {
		this.endTv = endTv;
	}	
}
