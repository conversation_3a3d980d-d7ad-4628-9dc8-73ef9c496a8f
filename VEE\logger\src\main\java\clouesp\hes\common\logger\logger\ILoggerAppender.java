/*
 * 文件名：ILoggerAppender.java 版权：Copyright by Power7000 Team 描述： 修改人：jybai 修改时间：2017年10月19日 跟踪单号：
 * 修改单号： 修改内容：
 */

package clouesp.hes.common.logger.logger;

interface ILoggerAppender {
    public void start();

    public void stop();

    public void setLevel(LoggerLevel level);
 
    public void writeLogInfo(LoggerLevel level, String serviceId, String deviceId,
                             String infoType, String info);
    
    public void appendLogger();
    public void directAppendLogger();
}
