package clouesp.hes.common.DataRepository.Persistence.Asset;

import org.springframework.data.jpa.repository.JpaRepository;

import clouesp.hes.common.DataEntity.Asset.MdmAssetServicePoint;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface MdmAssetServicePointRepository extends JpaRepository<MdmAssetServicePoint, String>{

    List<MdmAssetServicePoint> findAllByVeeValidationGroupId(String veeCalculationGroupId);

    @Modifying
    @Transactional
    @Query(value = "delete from MDM_DATA_REG_DAYLY where SDP_ID= :sdpId and TV >= :startTv",nativeQuery = true)
     void deleteMdmDataRegDayly(
            @Param("sdpId") String sdpId,
            @Param("startTv") Date startTv);

    @Modifying
    @Transactional
    @Query(value = "delete from MDM_DATA_REG_MONTHLY where SDP_ID= :sdpId and TV >= :startTv",nativeQuery = true)
     void deleteMdmDataRegMonthly(
            @Param("sdpId") String sdpId,
            @Param("startTv") Date startTv);

    @Modifying
    @Transactional
    @Query(value = "delete from MDM_DATA_REG_MINUTELY where SDP_ID= :sdpId and TV >= :startTv",nativeQuery = true)
     void deleteMdmDataRegMinutely(
            @Param("sdpId") String sdpId,
            @Param("startTv") Date startTv);


    @Query(value = "select * from MDM_ASSET_SERVICE_POINT where VEE_VALIDATION_GROUP_ID in :validGroupIds and id not in(select object_id from MDM_DATA_VEE_EVENT where tv= to_date(:dataTv,'yyyy-MM-dd') and event_id=:eventId and scheme_type=2)"
            , nativeQuery = true)
    List<MdmAssetServicePoint> findSdpListByValidGroupIds(
            @Param("validGroupIds") Collection<String> validGroupIds,
            @Param("dataTv")String dataTv,
            @Param("eventId")String eventId);

}
