package clouesp.hes.common.DataEntity.System;

import java.io.Serializable;

import javax.persistence.Column;

public class MdmSysServiceAttributePK implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 7966303834197220714L;
	@Column(name = "id", nullable = false, columnDefinition = "varchar(32)")
	private String id;
	
	@Column(name = "attribute_name", columnDefinition = "varchar(64)")
	private String attributeName;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getAttributeName() {
		return attributeName;
	}

	public void setAttributeName(String attributeName) {
		this.attributeName = attributeName;
	}
	
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		result = prime * result
				+ ((attributeName == null) ? 0 : attributeName.hashCode());
		return result;
	}
	
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		MdmSysServiceAttributePK other = (MdmSysServiceAttributePK) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		if (attributeName == null) {
			if (other.attributeName != null)
				return false;
		} else if (!attributeName.equals(other.attributeName))
			return false;		
		return true;
	}					
}
