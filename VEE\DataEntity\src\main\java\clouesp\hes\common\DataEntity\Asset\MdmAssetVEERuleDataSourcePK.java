package clouesp.hes.common.DataEntity.Asset;

import java.io.Serializable;

public class MdmAssetVEERuleDataSourcePK implements Serializable{
	private static final long serialVersionUID = 2541874808119585087L;
	private String ruleId;
	private Integer dataType;
	private Integer schemeType;
	private String sourceEventType;	
	
	public String getRuleId() {
		return ruleId;
	}
	public void setRuleId(String ruleId) {
		this.ruleId = ruleId;
	}

	public Integer getDataType() {
		return dataType;
	}
	public void setDataType(Integer dataType) {
		this.dataType = dataType;
	}
	public Integer getSchemeType() {
		return schemeType;
	}
	public void setSchemeType(Integer schemeType) {
		this.schemeType = schemeType;
	}
	public String getSourceEventType() {
		return sourceEventType;
	}
	public void setSourceEventType(String sourceEventType) {
		this.sourceEventType = sourceEventType;
	}
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((ruleId == null) ? 0 : ruleId.hashCode());
		result = prime * result
				+ ((dataType == null) ? 0 : dataType.hashCode());
		result = prime * result
				+ ((schemeType == null) ? 0 : schemeType.hashCode());
		result = prime * result
				+ ((sourceEventType == null) ? 0 : sourceEventType.hashCode());
		return result;
	}
	
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		MdmAssetVEERuleDataSourcePK other = (MdmAssetVEERuleDataSourcePK) obj;
		if (ruleId == null) {
			if (other.ruleId != null)
				return false;
		} else if (!ruleId.equals(other.ruleId))
			return false;
		if (dataType == null) {
			if (other.dataType != null)
				return false;
		} else if (!dataType.equals(other.dataType))
			return false;
		if (schemeType == null) {
			if (other.schemeType != null)
				return false;
		} else if (!schemeType.equals(other.schemeType))
			return false;
		if (sourceEventType == null) {
			if (other.sourceEventType != null)
				return false;
		} else if (!sourceEventType.equals(other.sourceEventType))
			return false;		
		return true;
	}					
}
