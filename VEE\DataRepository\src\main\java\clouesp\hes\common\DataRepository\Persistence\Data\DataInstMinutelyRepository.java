package clouesp.hes.common.DataRepository.Persistence.Data;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import clouesp.hes.common.DataEntity.Data.DataInstMinutely;
import clouesp.hes.common.DataEntity.Data.DataInstPK;

public interface DataInstMinutelyRepository extends JpaRepository<DataInstMinutely, DataInstPK>{
	List<DataInstMinutely> findByDataInstPKSdpIdAndDataInstPKDataTypeAndDataInstPKTvIn(
			String sdpId, 
			int dataType,
			List<Date> tvs
			);
}
