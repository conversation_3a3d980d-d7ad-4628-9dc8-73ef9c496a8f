package clouesp.hes.common.DataRepository.RealTime.Asset;

import org.springframework.data.jpa.repository.JpaRepository;

import clouesp.hes.common.DataEntity.Asset.MdmAssetServicePoint;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface RtMdmAssetServicePointRepository extends JpaRepository<MdmAssetServicePoint, String>{
	MdmAssetServicePoint findByMeterId(String MeterId);

	@Query(value = "select * from MDM_ASSET_SERVICE_POINT where VEE_VALIDATION_GROUP_ID in :validGroupIds and id not in(select object_id from MDM_DATA_VEE_EVENT where tv=:dataTv and event_id=:eventId and scheme_type=2)"
			, nativeQuery = true)
	List<MdmAssetServicePoint> findSdpListByValidGroupIds(
			@Param("validGroupIds")Collection<String> validGroupIds,
			@Param("dataTv")Date dataTv,
			@Param("eventId")String eventId);


}
