<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="AssetScheduleSchemeMapper">

    <select id="getScheduleSchemeList" resultMap="AssetScheduleSchemeMap"  parameterType="PagingRequest">
        select
        *
        from
        asset_schedule_scheme
        where
        1 = 1
        <if test="entity">
            <if test="entity.id != null and entity.id != ''">
                and id = #{entity.id}
            </if>
            <if test="entity.name != null and entity.name != ''">
                and name like concat(concat('%', #{entity.name}),'%')
            </if>
            <if test="entity.protocolId != null and entity.protocolId != ''">
                and protocol_id = #{entity.protocolId}
            </if>


        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <resultMap type="AssetScheduleScheme" id="AssetScheduleSchemeMap">
        <id column="id" property="id" />
        <association property="protocol" column="protocol_id"
                     javaType="DictProtocol" select="getProtocol">
        </association>
        <association property="meterCount" column="id"
                     javaType="java.lang.Integer" select="getMeterCount">
        </association>
    </resultMap>

    <select id="getProtocol" parameterType="String" resultType="DictProtocol">
        select * from dict_protocol where id=#{id}
    </select>

    <select id="getMeterCount" parameterType="String" resultType="java.lang.Integer">
        select count(*) from asset_meter_group_map where type = 0 and group_id=#{id}
    </select>


    <select id="getScheduleSchemeDetailList" resultType="AssetScheduleSchemeDetail"  parameterType="PagingRequest">
        select
        *
        from
        asset_schedule_scheme_detail
        where
        1 = 1
        <if test="entity">
            <if test="entity.id != null and entity.id != ''">
                and id = #{entity.id}
            </if>
            <if test="entity.taskId != null and entity.taskId != ''">
                and task_Id = #{entity.taskId}
            </if>

        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>


</mapper>