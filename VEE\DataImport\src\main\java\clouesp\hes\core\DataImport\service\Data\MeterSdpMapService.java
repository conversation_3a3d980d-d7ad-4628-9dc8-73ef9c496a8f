package clouesp.hes.core.DataImport.service.Data;


import clouesp.hes.common.DataMode.Data.ModeMeterSnSdpMap;
import clouesp.hes.core.DataImport.dao.Persistence.DaoSupport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

@Slf4j
@Service("meterSdpMapService")
public class MeterSdpMapService {
    @Resource(name = "daoSupport")
    private DaoSupport dao;
    private long       lastLoadTime = 0 ;

    private  List<ModeMeterSnSdpMap>  modeMeterSnSdpMapList = new ArrayList<ModeMeterSnSdpMap>() ;

    private    Comparator<ModeMeterSnSdpMap> SnComparator = new Comparator<ModeMeterSnSdpMap>() {
        @Override
        public int compare(ModeMeterSnSdpMap p1, ModeMeterSnSdpMap p2) {
            return p1.getMeterSn().compareTo(p2.getMeterSn());
        }
    };

    public void Load()throws Exception{

        log.info("Start load meterSn and Sdp map ") ;
        lastLoadTime = System.currentTimeMillis() ;
        modeMeterSnSdpMapList =  (List<ModeMeterSnSdpMap>)(dao.findForList("DataAccessMapper.getMeterSnSdpMapList",null) );
        if ( modeMeterSnSdpMapList != null && !modeMeterSnSdpMapList.isEmpty()){
            Collections.sort(modeMeterSnSdpMapList) ;
            log.info("End load meterSn and Sdp map , Size = " + modeMeterSnSdpMapList.size());
        }else {
            log.info("End load meterSn and Sdp map , Size = 0 " ) ;
        }
    }

    public void RunTimerLoad()throws Exception{
        if ( System.currentTimeMillis() - lastLoadTime  > ( 4*60*60*1000)){
            Load();
        }
    }

    public String getSdpIdbyMeterSn(String sn){

        if ( modeMeterSnSdpMapList == null || modeMeterSnSdpMapList.isEmpty() ) return  null  ;
        ModeMeterSnSdpMap key  = new ModeMeterSnSdpMap() ;
        key.setMeterSn(sn);
        int index  = Collections.binarySearch(modeMeterSnSdpMapList,key,SnComparator) ;

        if ( index < 0 ) return  null ;

        return modeMeterSnSdpMapList.get(index).getSdpId() ;
    }

}
