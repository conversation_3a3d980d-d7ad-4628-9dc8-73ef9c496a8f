package clouesp.hes.common.DataEntity.Dict;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name= "dict_meter_data_storage_info")
public class MeterDataStorageInfo {
	@Id
	@Column(name = "id", nullable = false, columnDefinition = "varchar(64)")
	private String id;
	@Column(name = "table_id", columnDefinition = "varchar(32)")
	private String tableId;
	@Column(name = "field_index")
	private Integer fieldIndex;
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getTableId() {
		return tableId;
	}
	public void setTableId(String tableId) {
		this.tableId = tableId;
	}
	public Integer getFieldIndex() {
		return fieldIndex;
	}
	public void setFieldIndex(Integer fieldIndex) {
		this.fieldIndex = fieldIndex;
	}
	
}
