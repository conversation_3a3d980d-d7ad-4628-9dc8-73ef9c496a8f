spring:

  application:

    name: DataGenerator #服务名称
    
  profiles:
    include: common        
    
#realtime        
#  realtime:
#    datasource:
#      jdbc-url: jdbc:h2:tcp://localhost:9799/mem:realtimedb;DB_CLOSE_ON_EXIT=FALSE
#      driver-class-name: org.h2.Driver
#      username: root
#      password: 123
                       
server:
  port: 9321 #端口

knife4j:
  enable: true #开启配置增强
  setting:
    enableVersion: true
schedule:
  scanStartTime: 2020-01-01 00:57:00
  scanCycle: 1
  scanMinute: 7 #天
  scanDay: 30 #天
  scanMonth: 6 #月
  scanEvent: 1 #月
