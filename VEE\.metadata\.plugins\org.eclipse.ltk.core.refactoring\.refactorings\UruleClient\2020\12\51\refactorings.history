<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;UruleClient&apos;&#x0D;&#x0A;- Original project: &apos;UruleClient&apos;&#x0D;&#x0A;- Original element: &apos;application.properties&apos;" description="Delete element" element1="src/main/resources/application.properties" elements="0" flags="589830" id="org.eclipse.jdt.ui.delete" resources="1" stamp="1608261718364" subPackages="false" version="1.0"/>&#x0A;<refactoring comment="Move &apos;DataVerifyRuleUtils.java&apos; to &apos;service&apos;" description="Move &apos;DataVerifyRuleUtils.java&apos; to &apos;service&apos;" destination="src/main/java/clouesp/hes/core/UruleClient/service" element1="src/main/java/clouesp/hes/core/UruleClient/utils/DataVerifyRuleUtils.java" flags="7" id="org.eclipse.ltk.core.refactoring.move.resources" resources="1" stamp="1608263835812" updateReferences="true"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;UruleClient&apos;&#x0D;&#x0A;- Original project: &apos;UruleClient&apos;&#x0D;&#x0A;- Original element: &apos;com&apos;" description="Delete element" element1="src/main/java/clouesp/com" elements="0" flags="589830" id="org.eclipse.jdt.ui.delete" resources="1" stamp="1608283459679" subPackages="false" version="1.0"/>&#x0A;<refactoring comment="Move &apos;MethodLoad(1).java&apos; to &apos;urule&apos;" description="Move &apos;MethodLoad(1).java&apos; to &apos;urule&apos;" destination="src/main/java/com/clou/esp/hes/app/web/model/urule" element1="src/main/java/clouesp/hes/core/UruleClient/MethodLoad(1).java" flags="7" id="org.eclipse.ltk.core.refactoring.move.resources" resources="1" stamp="1608283564937" updateReferences="true"/>
</session>