<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="CalcAccessMapper">
	<select id="querySumValue" resultType="java.util.Map"  parameterType="java.util.HashMap">
		select sum(${colName}) as CALCVALUE, count(*) as COUNTTOTAL 
		from ${tableName} dt 
		where 1 = 1 
		and scheme_id = #{schemeId} 
		<if test="calcTv">
		 	 and tv =
		    <if test="_databaseId == 'oracle'">
               to_date(${calcTv}, 'yyyy-mm-dd hh24:mi:ss')
			</if>
			<if test="_databaseId == 'mysql'">
				${calcTv}
			</if>
		</if>		
		<if test="objectId">
		<choose>
			<when test="getAsset == 1">
				and sdp_id in (select id 
				from mdm_asset_service_point 
				where 1 = 1 
				<if test="assetColName">
					and ${assetColName} = #{assetColValue} 
				</if>
				<if test="veeCalculationGroupId">
					and vee_calculation_group_id = #{veeCalculationGroupId} 
				</if>				
				)
			</when>	    	
			<otherwise>
				and sdp_id in (select metering_id 
				from mdm_asset_calc_obj_map 
				where 
				metering_type = 1 
				and id = #{objectId} 
				and type = #{ioType} 
				and calc_formula = #{calcFormula} ) 
			</otherwise>
		 </choose>
	    </if>
	    
	</select>
	
	<select id="querySdpCount" resultType="java.lang.Integer"  parameterType="java.util.HashMap">
		<choose>
			<when test="getAsset == 1">
				select count(*) as COUNTTOTAL 
				from mdm_asset_service_point 
				where 1 = 1 
				<if test="assetColName">
					and ${assetColName} = #{assetColValue} 
				</if>
				<if test="veeCalculationGroupId">
					and vee_calculation_group_id = #{veeCalculationGroupId} 
				</if>			
			</when>
			<otherwise>
			select count(*) as COUNTTOTAL 
			from mdm_asset_calc_obj_map 
			where 1 = 1 
			and metering_type = 1 
			and id = #{objectId} 
			</otherwise>
		</choose>
	</select>
	
	 <select id="queryMissList" resultType="DataCalcObjMiss"  parameterType="java.util.HashMap">
	 <choose>
	 	<when test="getAsset == 1">
	 	  select
      		#{objectId}  as calc_obj_id,
			<if test="_databaseId == 'oracle'">
      		     to_date(${calcTv}, 'yyyy-mm-dd hh24:mi:ss') as tv,
			</if>
			<if test="_databaseId == 'mysql'">
				${calcTv} as tv,
			</if>
      		#{schemeId} as scheme_id,
      		id as sdp_id
      	from 
      		mdm_asset_service_point 
	 	where 
	 		1 = 1 
			<if test="assetColName">
				and ${assetColName} = #{assetColValue} 
			</if>
			<if test="veeCalculationGroupId">
				and vee_calculation_group_id = #{veeCalculationGroupId} 
			</if>	
			
			and id not in (select sdp_id from mdm_data_energy_dayly 
        	where scheme_id = #{schemeId}  and tv =
			<if test="_databaseId == 'oracle'">
        	     to_date(${calcTv}, 'yyyy-mm-dd hh24:mi:ss'))
			</if>
			<if test="_databaseId == 'mysql'">
				${calcTv}
			</if>
					
	 	</when>
	 	<otherwise>
        select
      		#{objectId}  as calc_obj_id,
			<if test="_databaseId == 'oracle'">
      		   to_date(${calcTv}, 'yyyy-mm-dd hh24:mi:ss') as tv,
			</if>
			<if test="_databaseId == 'mysql'">
				${calcTv} as tv,
			</if>
      		#{schemeId} as scheme_id,
      		metering_id as sdp_id
      	from 
      		mdm_asset_calc_obj_map
        where
        	metering_type = 1 
        	and id = #{objectId} 
        	and metering_id not in (select sdp_id from mdm_data_energy_dayly 
        	where scheme_id = #{schemeId}  and
			<if test="_databaseId == 'oracle'">
        	      tv = to_date(${calcTv}, 'yyyy-mm-dd hh24:mi:ss')
			</if>
			<if test="_databaseId == 'mysql'">
				${calcTv}
			</if>
        	    )
        	</otherwise>
      </choose>
    </select>

	 <insert id="batchSaveCalcObj" parameterType="java.util.List" databaseId="oracle">
	    merge into mdm_data_calc_obj T1 USING ( 
		<foreach collection="list" item="calcObj" index="index" separator="UNION">
        select 
	        #{calcObj.dataCalcObjPK.calcObjId, jdbcType=VARCHAR} calc_obj_id,
	        #{calcObj.dataCalcObjPK.tv, jdbcType=TIMESTAMP} tv,
	        #{calcObj.dataCalcObjPK.schemeId, jdbcType=VARCHAR} scheme_id,
	        #{calcObj.outTotal, jdbcType=NUMERIC} out_total,
	        #{calcObj.inTotal, jdbcType=NUMERIC} in_total,
	        #{calcObj.calcValue, jdbcType=NUMERIC} calc_value,
	        #{calcObj.missData, jdbcType=NUMERIC} miss_data,
	        #{calcObj.updateTv, jdbcType=TIMESTAMP} update_tv 
        from dual 
    	</foreach>
    	) T2 ON ( 
     		T1.calc_obj_id = T2.calc_obj_id 
    		and T1.tv = T2.tv 
    		and T1.scheme_id = T2.scheme_id   
    	) 
    	when not matched then 
    	insert ( 
	    	calc_obj_id,
	    	tv,
	    	scheme_id, 
	    	out_total,
	    	in_total,
	    	calc_value,
	    	miss_data,
	    	update_tv 
    	)  
   		values (
		    T2.calc_obj_id,
		    T2.tv,
		    T2.scheme_id,  
		    T2.out_total,
		    T2.in_total,
		    T2.calc_value,
		    T2.miss_data,
		    T2.update_tv 
	
    	) 
    	when matched then 
        update 
	    set 
	        T1.out_total = T2.out_total,
		    T1.in_total = T2.in_total,
		    T1.calc_value = T2.calc_value,
		    T1.miss_data= T2.miss_data,
		    T1.update_tv = T2.update_tv 
	</insert>

	<insert id="batchSaveCalcObj" parameterType="java.util.List" databaseId="mysql">
		replace into mdm_data_calc_obj (
		calc_obj_id,
		tv,
		scheme_id,
		out_total,
		in_total,
		calc_value,
		miss_data,
		update_tv
		)
		values
		<foreach collection="list" item="calcObj" index="index" separator=",">
		(
		#{calcObj.dataCalcObjPK.calcObjId, jdbcType=VARCHAR},
		#{calcObj.dataCalcObjPK.tv, jdbcType=TIMESTAMP},
		#{calcObj.dataCalcObjPK.schemeId, jdbcType=VARCHAR},
		#{calcObj.outTotal, jdbcType=NUMERIC},
		#{calcObj.inTotal, jdbcType=NUMERIC},
		#{calcObj.calcValue, jdbcType=NUMERIC},
		#{calcObj.missData, jdbcType=NUMERIC},
		#{calcObj.updateTv, jdbcType=TIMESTAMP}
		)
		</foreach>
	</insert>

	<insert id="batchSaveCalcObjEnergy" parameterType="java.util.List" databaseId="oracle">
	    merge into mdm_data_calc_obj_energy T1 USING ( 
		<foreach collection="list" item="calcObj" index="index" separator="UNION">
        select 
	        #{calcObj.dataCalcObjPK.calcObjId, jdbcType=VARCHAR} calc_obj_id,
	        #{calcObj.dataCalcObjPK.tv, jdbcType=TIMESTAMP} tv,
	        #{calcObj.dataCalcObjPK.schemeId, jdbcType=VARCHAR} scheme_id,
	        
	        #{calcObj.R0P1, jdbcType=NUMERIC} R0P1,
	        #{calcObj.R0P2, jdbcType=NUMERIC} R0P2,        
	        #{calcObj.R1P1, jdbcType=NUMERIC} R1P1,
	        #{calcObj.R1P2, jdbcType=NUMERIC} R1P2,	        
	        #{calcObj.R2P1, jdbcType=NUMERIC} R2P1,
	        #{calcObj.R2P2, jdbcType=NUMERIC} R2P2,        
	        #{calcObj.R3P1, jdbcType=NUMERIC} R3P1,
	        #{calcObj.R3P2, jdbcType=NUMERIC} R3P2,	        
	        #{calcObj.R4P1, jdbcType=NUMERIC} R4P1,
	        #{calcObj.R4P2, jdbcType=NUMERIC} R4P2,	
	        
	            
  			#{calcObj.R0P1A1, jdbcType=NUMERIC} R0P1A1,
	        #{calcObj.R0P1A2, jdbcType=NUMERIC} R0P1A2,
	        #{calcObj.R0P1A3, jdbcType=NUMERIC} R0P1A3,
	        #{calcObj.R0P1A4, jdbcType=NUMERIC} R0P1A4,	 	
	        
	       	#{calcObj.R1P1A1, jdbcType=NUMERIC} R1P1A1,
	        #{calcObj.R1P1A2, jdbcType=NUMERIC} R1P1A2,
	        #{calcObj.R1P1A3, jdbcType=NUMERIC} R1P1A3,
	        #{calcObj.R1P1A4, jdbcType=NUMERIC} R1P1A4,	 	
	        
	       	#{calcObj.R2P1A1, jdbcType=NUMERIC} R2P1A1,
	        #{calcObj.R2P1A2, jdbcType=NUMERIC} R2P1A2,
	        #{calcObj.R2P1A3, jdbcType=NUMERIC} R2P1A3,
	        #{calcObj.R2P1A4, jdbcType=NUMERIC} R2P1A4,	 	
	        
	       	#{calcObj.R3P1A1, jdbcType=NUMERIC} R3P1A1,
	        #{calcObj.R3P1A2, jdbcType=NUMERIC} R3P1A2,
	        #{calcObj.R3P1A3, jdbcType=NUMERIC} R3P1A3,
	        #{calcObj.R3P1A4, jdbcType=NUMERIC} R3P1A4,	
	        
	       	#{calcObj.R4P1A1, jdbcType=NUMERIC} R4P1A1,
	        #{calcObj.R4P1A2, jdbcType=NUMERIC} R4P1A2,
	        #{calcObj.R4P1A3, jdbcType=NUMERIC} R4P1A3,
	        #{calcObj.R4P1A4, jdbcType=NUMERIC} R4P1A4,		        
	        
	             
	        #{calcObj.dataSource, jdbcType=NUMERIC} data_source,	 	               
	        #{calcObj.updateTv, jdbcType=TIMESTAMP} update_tv, 	        
	        #{calcObj.dataVersion, jdbcType=NUMERIC} data_version,
	        #{calcObj.totalNumber, jdbcType=NUMERIC} total_number,	 
	        #{calcObj.missNumber, jdbcType=NUMERIC} miss_number,
	        #{calcObj.integrityRate, jdbcType=NUMERIC} integrity_rate 
	       
        from dual 
    	</foreach>
    	) T2 ON ( 
     		T1.calc_obj_id = T2.calc_obj_id 
    		and T1.tv = T2.tv 
    		and T1.scheme_id = T2.scheme_id   
    	) 
    	when not matched then 
    	insert ( 
	    	calc_obj_id,
	    	tv,
	    	scheme_id,   	
	        R0P1,
	        R0P2,        
	        R1P1,
	        R1P2,	        
	        R2P1,
	        R2P2,        
	        R3P1,
	        R3P2,	        
	        R4P1,
	        R4P2,
	        
	        R0P1A1,
	    	R0P1A2,
	    	R0P1A3,
	    	R0P1A4,	
	        
	    	R1P1A1,
	    	R1P1A2,
	    	R1P1A3,
	    	R1P1A4,	
	    	
	    	R2P1A1,
	    	R2P1A2,
	    	R2P1A3,
	    	R2P1A4,		
	    	
	    	R3P1A1,
	    	R3P1A2,
	    	R3P1A3,
	    	R3P1A4,	
	    	
	    	R4P1A1,
	    	R4P1A2,
	    	R4P1A3,
	    	R4P1A4,		   	        
	        	         
	        data_source,	 	               
	        update_tv, 	        
	        data_version,
	        total_number,	 
	        miss_number,
	        integrity_rate 
    	)  
   		values (
	    	T2.calc_obj_id,
	    	T2.tv,
	    	T2.scheme_id,   	
	        T2.R0P1,
	        T2.R0P2,        
	        T2.R1P1,
	        T2.R1P2,	        
	        T2.R2P1,
	        T2.R2P2,        
	        T2.R3P1,
	        T2.R3P2,	        
	        T2.R4P1,
	        T2.R4P2,	
	        
		   	T2.R0P1A1,
	    	T2.R0P1A2,
	    	T2.R0P1A3,
	    	T2.R0P1A4,	
	    	
	    	T2.R1P1A1,
	    	T2.R1P1A2,
	    	T2.R1P1A3,
	    	T2.R1P1A4,	
	    	
	    	T2.R2P1A1,
	    	T2.R2P1A2,
	    	T2.R2P1A3,
	    	T2.R2P1A4,	
	    	
	    	T2.R3P1A1,
	    	T2.R3P1A2,
	    	T2.R3P1A3,
	    	T2.R3P1A4,	
	    	
	    	T2.R4P1A1,
	    	T2.R4P1A2,
	    	T2.R4P1A3,
	    	T2.R4P1A4,		   	        
	                 
	        T2.data_source,	 	               
	        T2.update_tv, 	        
	        T2.data_version,
	        T2.total_number,	 
	        T2.miss_number,
	        T2.integrity_rate 
    	) 
    	when matched then 
        update 
	    set 
	        T1.R0P1 = T2.R0P1 ,
	        T1.R0P2 = T2.R0P2 ,        
	        T1.R1P1 = T2.R1P1 ,
	        T1.R1P2 = T2.R1P2 ,	        
	        T1.R2P1 = T2.R2P1 ,
	        T1.R2P2 = T2.R2P2 ,        
	        T1.R3P1 = T2.R3P1 ,
	        T1.R3P2 = T2.R3P2 ,	        
	        T1.R4P1 = T2.R4P1 ,
	        T1.R4P2 = T2.R4P2 ,	
	        
		    T1.R0P1A1 = T2.R0P1A1,
	    	T1.R0P1A2 = T2.R0P1A2,
	    	T1.R0P1A3 = T2.R0P1A3,
	    	T1.R0P1A4 = T2.R0P1A4,	
	    	
	    	T1.R1P1A1 = T2.R1P1A1,
	    	T1.R1P1A2 = T2.R1P1A2,
	    	T1.R1P1A3 = T2.R1P1A3,
	    	T1.R1P1A4 = T2.R1P1A4,	
	    	
	    	T1.R2P1A1 = T2.R2P1A1,
	    	T1.R2P1A2 = T2.R2P1A2,
	    	T1.R2P1A3 = T2.R2P1A3,
	    	T1.R2P1A4 = T2.R2P1A4,	
	    	
	    	T1.R3P1A1 = T2.R3P1A1,
	    	T1.R3P1A2 = T2.R3P1A2,
	    	T1.R3P1A3 = T2.R3P1A3,
	    	T1.R3P1A4 = T2.R3P1A4,	
	    	
	    	T1.R4P1A1 = T2.R4P1A1,
	    	T1.R4P1A2 = T2.R4P1A2,
	    	T1.R4P1A3 = T2.R4P1A3,
	    	T1.R4P1A4 = T2.R4P1A4,		    		        
	                 
	        T1.data_source = T2.data_source,	 	               
	        T1.update_tv = T2.update_tv, 	        
	        T1.data_version = T2.data_version,
	        T1.total_number = T2.total_number,	 
	        T1.miss_number = T2.miss_number,
	        T1.integrity_rate = T2.integrity_rate 
	</insert>

	<insert id="batchSaveCalcObjEnergy" parameterType="java.util.List" databaseId="mysql">
		replace into mdm_data_calc_obj_energy (
		calc_obj_id,
		tv,
		scheme_id,
		R0P1,
		R0P2,
		R1P1,
		R1P2,
		R2P1,
		R2P2,
		R3P1,
		R3P2,
		R4P1,
		R4P2,

		R0P1A1,
		R0P1A2,
		R0P1A3,
		R0P1A4,

		R1P1A1,
		R1P1A2,
		R1P1A3,
		R1P1A4,

		R2P1A1,
		R2P1A2,
		R2P1A3,
		R2P1A4,

		R3P1A1,
		R3P1A2,
		R3P1A3,
		R3P1A4,

		R4P1A1,
		R4P1A2,
		R4P1A3,
		R4P1A4,

		data_source,
		update_tv,
		data_version,
		total_number,
		miss_number,
		integrity_rate
		)
		values
		<foreach collection="list" item="calcObj" index="index" separator=",">
       (
			#{calcObj.dataCalcObjPK.calcObjId, jdbcType=VARCHAR},
			#{calcObj.dataCalcObjPK.tv, jdbcType=TIMESTAMP},
			#{calcObj.dataCalcObjPK.schemeId, jdbcType=VARCHAR},

			#{calcObj.R0P1, jdbcType=NUMERIC},
			#{calcObj.R0P2, jdbcType=NUMERIC},
			#{calcObj.R1P1, jdbcType=NUMERIC},
			#{calcObj.R1P2, jdbcType=NUMERIC},
			#{calcObj.R2P1, jdbcType=NUMERIC},
			#{calcObj.R2P2, jdbcType=NUMERIC},
			#{calcObj.R3P1, jdbcType=NUMERIC},
			#{calcObj.R3P2, jdbcType=NUMERIC},
			#{calcObj.R4P1, jdbcType=NUMERIC},
			#{calcObj.R4P2, jdbcType=NUMERIC},


			#{calcObj.R0P1A1, jdbcType=NUMERIC},
			#{calcObj.R0P1A2, jdbcType=NUMERIC},
			#{calcObj.R0P1A3, jdbcType=NUMERIC},
			#{calcObj.R0P1A4, jdbcType=NUMERIC},

			#{calcObj.R1P1A1, jdbcType=NUMERIC},
			#{calcObj.R1P1A2, jdbcType=NUMERIC},
			#{calcObj.R1P1A3, jdbcType=NUMERIC},
			#{calcObj.R1P1A4, jdbcType=NUMERIC},

			#{calcObj.R2P1A1, jdbcType=NUMERIC},
			#{calcObj.R2P1A2, jdbcType=NUMERIC},
			#{calcObj.R2P1A3, jdbcType=NUMERIC},
			#{calcObj.R2P1A4, jdbcType=NUMERIC},

			#{calcObj.R3P1A1, jdbcType=NUMERIC},
			#{calcObj.R3P1A2, jdbcType=NUMERIC},
			#{calcObj.R3P1A3, jdbcType=NUMERIC},
			#{calcObj.R3P1A4, jdbcType=NUMERIC},

			#{calcObj.R4P1A1, jdbcType=NUMERIC},
			#{calcObj.R4P1A2, jdbcType=NUMERIC},
			#{calcObj.R4P1A3, jdbcType=NUMERIC},
			#{calcObj.R4P1A4, jdbcType=NUMERIC},


			#{calcObj.dataSource, jdbcType=NUMERIC},
			#{calcObj.updateTv, jdbcType=TIMESTAMP},
			#{calcObj.dataVersion, jdbcType=NUMERIC},
			#{calcObj.totalNumber, jdbcType=NUMERIC},
			#{calcObj.missNumber, jdbcType=NUMERIC},
			#{calcObj.integrityRate, jdbcType=NUMERIC}
			)

		</foreach>
	</insert>
</mapper>