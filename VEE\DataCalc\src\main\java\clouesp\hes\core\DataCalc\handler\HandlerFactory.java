package clouesp.hes.core.DataCalc.handler;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.springframework.stereotype.Component;
import clouesp.hes.core.DataCalc.handler.Calculation.CalcEnergyHandler;
import clouesp.hes.core.DataCalc.handler.Calculation.CalcObjHandler;
import clouesp.hes.core.DataCalc.handler.Estimation.EstimationAutoHandler;
import clouesp.hes.core.DataCalc.handler.Validation.ValidationHandler;

@Component
public class HandlerFactory {	
	private Map<String, BaseHandler> mapBaseHandler = new ConcurrentHashMap<String, BaseHandler>();

	public synchronized BaseHandler getHandler(String serviceId, String type) {
		String key = serviceId;
		key += "\r\n";
		key += type;
		if (mapBaseHandler.containsKey(key))
			return mapBaseHandler.get(key);
		BaseHandler handler = null;
		switch (type) {
		case "VALIDATION":
			handler = new ValidationHandler();
			break;
		case "CALCENERGY":
			handler = new CalcEnergyHandler();
			break;
		case "CALCOBJ":
			handler = new CalcObjHandler();
			break;	
		case "AUTOESTIMATION":
			handler = new EstimationAutoHandler();
			break;
		}
		if (handler != null) {
			handler.init(serviceId);
			mapBaseHandler.put(key, handler);
		}
		return handler;
	}
	
}
