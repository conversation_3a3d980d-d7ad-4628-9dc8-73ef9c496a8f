package clouesp.hes.common.DataEntity.Dict;

import javax.persistence.*;
import java.util.Objects;

@Entity
@Table(name = "DICT_VEE_EVENT")
public class DictVeeEvent {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "ID", nullable = false, length = 32)
    private String id;
    @Basic
    @Column(name = "NAME", nullable = false, length = 128)
    private String name;
    @Basic
    @Column(name = "USER_DEFINE", nullable = false, precision = 0)
    private short userDefine;
    @Basic
    @Column(name = "METHOD", nullable = false, length = 32)
    private String method;
    @Basic
    @Column(name = "DATAITEM_ID", nullable = true, length = 64)
    private String dataitemId;
    @Basic
    @Column(name = "DESCR", nullable = true, length = 256)
    private String descr;
    @Basic
    @Column(name = "SORT_ID", nullable = true, precision = 0)
    private Short sortId;
    @Basic
    @Column(name = "RULE_TYPE", nullable = true, precision = 0)
    private Byte ruleType;
    @Basic
    @Column(name = "ESTIMATION_FLAG", nullable = true, precision = 0)
    private Byte estimationFlag;
    @Basic
    @Column(name = "MULT_DATASOURCE", nullable = true, precision = 0)
    private Byte multDatasource;
    @Basic
    @Column(name = "DATASOURCE_TYPE", nullable = true, precision = 0)
    private Byte datasourceType;
    //@Basic
    //@Column(name = "OBJECT_TYPE", nullable = true, precision = 0)
    //private Byte objectType;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public short getUserDefine() {
        return userDefine;
    }

    public void setUserDefine(short userDefine) {
        this.userDefine = userDefine;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getDataitemId() {
        return dataitemId;
    }

    public void setDataitemId(String dataitemId) {
        this.dataitemId = dataitemId;
    }

    public String getDescr() {
        return descr;
    }

    public void setDescr(String descr) {
        this.descr = descr;
    }

    public Short getSortId() {
        return sortId;
    }

    public void setSortId(Short sortId) {
        this.sortId = sortId;
    }

    public Byte getRuleType() {
        return ruleType;
    }

    public void setRuleType(Byte ruleType) {
        this.ruleType = ruleType;
    }

    public Byte getEstimationFlag() {
        return estimationFlag;
    }

    public void setEstimationFlag(Byte estimationFlag) {
        this.estimationFlag = estimationFlag;
    }

    public Byte getMultDatasource() {
        return multDatasource;
    }

    public void setMultDatasource(Byte multDatasource) {
        this.multDatasource = multDatasource;
    }

    public Byte getDatasourceType() {
        return datasourceType;
    }

    public void setDatasourceType(Byte datasourceType) {
        this.datasourceType = datasourceType;
    }

    //public Byte getObjectType() {
    //    return objectType;
   // }

    //public void setObjectType(Byte objectType) {
    //    this.objectType = objectType;
    //}

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DictVeeEvent that = (DictVeeEvent) o;
        return userDefine == that.userDefine && Objects.equals(id, that.id) && Objects.equals(name, that.name) && Objects.equals(method, that.method) && Objects.equals(dataitemId, that.dataitemId) && Objects.equals(descr, that.descr) && Objects.equals(sortId, that.sortId) && Objects.equals(ruleType, that.ruleType) && Objects.equals(estimationFlag, that.estimationFlag) && Objects.equals(multDatasource, that.multDatasource) && Objects.equals(datasourceType, that.datasourceType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name, userDefine, method, dataitemId, descr, sortId, ruleType, estimationFlag, multDatasource, datasourceType);
    }
}
