# 业务数据处理流程与流程图

## 名词定义

### 数据类型定义

| 英文名称 | 中文名称 | 说明 |
|---------|---------|------|
| Register Data | 表码 | 电表显示的读数，表示累计用电量 |
| Interval Data | 增量 | 两个时间点之间的用电量差值 |
| Instantaneous Data | 瞬时量 | 某一时刻的即时测量值 |
| Demand Data | 需量 | 在特定时间间隔内的平均功率 |
| Energy Data | 电量 | 电能消耗的计量值 |

### 异常类型定义

| 英文名称 | 中文名称 | 说明 |
|---------|---------|------|
| Register Data Exception | 表码异常 | 表码读数异常，如跳码、倒走等 |
| Interval Data Exception | 增量异常 | 用电增量异常，如负增量、突增等 |
| Energy Data Exception | 电量异常 | 电量数据异常，如不合理的用电量 |
| Voltage Data Exception | 电压异常 | 电压值超出正常范围 |
| Current Data Exception | 电流异常 | 电流值异常，如过载、断流等 |
| Power Data Exception | 负荷异常 | 功率负荷异常，如超出合理范围 |
| Energy Usage Exception | 用电异常 | 用电行为异常，如窃电、违约用电 |
| Asset Data Exception | 档案异常 | 设备档案信息异常或不完整 |
| Loss Data Exception | 线损异常 | 线路损耗数据异常 |

### 数据项映射表

| 数据项标识 | 数据项名称（英文） | 数据项名称（中文） |
|-----------|-------------------|------------------|
| R0P1 | Active Energy Import[Unit: kWh] | 正向有功电能[单位: 千瓦时] |
| R0P2 | Active Energy Export[Unit: kWh] | 反向有功电能[单位: 千瓦时] |
| R0P3 | Reactive Energy Import[Unit: kVarh] | 正向无功电能[单位: 千乏时] |
| R0P4 | Reactive Energy Export[Unit: kVarh] | 反向无功电能[单位: 千乏时] |
| R0P5 | Reactive Energy QI[Unit: kVarh] | 第一象限无功电能[单位: 千乏时] |
| R0P6 | Reactive Energy QII[Unit: kVarh] | 第二象限无功电能[单位: 千乏时] |
| R0P7 | Reactive Energy QIII[Unit: kVarh] | 第三象限无功电能[单位: 千乏时] |
| R0P8 | Reactive Energy QIV[Unit: kVarh] | 第四象限无功电能[单位: 千乏时] |
| R1P1 | Active Energy Import Rate 1[Unit: kWh] | 费率1正向有功电能[单位: 千瓦时] |
| R1P2 | Active Energy Export Rate 1[Unit: kWh] | 费率1反向有功电能[单位: 千瓦时] |
| R1P3 | Reactive Energy Import Rate 1[Unit: kVarh] | 费率1正向无功电能[单位: 千乏时] |
| R1P4 | Reactive Energy Export Rate 1[Unit: kVarh] | 费率1反向无功电能[单位: 千乏时] |
| R2P1 | Active Energy Import Rate 2[Unit: kWh] | 费率2正向有功电能[单位: 千瓦时] |
| R2P2 | Active Energy Export Rate 2[Unit: kWh] | 费率2反向有功电能[单位: 千瓦时] |
| R2P3 | Reactive Energy Import Rate 2[Unit: kVarh] | 费率2正向无功电能[单位: 千乏时] |
| R2P4 | Reactive Energy Export Rate 2[Unit: kVarh] | 费率2反向无功电能[单位: 千乏时] |
| R3P1 | Active Energy Import Rate 3[Unit: kWh] | 费率3正向有功电能[单位: 千瓦时] |
| R3P2 | Active Energy Export Rate 3[Unit: kWh] | 费率3反向有功电能[单位: 千瓦时] |
| R3P3 | Reactive Energy Import Rate 3[Unit: kVarh] | 费率3正向无功电能[单位: 千乏时] |
| R3P4 | Reactive Energy Export Rate 3[Unit: kVarh] | 费率3反向无功电能[单位: 千乏时] |
| R4P1 | Active Energy Import Rate 4[Unit: kWh] | 费率4正向有功电能[单位: 千瓦时] |
| R4P2 | Active Energy Export Rate 4[Unit: kWh] | 费率4反向有功电能[单位: 千瓦时] |
| R4P3 | Reactive Energy Import Rate 4[Unit: kVarh] | 费率4正向无功电能[单位: 千乏时] |
| R4P4 | Reactive Energy Export Rate 4[Unit: kVarh] | 费率4反向无功电能[单位: 千乏时] |
| R0P1A1 | Cumulative active energy, \|+A\| + \|-A\| | 累计有功电能总和（正向+反向）|
| R0P1A2 | Cumulative active energy, \|+A\| - \|-A\| | 累计有功电能差值（正向-反向）|
| R0P1A3 | Cumulative reactive energy, \|+R\| + \|-R\| | 累计无功电能总和（正向+反向）|
| R0P1A4 | Cumulative reactive energy, \|+R\| - \|-R\| | 累计无功电能差值（正向-反向）|

## 整体数据流程

根据代码分析，整个数据处理流程如下：

1. **DataExport**：从HES数据库中导出数据到CSV文件
2. **DataImport**：导入CSV文件中的数据到系统
3. **DataScan**：扫描数据库中的记录，将其发送到消息队列
4. **MqBus**：消息队列总线，负责DataScan和DataCalc之间的通信
5. **DataGenerator**：生成测试或模拟数据
6. **DataCalc**：对导入的数据进行处理和计算
7. **RealTimeDB**：实时内存数据库，用于临时存储和快速访问处理中的数据
8. **MDM数据库**：持久化存储系统，作为最终业务数据的存储位置

### 整体数据处理流程图

```mermaid
graph TD
    subgraph 数据源
        HES[HES数据库]
    end
    
    subgraph 数据获取阶段
        DE[DataExport<br>数据导出]
        DI[DataImport<br>数据导入]
        DIBackup[文件备份移至Backup]
        DS[DataScan<br>数据库扫描]
        DG[DataGenerator<br>数据生成器]
    end
    
    subgraph 数据处理阶段
        MB[MqBus<br>消息总线]
        DC[DataCalc<br>数据计算]
        subgraph 计算类型
            VA[数据验证<br>VALIDATION]
            CE[电量计算<br>CALCENERGY]
            CO[对象计算<br>CALCOBJ]
            AE[自动估算<br>AUTOESTIMATION]
        end
    end
    
    subgraph 数据存储阶段
        RT[RealTimeDB<br>实时内存数据库]
        MDM[MDM数据库<br>持久化存储]
    end
    
    %% 主要数据流向
    HES --> DE
    DE -->|CSV文件| DI
    DI -->|处理后移动| DIBackup
    DI --> MDM
    DI --> RT
    DG -->|模拟数据| DI
    MDM --> DS
    DS --> MB
    MB --> DC
    DC --> VA & CE & CO & AE
    VA & CE & CO & AE --> MB
    DC --> MDM
    DC --> RT
    
    %% 内存数据库加速访问
    MDM <-->|定期同步| RT
    
    %% 关系说明
    classDef primary fill:#f96,stroke:#333,stroke-width:2px
    classDef secondary fill:#99ccff,stroke:#333,stroke-width:1px
    classDef tertiary fill:#9f9,stroke:#333,stroke-width:1px
    classDef hidden fill:#ffffff,stroke:#ffffff,stroke-width:0px
    
    class HES,DE,DI,DC,MDM primary
    class DS,MB,RT secondary
    class DG,VA,CE,CO,AE tertiary
    class DIBackup secondary
```

下面对每个组件的详细工作流程进行说明：

## 1. DataExport - 数据导出

### 工作流程

1. DataExport组件通过`DataExportService`定期执行数据导出任务
2. 首先从`rtSysDataitemExportRepository`查询需要导出的数据项
3. 对每个数据项，获取相应的存储信息和表信息
4. 创建`DataTableHandler`对象处理表数据的导出
5. 导出的数据以CSV文件格式保存到本地目录（由配置文件中的`ftp.localDir`指定）
6. 通过`FtpService`将生成的CSV文件上传到FTP/SFTP服务器的`ftpDir`目录
7. 文件上传成功后，DataImport组件会从FTP/SFTP服务器下载这些文件进行后续处理

### 数据导出详细过程

1. **查询数据项配置**：
   - 系统首先从`rtSysDataitemExportRepository`查询设置为需要导出的数据项（`exportFlag=1`）
   - 每个数据项都在`SysDataitemExport`表中配置，包含数据项标识、数据类型、时间类型等信息
   
2. **获取数据存储信息**：
   - 对每个数据项，系统查询`MeterDataStorageInfo`获取其存储信息
   - 从`MeterDataStorageTable`获取对应的表信息
   
3. **构建数据导出处理器**：
   - 为每个表创建一个`DataTableHandler`对象
   - 如果多个数据项存储在同一个表中，它们共用同一个处理器
   
4. **执行数据查询与导出**：
   - 每个处理器根据存储信息和导出配置构建SQL查询
   - 从HES数据库中查询数据
   - 将查询结果转换为CSV格式
   - 保存到指定目录

### 导出的CSV文件格式

根据实际项目中导出的CSV文件示例，系统导出的CSV文件采用以下格式规范：

1. **文件命名规则**：
   - 文件名格式：`[数据类型]_[时间戳]_[其他信息]_[序号].csv`
   - 示例：`MONTH_20250107014716_DATA_MD_MONTHLY_LB_2_0.csv`（月冻结数据）
   - 示例：`EVENT_2501091147_1_0.csv`（事件数据）
   - 示例：`DAY_20250107014716_DATA_MD_DAILY_LB_2_0.csv`（日冻结数据）
   - 示例：`CURVE_20250107014716_DATA_MD_CURVE_LB_2_0.csv`（曲线数据）

2. **不同类型CSV文件结构**：

   a) **事件数据文件**:
   ```
   sdp_id,tv,mdm_event_type,hes_event_id,hes_event_detail
   202300002349,2025-01-07 18:37:04,20001001,3.26.0.285,
   202300002841,2025-01-08 17:55:29,20001001,3.26.0.285,
   ...
   ```
   
   b) **月冻结数据文件**:
   ```
   sdp_id,tv,update_tv,REG_R0P1,REG_R0P2,REG_R0P1A1,REG_R1P1A1,REG_R2P1A1,REG_R3P1A1,REG_R0P1A3,REG_R1P1A3,REG_R2P1A3,REG_R3P1A3,REG_R1P1,REG_R2P1,REG_R3P1,REG_R1P2,REG_R2P2,REG_R3P2
   202222008104,2025-01-01 00:00:00,2025-01-07 02:01:01,4256.730,0.000,4256.730,3207.230,1049.490,0.000,618.370,388.040,230.330,0.000,3207.230,1049.490,0.000,0.000,0.000,0.000
   202222008001,2025-01-01 00:00:00,2025-01-07 02:01:01,6063.180,0.000,6063.180,4694.430,1368.750,0.000,3772.960,2344.610,1428.340,0.000,4694.430,1368.750,0.000,0.000,0.000,0.000
   ...
   ```
   
   c) **日冻结数据文件**:
   ```
   sdp_id,tv,update_tv,REG_R0P1,REG_R0P2,REG_R0P1A1,REG_R1P1A1,REG_R2P1A1,REG_R3P1A1,REG_R0P1A3,...
   202222008104,2025-01-07 00:00:00,2025-01-07 14:01:01,145.230,0.000,145.230,107.320,37.910,0.000,21.240,...
   202222008001,2025-01-07 00:00:00,2025-01-07 14:01:01,198.450,0.000,198.450,152.740,45.710,0.000,124.230,...
   ...
   ```
   
   d) **曲线数据文件**:
   ```
   sdp_id,tv,update_tv,REG_R0P1,REG_R0P2,REG_R0P1A1,REG_R1P1A1,REG_R2P1A1,REG_R3P1A1,REG_R0P1A3,REG_R1P1A3,REG_R2P1A3,REG_R3P1A3,...
   202222008104,2025-01-07 00:15:00,2025-01-07 01:05:22,0.624,0.000,0.624,0.420,0.204,0.000,0.090,0.052,0.038,0.000,...
   202222008104,2025-01-07 00:30:00,2025-01-07 01:05:22,0.638,0.000,0.638,0.430,0.208,0.000,0.092,0.053,0.039,0.000,...
   ...
   ```

3. **主要数据列说明**：

   a) **事件数据文件**:
   - **sdp_id**：服务点标识符（Service Delivery Point ID），表示唯一的测量点
   - **tv**：时间点（Time Value），事件发生的时间戳
   - **mdm_event_type**：事件类型编码，例如20001001、20001002等
   - **hes_event_id**：HES系统事件ID，格式如3.26.0.285
   - **hes_event_detail**：事件详细信息（可选，实际样例中为空）

   b) **月冻结数据文件**:
   - **sdp_id**：服务点标识符
   - **tv**：数据冻结时间点（月初）
   - **update_tv**：数据更新时间
   - **REG_R0P1**：表码数据 - 正向有功电能
   - **REG_R0P2**：表码数据 - 反向有功电能
   - **REG_R0P1A1**：累计有功电能总和（正向+反向）
   - **REG_R1P1A1**、**REG_R2P1A1**、**REG_R3P1A1**：不同费率的累计有功电能总和
   - **REG_R0P1A3**、**REG_R1P1A3**、**REG_R2P1A3**、**REG_R3P1A3**：累计无功电能总和（正向+反向）及不同费率
   - **REG_R1P1**、**REG_R2P1**、**REG_R3P1**：不同费率的正向有功电能
   - **REG_R1P2**、**REG_R2P2**、**REG_R3P2**：不同费率的反向有功电能

   c) **日冻结数据文件**:
   - 基于月冻结数据文件结构推断，包含相同的列名格式
   - **sdp_id**：服务点标识符
   - **tv**：数据冻结时间点（每日凌晨）
   - **update_tv**：数据更新时间
   - **REG_R0P1**、**REG_R0P2**等数据项：与月冻结数据格式相同，但数据为每日冻结值

   d) **曲线数据文件**:
   - 基于月冻结数据文件结构推断，包含相同的列名格式
   - **sdp_id**：服务点标识符
   - **tv**：数据时间点（通常为每15分钟或30分钟一个点）
   - **update_tv**：数据更新时间
   - **REG_R0P1**、**REG_R0P2**等数据项：与月冻结数据格式相同，但数据为每个时间点的值

4. **数据特点**：

   a) **事件数据**：
   - 每行记录一个事件实例
   - 事件按时间顺序排列
   - 同一设备可能有多个不同类型的事件
   - 事件数据不包含电能计量值，主要记录事件发生的时间和类型
   - 根据实际样例文件，主要记录事件类型代码20001001、20001002、20004295、20004296等

   b) **月冻结数据**：
   - 每行表示一个测量点在特定月份的累计电能读数
   - 包含多个电能数据项，涵盖不同的费率和电能类型
   - 数据以浮点数形式存储，通常保留3位小数
   - 根据实际样例，数据冻结时间统一为2025-01-01 00:00:00，更新时间为2025-01-07左右

   c) **日冻结数据**：
   - 每行表示一个测量点在特定日期的累计电能读数
   - 数据结构与月冻结类似，但时间粒度更细（按天）
   - 适合日用电量分析和日报表生成

   d) **曲线数据**：
   - 每行表示一个时间点（如15分钟）的电能数据
   - 数据点密集，通常每天每个测量点有96个数据点（15分钟间隔）
   - 列头带有"REG_"前缀，与月冻结和日冻结数据结构类似
   - 适合进行负荷曲线分析、峰谷分析等精细化分析

5. **数据值格式和CSV特性**：
   - 时间格式：`YYYY-MM-DD HH:MM:SS`
   - 数值格式：浮点数，保留3位小数
   - 使用逗号(,)作为字段分隔符
   - 第一行为列标题
   - 无引号包围字段
   - 空字段以空字符表示（例如事件详情字段中的空值）

### 关键组件

- **DataExportService**：负责数据导出的主要服务
- **DataTableHandler**：处理具体的表数据导出
- **FtpService**：负责文件上传到FTP/SFTP服务器
- **SysDataitemExport**：配置数据项导出的实体类

### DataExport数据导出流程图

```mermaid
graph TD
    start[开始] --> query[查询需要导出的数据项<br>SysDataitemExport]
    query --> check{是否有<br>数据项?}
    check -->|否| endEmpty[结束]
    check -->|是| iter[遍历每个数据项]
    iter --> getMeta[获取存储信息和表信息]
    getMeta --> handler[创建DataTableHandler<br>处理表数据导出]
    handler --> buildSQL[构建SQL查询]
    buildSQL --> queryDB[查询HES数据库]
    queryDB --> format[格式化数据为CSV]
    format --> saveFile[保存CSV文件到本地目录]
    saveFile --> uploadFTP[上传CSV文件到FTP服务器]
    uploadFTP --> nextItem{还有更多<br>数据项?}
    nextItem -->|是| iter
    nextItem -->|否| endExport[结束导出]
```

## 2. DataScan - 数据扫描

### 工作流程

DataScan就像一个勤劳的"数据收集员"，它定期查看MDM数据库中的各种电力数据记录，并将它们发送给处理系统。具体工作方式如下：

1. **定时扫描**：每小时检查一次MDM数据库中的新数据（配置参数scanCycle=1，单位为小时）
2. **全面覆盖**：扫描四种主要数据类型
   - 月度表码数据：查找过去6个月的数据（从mdm_data_reg_monthly表）
   - 日度表码数据：查找过去30天的数据（从mdm_data_reg_dayly表）
   - 分钟级表码数据：查找过去7天的数据（从mdm_data_reg_minutely表）
   - 事件数据：查找过去1个月的数据（从mdm_data_vee_event表）
3. **批量处理**：每次最多取出10万条记录进行处理，避免系统负担过重
4. **消息传递**：将找到的数据通过RocketMQ消息队列发送给后续处理模块
5. **状态更新**：定期更新自身的服务状态，确保系统能监控到服务是否正常运行

### 工作原理详解

#### 1. 扫描控制参数（可在application.yml中配置）

应用程序可通过配置文件调整扫描范围：
```
schedule:
  scanCycle: 1       # 扫描频率：1小时一次
  scanMinute: 7      # 分钟级数据扫描范围：过去7天
  scanDay: 30        # 日度数据扫描范围：过去30天
  scanMonth: 6       # 月度数据扫描范围：过去6个月
  scanEvent: 1       # 事件数据扫描范围：过去1个月
```

#### 2. 扫描步骤与数据处理流程

每次扫描任务启动时，DataScan会按顺序执行以下操作：

1. **准备工作**：
   - 设置当前时间
   - 根据配置参数计算各类数据的起始时间点
   - 选择合适的数据库查询方式（MySQL或Oracle）
   - **查询数据计算进度表**：从`mdm_data_calc_progress`表获取已处理数据的进度信息

2. **按顺序扫描四类数据**：
   - **月度数据扫描**：
     ```
     计算6个月前的日期 → 查询满足条件的月度数据 → 转换成DataReg对象 → 打包发送
     ```
   
   - **日度数据扫描**：
     ```
     计算30天前的日期 → 查询满足条件的日度数据 → 转换成DataReg对象 → 打包发送
     ```
   
   - **分钟级数据扫描**：
     ```
     计算7天前的日期 → 查询满足条件的分钟级数据 → 转换成DataReg对象 → 打包发送
     ```
   
   - **事件数据扫描**：
     ```
     计算1个月前的日期 → 查询满足条件的事件数据 → 转换成DataVEEEvent对象 → 打包发送
     ```

3. **数据加工处理**：
   - 表码数据处理（handleDataReg方法）：
     - 从数据库结果集中读取每一行数据
     - 创建DataReg对象并填充字段值
     - **检查计算进度**：通过`DataCalcProgress`记录比对，确认数据是否已经处理过
     - 检查是否已经处理过（避免重复处理同一数据）
     - 标记数据类型（月度/日度/分钟级）
     - 装入消息并发送

   - 事件数据处理（handleEvent方法）：
     - 从数据库结果集中读取每一行数据
     - 创建DataVEEEvent对象并填充字段值
     - 装入消息并发送

#### 3. 消息发送机制

DataScan像是一个"邮递员"，它将收集到的数据打包成"邮件"（消息），然后通过"邮局"（RocketMQ）发送给"收件人"（数据计算服务）：

1. **消息打包**：
   - 创建MQMsg消息对象
   - 设置消息类型（"Register"或"VeeEvent"）
   - 添加发送方信息（DataScan的服务ID）
   - 设置消息主题和标签（指向目标服务）
   - 将数据对象放入消息体

2. **消息投递**：
   - 表码数据通过RocketMQ发送到数据验证处理服务（ValidationHandler）
   - 事件数据通过RocketMQ发送到事件处理服务
   - 接收方根据消息类型分别处理不同的数据

### 数据流向示意图

```mermaid
graph TD
    A[DataScan启动] --> AA["每小时触发一次扫描任务"]
    
    AA --> B1["扫描月度数据<br>（过去6个月）"]
    B1 --> B2["处理并打包数据<br>（每批最多10万条）"]
    B2 --> B3["发送到消息队列"]
    
    B3 --> C1["扫描日度数据<br>（过去30天）"]
    C1 --> C2["处理并打包数据<br>（每批最多10万条）"]
    C2 --> C3["发送到消息队列"]
    
    C3 --> D1["扫描分钟级数据<br>（过去7天）"]
    D1 --> D2["处理并打包数据<br>（每批最多10万条）"]
    D2 --> D3["发送到消息队列"]
    
    D3 --> E1["扫描事件数据<br>（过去1个月）"]
    E1 --> E2["处理并打包数据<br>（每批最多10万条）"]
    E2 --> E3["发送到消息队列"]
    
    B3 & C3 & D3 & E3 --> F["消息队列（RocketMQ）"]
    F --> G["数据计算服务（DataCalc）"]
    G --> H1["数据验证"]
    G --> H2["电量计算"]
    G --> H3["对象计算"]
    G --> H4["自动估算"]
```

### 总结

DataScan就像一个高效的数据管道，它：
- 每小时从MDM数据库取出最新的电力数据
- 分批处理大量数据，确保系统稳定运行
- 将处理好的数据通过RocketMQ消息队列发送给后续处理模块
- 自动更新服务状态，确保系统监控正常

这个组件在整个数据处理流程中起到了"数据供应商"的作用，为后续的数据验证、计算和分析提供必要的原始数据。

### 关键组件

- **DataScanApplication**：应用程序入口
- **InitCommandLineRunner**：启动时运行的初始化任务，负责启动定时任务
- **ScheduleService**：负责定时执行扫描任务，根据scanCycle配置的周期(1小时)触发
- **ScheduleScanTask**：实现具体的数据库扫描逻辑
- **ServerConfig**：负责系统配置的加载和管理
- **persistenceJdbcTemplate**：用于访问MDM持久化数据库的JDBC模板
- **ServiceProducer**：负责将消息发送到RocketMQ消息队列

### DataScan数据扫描流程图

```mermaid
graph TD
    A[开始扫描] --> AA[每小时触发一次扫描任务]
    AA --> B{检查数据库类型}
    B -->|Oracle| C[使用Oracle查询策略<br>扫描MDM持久化数据库]
    B -->|MySQL| D[使用MySQL查询策略<br>扫描MDM持久化数据库]
    
    C & D --> E1[扫描月度数据<br>mdm_data_reg_monthly]
    E1 --> E2[最多100000条分批处理]
    E2 --> E3[handleDataReg处理数据<br>loadType:Register]
    
    E3 --> F1[扫描日度数据<br>mdm_data_reg_dayly]
    F1 --> F2[最多100000条分批处理]
    F2 --> F3[handleDataReg处理数据<br>loadType:Register]
    
    F3 --> G1[扫描分钟级数据<br>mdm_data_reg_minutely]
    G1 --> G2[最多100000条分批处理]
    G2 --> G3[handleDataReg处理数据<br>loadType:Register]
    
    G3 --> H1[扫描VEE事件数据<br>mdm_data_vee_event]
    H1 --> H2[最多100000条分批处理]
    H2 --> H3[handleEvent处理数据<br>loadType:VeeEvent]
    
    E3 & F3 & G3 & H3 --> I[sendMsg方法发送数据到MqBus<br>目标:ValidationHandler]
    I --> J[结束扫描]
```

### DataScan数据扫描流程图

```mermaid
graph TD
    A[DataScan启动] --> AA[每小时触发一次扫描任务]
    AA --> B{检查数据库类型}
    B -->|Oracle| C[使用Oracle查询策略<br>扫描MDM持久化数据库]
    B -->|MySQL| D[使用MySQL查询策略<br>扫描MDM持久化数据库]
    
    C & D --> E1[扫描月度数据<br>mdm_data_reg_monthly<br>（过去6个月）]
    E1 --> E2[最多100000条分批处理]
    E2 --> E3[handleDataReg处理数据<br>loadType:Register]
    
    E3 --> F1[扫描日度数据<br>mdm_data_reg_dayly<br>（过去30天）]
    F1 --> F2[最多100000条分批处理]
    F2 --> F3[handleDataReg处理数据<br>loadType:Register]
    
    F3 --> G1[扫描分钟级数据<br>mdm_data_reg_minutely<br>（过去7天）]
    G1 --> G2[最多100000条分批处理]
    G2 --> G3[handleDataReg处理数据<br>loadType:Register]
    
    G3 --> H1[扫描VEE事件数据<br>mdm_data_vee_event<br>（过去1个月）]
    H1 --> H2[最多100000条分批处理]
    H2 --> H3[handleEvent处理数据<br>loadType:VeeEvent]
    
    E3 & F3 & G3 & H3 --> I[sendMsg方法发送数据到MqBus<br>目标:ValidationHandler]
    I --> K[消息队列（RocketMQ）]
    K --> L[数据计算服务（DataCalc）]
    L --> M1[数据验证]
    L --> M2[电量计算]
    L --> M3[对象计算]
    L --> M4[自动估算]
```

### 数据计算进度管理

DataScan使用`mdm_data_calc_progress`表（通过`DataCalcProgress`实体类访问）来跟踪和管理数据处理的进度。这是系统避免重复处理数据的关键机制：

1. **进度表结构**：
   - `sdp_id`：服务点ID，标识数据所属的测量点
   - `scheme_id`：方案ID，标识数据处理采用的计算方案
   - `load_type`：数据类型，如"Register"（表码）或"VeeEvent"（事件）
   - `tv`：最后处理时间，记录该测量点最后一次数据处理的时间戳
   - `update_tv`：更新时间，记录进度信息的最后更新时间

2. **进度检查流程**：
   - 数据扫描启动时，系统首先通过`DataCalcProgressRepository.findAll()`加载所有进度记录到内存中
   - 对每条记录，构建唯一键：`sdpId + "#" + schemeId + "#" + loadType`作为Map的key
   - 在处理扫描到的数据时，使用相同方式构建键并检查Map中是否存在
   - 如果找到匹配记录，比较数据时间戳与进度记录中的时间戳：
     ```java
     if(dataReg.getDataRegPK().getTv().getTime() <= calcProgressTv.getTime()) {
         continue; // 跳过已处理的数据
     }
     ```
   - 对于新的数据，正常处理并通过MQ发送到DataCalc服务

3. **进度表在整体流程中的作用**：
   - **增量处理**：DataScan只处理新增或更新的数据，避免重复工作
   - **系统协调**：DataCalc处理数据后会更新进度表，供DataScan下次扫描时参考
   - **资源优化**：减少不必要的数据处理和消息传输，降低系统负载
   - **数据一致性**：防止同一数据被重复处理可能导致的数据异常

在DataScan和DataCalc之间，`mdm_data_calc_progress`表形成了一个反馈循环：DataScan扫描数据并检查进度→发送未处理数据→DataCalc处理数据→更新进度表→DataScan在下次扫描时读取更新后的进度。这确保了系统只处理新数据，提高了整体效率。

## 3. DataImport - 数据导入

### 工作流程

1. `FtpService`首先以线程方式启动，从远程FTP/SFTP服务器下载文件：
   - 从`ftp.ftpManualDir`目录下载CSV文件到本地`ftp.manualDir`目录
   - 从`ftp.ftpDir`目录下载CSV文件到本地`ftp.localDir`目录
   - 支持FTP和SFTP两种协议（由`ftp.type`配置决定）
   - 每10分钟执行一次下载操作

2. `DataImportService`定期执行，扫描两个本地目录：
   - `ftp.localDir`：DataExport生成并由FTP下载的原始文件目录
   - `ftp.manualDir`：手动上传并由FTP下载的文件目录
   
3. 对于每个找到的CSV文件：
   - 检查文件是否存在
   - 检查文件修改时间是否超过一定时间（防止处理正在写入的文件）
   - 对于`localDir`中的文件，等待文件修改时间超过300秒后处理
   - 对于`manualDir`中的文件，等待文件修改时间超过600秒后处理
   - 使用`dataFileHandler`处理CSV文件内容
   
4. 对于手动导入的文件（在`manualDir`目录下），额外解析文件名中的数据类型信息

5. 导入完成后，数据将被保存到数据库中

6. 处理完成后的文件会被移动到原目录下的`Backup`子目录中

### 关键组件

- **FtpService**：负责从远程FTP/SFTP服务器下载文件
- **DataImportService**：负责数据导入的主要服务
- **DataFileHandler**：处理具体的CSV文件导入
- **EventFileHandler**：处理事件文件导入
- **FileUtils**：提供文件操作功能，包括文件备份
- **MeterSdpMapService**：处理表计与数据点的映射关系

### DataImport数据导入流程图

```mermaid
flowchart TD
    A[开始] --> B1[FtpService启动]
    B1 --> B2[连接FTP/SFTP服务器]
    B2 --> B3[下载ftpManualDir到manualDir]
    B3 --> B4[下载ftpDir到localDir]
    B4 --> B[检查导入目录]
    B --> C[查找CSV文件]
    C --> D{遍历所有CSV文件}
    D -->|文件存在且未在修改中| E[处理CSV文件内容]
    D -->|无更多文件| J[所有文件处理完毕]
    E --> F[映射数据到系统实体]
    F --> G1[保存数据到MDM持久化数据库]
    G1 --> G2[保存数据到RealTimeDB实时数据库]
    G2 --> I[将处理完的文件移动到Backup目录]
    I --> D
    J --> K[结束导入]
    B4 --> L[等待10分钟]
    L --> B2
```

### CSV文件处理详细流程

以月冻结数据文件`MONTH_20250107014716_DATA_MD_MONTHLY_LB_2_0.csv`为例，详细说明数据处理流程：

1. **文件扫描与检查**
   ```mermaid
   graph TD
       A[DataImportService.execute] -->|扫描ftp.localDir| B{检查文件}
       B -->|文件存在| C{检查修改时间}
       C -->|>300秒| D[创建DataFileHandler]
       C -->|≤300秒| E[跳过处理]
       B -->|文件不存在| E
   ```

   - DataImportService通过`execute()`方法启动导入流程
   - 使用`getFilePaths()`方法扫描指定目录下的CSV文件
   - 对每个文件进行以下检查：
     - 文件是否存在
     - 文件修改时间是否超过300秒（防止处理正在写入的文件）
     - 文件名是否符合命名规范（包含时间类型标识）

2. **文件内容解析**
   ```mermaid
   graph TD
       A[DataFileHandler.handleCsvFiles] -->|读取CSV文件| B[解析表头]
       B -->|获取sdp_id位置| C[解析数据列]
       C -->|分类处理| D[REG类型数据]
       C -->|分类处理| E[INTERVAL类型数据]
       C -->|分类处理| F[INST类型数据]
   ```

   - 使用CsvReader读取CSV文件内容
   - 解析表头信息：
     - 获取sdp_id列的位置
     - 根据列名前缀（REG_、INTERVAL_、INST_）分类处理数据
     - 构建HeaderInfo对象存储列信息
   - 数据分类处理：
     - REG类型：表码数据
     - INTERVAL类型：增量数据
     - INST类型：瞬时量数据

3. **数据映射与转换**
   ```mermaid
   graph TD
       A[DataFileHandler] -->|读取数据行| B[获取sdp_id]
       B -->|查询映射| C[MeterSdpMapService]
       C -->|返回映射| D[构建DataReg对象]
       D -->|设置字段| E[设置PK字段]
       D -->|设置字段| F[设置普通字段]
       D -->|设置字段| G[设置更新时间]
   ```

   - 逐行读取CSV数据：
     - 获取每行的sdp_id
     - 通过MeterSdpMapService查询对应的服务点ID
     - 如果找不到对应的服务点ID，记录错误并跳过该记录
   - 构建数据对象：
     - 根据数据类型创建相应的实体对象（DataReg或DataInstMinutely）
     - 设置主键字段（sdpId、tv等）
     - 设置普通字段（各种电能数据）
     - 设置更新时间
   - 数据验证：
     - 检查必要字段是否存在
     - 验证数据格式是否正确
     - 记录错误信息

4. **数据保存流程**
   ```mermaid
   graph TD
       A[DataFileHandler] -->|批量处理| B[数据去重]
       B -->|保存数据| C[调用rtDataAccessService]
       C -->|构建SQL| D[生成INSERT语句]
       D -->|执行插入| E[保存到数据库]
       E -->|失败| F[执行UPDATE]
       E -->|成功| G[更新日志]
   ```

   - 数据去重处理：
     - 使用TreeSet进行数据去重
     - 根据sdpId和tv构建唯一键
     - 保留最新的数据记录
   - 数据库操作：
     - 根据时间类型选择对应的数据表
     - 构建批量插入SQL语句
     - 执行插入操作
     - 如果插入失败，执行更新操作
   - 更新日志：
     - 记录数据更新日志
     - 更新最后处理时间
     - 记录处理结果统计

### 数据字段映射示例

以月冻结数据为例，CSV文件中的字段映射如下：

| CSV列名 | 实体类字段 | 说明 |
|---------|------------|------|
| sdp_id | dataRegPK.sdpId | 服务点ID |
| tv | dataRegPK.tv | 数据时间 |
| REG_R0P1 | r0p1 | 正向有功电能 |
| REG_R0P2 | r0p2 | 反向有功电能 |
| REG_R0P1A1 | r0p1a1 | 累计有功电能总和 |
| update_tv | updateTv | 更新时间 |

### 处理结果记录

系统会记录以下信息：
- 总记录数
- 成功导入记录数
- 处理时间
- 错误信息（如果有）

处理完成后，文件会被移动到Backup目录，文件名保持不变。

### 错误处理机制

1. **文件处理错误**：
   - 文件不存在时跳过处理
   - 文件正在写入时等待处理
   - 文件格式错误时记录日志

2. **数据映射错误**：
   - 找不到对应的服务点ID时记录错误
   - 数据格式不正确时跳过该记录
   - 必要字段缺失时记录错误

3. **数据库操作错误**：
   - 插入失败时尝试更新
   - 更新失败时记录错误
   - 批量操作失败时记录错误

4. **日志记录**：
   - 使用slf4j记录详细日志
   - 记录处理开始和结束时间
   - 记录错误信息和异常堆栈

## 4. MqBus - 消息队列总线

### 工作流程

1. 作为DataScan和DataCalc之间的通信中间件
2. 基于RocketMQ实现消息的发布与订阅
3. 使用`ServiceProducer`发送消息，`ServiceConsumer`接收消息
4. 消息格式为`MQMsg`，包含消息类型、来源、目标等信息

### 关键组件

- **ServiceProducer**：消息生产者
- **ServiceConsumer**：消息消费者
- **MQMsg**：消息载体

### MqBus消息流程图

```mermaid
graph TD
    subgraph 生产者
        DS[DataScan]
    end
    
    subgraph MqBus
        SP[ServiceProducer<br>消息生产]
        MQ[RocketMQ]
        SC[ServiceConsumer<br>消息消费]
    end
    
    subgraph 消费者
        DC[DataCalc]
    end
    
    DS -->|生成消息| SP
    SP -->|发送消息| MQ
    MQ -->|接收消息| SC
    SC -->|消费消息| DC
    DC -->|直接保存| RT[RealTimeDB]
```

## 5. DataGenerator - 数据生成器

作为数据源的补充，可以生成测试数据或模拟数据，用于系统测试或在无法获取实际数据时使用。

## 6. DataCalc - 数据计算

### 工作流程

1. 通过MqBus接收来自DataScan的数据消息
2. 根据不同的服务ID和处理类型，选择相应的处理器进行处理：
   - `1001`：数据验证处理（VALIDATION）
   - `1003`：电量计算处理（CALCENERGY）
   - `1004`：对象计算处理（CALCOBJ）
   - `1005`：自动估算处理（AUTOESTIMATION）
3. 每种处理类型使用专门的Handler进行处理
4. 处理结果直接保存到数据库
5. **计算进度管理**：
   - `CalcEnergyHandler`负责更新`mdm_data_calc_progress`表
   - 处理完数据后，创建`DataCalcProgress`记录并保存
   - 这些进度记录将用于下一次DataScan扫描时决定哪些数据已处理

### 主要处理器与进度追踪

1. **ValidationHandler**：
   - 处理数据验证，但不直接更新`mdm_data_calc_progress`表
   - 将验证完的数据传递给CalcEnergyHandler进行后续处理

2. **CalcEnergyHandler**：
   - 接收并处理来自DataScan的表码数据
   - 初始化时加载所有计算进度记录到内存中
   - 为每条处理的数据构建键：`sdpId + "#" + schemeId + "#" + loadType`
   - 处理完数据后，检查是否需要更新进度：
     ```java
     // 检查是否需要更新进度
     boolean updateProgress = true;
     if (mapDataCalcProgress.containsKey(key)) {
         Date lastTv = mapDataCalcProgress.get(key);
         if (lastTv.getTime() >= data.getDataEnergyPK().getTv().getTime()) {
             updateProgress = false;
         }
     }
     
     // 如果需要更新，创建新的进度记录
     if (updateProgress) {
         DataCalcProgress dataCalcProgress = new DataCalcProgress();
         dataCalcProgress.setSdpId(data.getDataEnergyPK().getSdpId());
         dataCalcProgress.setSchemeId(data.getDataEnergyPK().getSchemeId());
         dataCalcProgress.setTv(data.getDataEnergyPK().getTv());
         dataCalcProgress.setUpdateTv(new Date());
         dataCalcProgress.setLoadType("Register");
         dataCalcProgressQueue.put(dataCalcProgress);
         mapDataCalcProgress.put(key, dataCalcProgress.getTv());
     }
     ```
   - 使用专门的线程和队列批量保存进度记录：
     ```java
     // 批量保存进度记录
     private int dataCalcProgressSave(int count) {
         // 获取队列中的记录并排序
         List<DataCalcProgress> datas = new ArrayList<DataCalcProgress>();
         dataCalcProgressQueue.drainTo(datas, count);
         Collections.sort(datas, ...);
         
         // 调用数据访问服务批量更新
         boolean ret = dataAccessService.batchUpdateCalcProgressSave("mdm_data_calc_progress", datas);
     }
     ```

3. **CalcObjHandler**：
   - 处理对象计算，但不直接更新`mdm_data_calc_progress`表
   - 计算结果可能会传递给其他处理器

4. **EstimationAutoHandler**：
   - 处理自动估算，但不直接更新`mdm_data_calc_progress`表

### 进度表更新机制

CalcEnergyHandler实现了一个高效的批量更新机制：
1. 将需要更新的进度记录添加到`dataCalcProgressQueue`队列
2. 后台线程(`DataSaveProc`)定期检查队列并批量保存记录
3. 使用以下条件触发批量保存：
   - 队列中有超过100条记录
   - 距离上次保存超过15秒
4. 批量保存前对记录按时间进行排序，确保时间顺序一致性
5. 根据数据库类型（Oracle/MySQL）选择不同的更新策略：
   - Oracle: 使用MERGE语句
   - MySQL: 使用REPLACE INTO语句

这种批处理方式大大提高了系统性能，减少了数据库访问次数，同时确保了数据一致性。

### 进度表在DataScan和DataCalc之间的闭环

DataScan和DataCalc组件通过`mdm_data_calc_progress`表形成了一个完整的数据处理闭环：

1. DataScan在扫描开始时加载全部进度记录
2. 扫描时跳过已处理的数据，只发送未处理数据到MqBus
3. DataCalc处理数据并更新进度表
4. 下一次DataScan扫描时使用更新后的进度记录

这种设计确保了：
- 数据只被处理一次
- 系统可以从中断点恢复
- 资源被有效利用
- 各组件之间松耦合但功能协调

### 关键组件

- **HandlerService**：处理数据的服务类
- **HandlerFactory**：处理器工厂，创建不同类型的处理器
- **BaseHandler**：基础处理器接口
- 各类专门处理器：ValidationHandler, CalcEnergyHandler等

### DataCalc数据计算流程图

```mermaid
graph TD
    start[开始] --> listen[监听MqBus消息]
    listen --> receive[接收数据处理消息]
    receive --> identify{识别处理类型}
    
    identify -->|VALIDATION| valid[数据验证处理]
    identify -->|CALCENERGY| energy[电量计算处理]
    identify -->|CALCOBJ| object[对象计算处理]
    identify -->|AUTOESTIMATION| estimate[自动估算处理]
    
    valid & energy & object & estimate --> process[处理数据]
    process --> result[生成处理结果]
    result --> saveDB[直接保存到数据库]
    saveDB --> listen
```

## 7. RealTimeDB - 实时数据库

### 工作流程

1. 提供实时数据的临时存储和快速访问服务
2. 通过`ReloadRTDBService`定期从MDM持久化数据库加载数据到内存中
3. 每6小时执行一次数据加载（@Scheduled(initialDelay = 600000, fixedDelay = 6 * 3600 * 1000)）
4. 每天23点清理过期数据（@Scheduled(cron="0 0 23 * * ?")）
5. 向其他组件提供数据的快速查询和临时存储
6. 作为MDM持久化数据库的高速缓存，加速系统的数据访问效率

### 关键组件

- **ReloadRTDBService**：负责从MDM持久化数据库加载数据到实时内存数据库
- **RealTimeDbApplication**：应用程序入口
- **各种Repository接口**：提供数据访问服务

### MDM数据库表加载与清理

RealTimeDB作为实时内存数据库，通过定期从MDM持久化数据库加载数据并设置清理策略来保持高效运行。

#### MDM数据库表加载到内存的情况

| MDM持久化数据库表 | RealTimeDB实时表 | 数据类型 | 加载规则 |
|-------------------|------------------|----------|----------|
| mdm_asset_vee_rule | rt_mdm_asset_vee_rule | VEE规则 | 每6小时全量加载 |
| mdm_asset_vee_rule_data_source | rt_mdm_asset_vee_rule_data_source | VEE规则数据源 | 每6小时全量加载 |
| mdm_asset_vee_rule_param | rt_mdm_asset_vee_rule_param | VEE规则参数 | 每6小时全量加载 |
| vee_method | rt_vee_method | VEE方法 | 每6小时全量加载 |
| meter_data_storage_table | rt_meter_data_storage_table | 表数据存储表 | 每6小时全量加载 |
| meter_data_storage_info | rt_meter_data_storage_info | 表数据存储信息 | 每6小时全量加载 |
| sys_dataitem_export_key | rt_sys_dataitem_export_key | 数据项导出键 | 每6小时全量加载 |
| data_di_export_progress | rt_data_di_export_progress | 数据导出进度 | 每6小时全量加载 |
| sys_dataitem_export | rt_sys_dataitem_export | 数据项导出 | 每6小时全量加载 |
| dict_detail | rt_dict_detail | 字典详情 | 每6小时全量加载 |
| mdm_asset_calc_scheme | rt_mdm_asset_calc_scheme | 计算档案方案 | 每6小时全量加载 |
| mdm_asset_calc_obj | rt_mdm_asset_calc_obj | 计算档案对象 | 每6小时全量加载 |
| mdm_data_update_log | rt_mdm_data_update_log | 数据更新日志 | 每6小时全量加载 |
| mdm_asset_service_point | rt_mdm_asset_service_point | 服务点档案 | 每6小时全量加载 |
| asset_meter | rt_asset_meter | 表计档案 | 每6小时全量加载 |
| asset_hes | rt_asset_hes | HES档案 | 每6小时全量加载 |
| mdm_asset_meter_replacement | rt_mdm_asset_meter_replacement | 表计更换记录 | 每6小时全量加载 |

#### 需要定期清理的实时表

| 实时表 | 原始MDM表 | 数据类型 | 清理规则 |
|--------|-----------|----------|----------|
| mdm_data_reg_dayly | mdm_data_reg_dayly | 日度表码数据 | 15天前数据删除 |
| mdm_data_interval_dayly | mdm_data_interval_dayly | 日度增量数据 | 15天前数据删除 |
| mdm_data_reg_monthly | mdm_data_reg_monthly | 月度表码数据 | 12个月前数据删除 |
| mdm_data_interval_monthly | mdm_data_interval_monthly | 月度增量数据 | 12个月前数据删除 |
| mdm_data_reg_minutely | mdm_data_reg_minutely | 分钟级表码数据 | 3天前数据删除 |
| mdm_data_interval_minutely | mdm_data_interval_minutely | 分钟级增量数据 | 3天前数据删除 |
| mdm_data_inst_minutely | mdm_data_inst_minutely | 分钟级瞬时量数据 | 3天前数据删除 |
| mdm_data_vee_event | mdm_data_vee_event | VEE事件数据 | 3个月前数据删除 |
| mdm_data_energy_minutely | mdm_data_energy_minutely | 分钟级电量数据 | 已实现清理接口但未被调用 |
| mdm_data_energy_dayly | mdm_data_energy_dayly | 日度电量数据 | 已实现清理接口但未被调用 |
| mdm_data_energy_monthly | mdm_data_energy_monthly | 月度电量数据 | 已实现清理接口但未被调用 |

### 定时调度详情

1. **数据加载调度**：
   ```java
   @Scheduled(initialDelay = 600000, fixedDelay = 6 * 3600 * 1000)
   public void loadRealTimeDB() {
       // 从MDM持久化数据库加载数据到实时内存数据库
   }
   ```
   - 初始延迟10分钟（600000毫秒），之后每6小时（6 * 3600 * 1000毫秒）执行一次
   - 从MDM加载各种基础配置数据和参考数据到实时内存数据库中

2. **数据清理调度**：
   ```java
   @Scheduled(cron="0 0 23 * * ?")
   private void clearExpiredRTDB() {
       // 清理过期数据
   }
   ```
   - 每天23:00执行清理任务
   - 根据不同数据类型设置不同的过期时间
   - 日度数据保留最近15天
   - 月度数据保留最近12个月
   - 分钟级数据保留最近3天
   - VEE事件数据保留最近3个月

### 实时数据库的优势

1. **性能优化**：
   - 内存数据库访问速度快
   - 配置数据全量加载，保证完整性
   - 业务数据定期清理，保持轻量高效

2. **数据一致性**：
   - 写入操作同时保存到MDM和RealTimeDB
   - 定期从MDM同步数据，确保数据一致性
   - 核心配置数据变更时自动刷新

3. **资源管理**：
   - 历史数据在MDM持久化存储
   - 活跃数据在RealTimeDB快速访问
   - 分级存储策略优化系统资源使用

### RealTimeDB加载流程图

```mermaid
graph TD
    A[ReloadRTDBService定时触发] --> B[检查系统初始化状态]
    B --> C{是否已初始化?}
    C -->|否| D[跳过加载]
    C -->|是| E[清理过期数据]
    E --> F[开始数据加载]
    F --> G1[加载VEE规则数据]
    G1 --> G2[加载存储配置数据]
    G2 --> G3[加载数据导出配置]
    G3 --> G4[加载字典数据]
    G4 --> G5[加载计算档案方案]
    G5 --> G6[加载档案数据]
    G6 --> H[数据加载完成]
```

## 8. MDM数据库 - 持久化存储

### 工作流程

1. 作为系统的主要持久化存储，存储所有业务数据
2. 接收并保存从DataImport导入的数据
3. 接收并保存DataCalc处理后的计算结果
4. 为DataScan提供数据扫描源
5. 为RealTimeDB提供数据同步来源
6. 保存系统的所有历史数据和当前数据

### 关键组件

- **各种Persistence Repository**：提供持久化数据访问服务
- **数据表**：存储各类业务数据，包括表码数据、增量数据、事件数据等

### 系统完整时序图

```mermaid
sequenceDiagram
    participant HES as HES数据库
    participant DE as DataExport
    participant FDIR as FTP目录(远程)
    participant DI as DataImport
    participant MDM as MDM数据库
    participant RT as RealTimeDB
    participant DS as DataScan
    participant MB as MqBus
    participant DC as DataCalc
    
    %% 数据导出阶段
    HES->>DE: 触发数据导出
    DE->>DE: 查询导出配置
    DE->>HES: 查询数据
    HES-->>DE: 返回数据
    DE->>DE: 生成CSV文件
    
    %% 文件上传与FTP下载
    DE->>DE: 保存CSV文件到本地目录
    DE->>FDIR: 上传CSV文件到FTP服务器(ftpDir)
    
    %% DataImport处理路径
    DI->>FDIR: 从FTP服务器下载CSV文件到ftp.localDir
    DI->>FDIR: 从FTP服务器下载手动文件到ftp.manualDir
    DI->>DI: 扫描ftp.localDir目录
    DI->>DI: 发现新CSV文件(修改时间>300秒)
    DI->>DI: 读取CSV文件
    DI->>DI: 解析数据
    DI->>MDM: 保存数据到MDM数据库
    DI->>RT: 同时保存数据到RealTimeDB
    DI->>DI: 移动文件到Backup目录
    
    %% 手动导入文件处理
    DI->>DI: 扫描ftp.manualDir目录
    DI->>DI: 发现新CSV文件(修改时间>600秒)
    DI->>DI: 读取CSV文件并解析数据类型
    DI->>DI: 解析数据
    DI->>MDM: 保存数据到MDM数据库
    DI->>RT: 同时保存数据到RealTimeDB
    DI->>DI: 移动文件到Backup目录
    
    %% 数据同步到实时库
    MDM->>RT: 定期加载数据(每6小时)
    
    %% DataScan处理路径
    DS->>MDM: 定时扫描数据库
    DS->>DS: 查询不同时间粒度的数据
    DS->>MB: 发送数据消息
    
    %% 数据处理阶段
    MB->>DC: 转发数据消息
    DC->>DC: 识别处理类型
    
    alt 数据验证
        DC->>DC: 执行数据验证
    else 电量计算
        DC->>DC: 执行电量计算
    else 对象计算
        DC->>DC: 执行对象计算
    else 自动估算
        DC->>DC: 执行自动估算
    end
    
    DC->>MB: 发送处理结果
    
    %% 数据存储阶段
    DC->>MDM: 保存处理结果到MDM数据库
    DC->>RT: 同时保存处理结果到RealTimeDB
    
    %% 数据应用阶段
    Note over MDM,RT: 数据可用于<br>实时监控/分析/报表
```

### 组件详细时序图

#### 1. DataExport详细时序图

```mermaid
sequenceDiagram
    participant Scheduler as 调度器
    participant DE as DataExportService
    participant Repo as rtSysDataitemExportRepository
    participant Storage as MeterDataStorageInfo
    participant Table as MeterDataStorageTable
    participant Handler as DataTableHandler
    participant DB as HES数据库
    participant File as CSV文件
    
    Scheduler->>DE: 触发导出任务
    DE->>Repo: 查询导出配置(exportFlag=1)
    Repo-->>DE: 返回数据项列表
    
    loop 每个数据项
        DE->>Storage: 获取存储信息
        Storage-->>DE: 返回存储配置
        DE->>Table: 获取表信息
        Table-->>DE: 返回表结构
        
        DE->>Handler: 创建DataTableHandler
        Handler->>DB: 构建SQL查询
        DB-->>Handler: 返回查询结果
        Handler->>Handler: 格式化数据
        Handler->>File: 写入CSV文件
    end
    
    DE->>DE: 导出完成
```

#### 2. DataScan详细时序图

```mermaid
sequenceDiagram
    participant Scheduler as 调度器
    participant DS as DataScanService
    participant DB as 数据库
    participant MQ as 消息队列
    participant RT as 实时数据库
    
    Scheduler->>DS: 触发扫描任务
    DS->>DS: 确定扫描时间范围
    DS->>DB: 扫描月度数据(过去6个月)
    DB-->>DS: 返回月度数据
    DS->>MQ: 发送月度数据到MqBus
    
    DS->>DB: 扫描日度数据(过去30天)
    DB-->>DS: 返回日度数据
    DS->>MQ: 发送日度数据到MqBus
    
    DS->>DB: 扫描分钟级数据(过去7天)
    DB-->>DS: 返回分钟级数据
    DS->>MQ: 发送分钟级数据到MqBus
    
    DS->>DB: 扫描VEE事件数据(过去1个月)
    DB-->>DS: 返回VEE事件数据
    DS->>MQ: 发送VEE事件数据到MqBus
    
    DS->>RT: 更新服务状态
    RT-->>DS: 确认状态更新
    
    DS->>DS: 扫描完成
```

#### 3. DataImport详细时序图

```mermaid
sequenceDiagram
    participant Scheduler as 调度器
    participant FTP as FtpService
    participant Remote as 远程FTP/SFTP服务器
    participant DI as DataImportService
    participant Handler as DataFileHandler
    participant Map as MeterSdpMapService
    participant DB as 数据库
    participant File as 文件系统
    
    %% FTP下载过程
    Scheduler->>FTP: 启动FTP服务
    FTP->>Remote: 连接FTP/SFTP服务器
    Remote-->>FTP: 连接成功
    FTP->>Remote: 请求下载ftpManualDir目录文件
    Remote-->>FTP: 返回文件列表
    FTP->>File: 保存文件到本地manualDir
    FTP->>Remote: 请求下载ftpDir目录文件
    Remote-->>FTP: 返回文件列表
    FTP->>File: 保存文件到本地localDir
    
    %% 数据导入过程
    Scheduler->>DI: 触发导入任务
    
    %% 处理localDir目录
    DI->>File: 扫描ftp.localDir目录
    File-->>DI: 返回文件列表
    
    loop 每个localDir中的CSV文件
        DI->>File: 检查文件状态
        File-->>DI: 返回文件信息
        
        alt 文件可处理(修改时间>300秒)
            DI->>Handler: 创建DataFileHandler
            Handler->>File: 读取CSV文件
            File-->>Handler: 返回文件内容
            Handler->>Handler: 解析CSV数据
            
            loop 每条数据记录
                Handler->>Map: 获取表计映射关系
                Map-->>Handler: 返回映射信息
                Handler->>Handler: 构建数据实体
            end
            
            Handler->>DB: 保存数据到数据库
            Handler->>File: 移动文件到Backup目录
        else 文件不可处理
            DI->>DI: 跳过处理
        end
    end
    
    %% 处理manualDir目录
    DI->>File: 扫描ftp.manualDir目录
    File-->>DI: 返回文件列表
    
    loop 每个manualDir中的CSV文件
        DI->>File: 检查文件状态
        File-->>DI: 返回文件信息
        
        alt 文件可处理(修改时间>600秒)
            DI->>DI: 解析文件名获取数据类型
            DI->>Handler: 创建DataFileHandler
            Handler->>File: 读取CSV文件
            File-->>Handler: 返回文件内容
            Handler->>Handler: 解析CSV数据(带数据类型)
            
            loop 每条数据记录
                Handler->>Map: 获取表计映射关系
                Map-->>Handler: 返回映射信息
                Handler->>Handler: 构建数据实体
            end
            
            Handler->>DB: 保存数据到数据库
            Handler->>File: 移动文件到Backup目录
        else 文件不可处理
            DI->>DI: 跳过处理
        end
    end
    
    DI->>DI: 导入完成
```

#### 4. DataCalc详细时序图

```mermaid
sequenceDiagram
    participant MB as MqBus
    participant DC as DataCalcService
    participant Factory as HandlerFactory
    participant Validator as ValidationHandler
    participant Energy as CalcEnergyHandler
    participant Object as CalcObjHandler
    participant Estimate as AutoEstimationHandler
    participant DB as 数据库
    
    MB->>DC: 接收数据消息
    DC->>DC: 解析消息内容
    
    alt 数据验证(1001)
        DC->>Factory: 创建ValidationHandler
        Factory-->>DC: 返回处理器
        DC->>Validator: 执行数据验证
        Validator-->>DC: 返回验证结果
    else 电量计算(1003)
        DC->>Factory: 创建CalcEnergyHandler
        Factory-->>DC: 返回处理器
        DC->>Energy: 执行电量计算
        Energy-->>DC: 返回计算结果
    else 对象计算(1004)
        DC->>Factory: 创建CalcObjHandler
        Factory-->>DC: 返回处理器
        DC->>Object: 执行对象计算
        Object-->>DC: 返回计算结果
    else 自动估算(1005)
        DC->>Factory: 创建AutoEstimationHandler
        Factory-->>DC: 返回处理器
        DC->>Estimate: 执行自动估算
        Estimate-->>DC: 返回估算结果
    end
    
    DC->>DB: 直接保存处理结果到数据库
```

### CSV文件处理流程示例

以月冻结数据文件`MONTH_20250107014716_DATA_MD_MONTHLY_LB_2_0.csv`为例，详细说明数据处理流程：

1. **文件扫描与检查**
   ```mermaid
   graph TD
       A[DataImportService.execute] -->|扫描ftp.localDir| B{检查文件}
       B -->|文件存在| C{检查修改时间}
       C -->|>300秒| D[创建DataFileHandler]
       C -->|≤300秒| E[跳过处理]
       B -->|文件不存在| E
   ```

2. **文件内容解析**
   ```mermaid
   graph TD
       A[DataFileHandler.handleCsvFiles] -->|读取CSV文件| B[解析表头]
       B -->|获取sdp_id位置| C[解析数据列]
       C -->|分类处理| D[REG类型数据]
       C -->|分类处理| E[INTERVAL类型数据]
       C -->|分类处理| F[INST类型数据]
   ```

3. **数据映射与转换**
   ```mermaid
   graph TD
       A[DataFileHandler] -->|读取数据行| B[获取sdp_id]
       B -->|查询映射| C[MeterSdpMapService]
       C -->|返回映射| D[构建DataReg对象]
       D -->|设置字段| E[设置PK字段]
       D -->|设置字段| F[设置普通字段]
       D -->|设置字段| G[设置更新时间]
   ```

4. **数据保存流程**
   ```mermaid
   graph TD
       A[DataFileHandler] -->|批量处理| B[数据去重]
       B -->|保存数据| C[调用rtDataAccessService]
       C -->|构建SQL| D[生成INSERT语句]
       D -->|执行插入| E[保存到数据库]
       E -->|失败| F[执行UPDATE]
       E -->|成功| G[更新日志]
   ```

#### 具体处理步骤

1. **文件扫描阶段**
   - DataImportService扫描ftp.localDir目录
   - 检查文件是否存在
   - 检查文件修改时间是否超过300秒
   - 创建DataFileHandler处理文件

2. **文件解析阶段**
   - 使用CsvReader读取CSV文件
   - 解析表头信息，获取各列位置
   - 根据列名前缀（REG_、INTERVAL_、INST_）分类处理数据
   - 获取sdp_id列位置

3. **数据映射阶段**
   - 读取每行数据的sdp_id
   - 通过MeterSdpMapService查询对应的服务点ID
   - 构建DataReg对象
   - 设置主键字段（sdpId、tv等）
   - 设置普通字段（各种电能数据）
   - 设置更新时间

4. **数据保存阶段**
   - 对数据进行去重处理
   - 构建SQL插入语句
   - 尝试批量插入数据
   - 如果插入失败，执行更新操作
   - 更新数据更新日志
   - 移动处理完的文件到Backup目录

#### 数据字段映射示例

以月冻结数据为例，CSV文件中的字段映射如下：

| CSV列名 | 实体类字段 | 说明 |
|---------|------------|------|
| sdp_id | dataRegPK.sdpId | 服务点ID |
| tv | dataRegPK.tv | 数据时间 |
| REG_R0P1 | r0p1 | 正向有功电能 |
| REG_R0P2 | r0p2 | 反向有功电能 |
| REG_R0P1A1 | r0p1a1 | 累计有功电能总和 |
| update_tv | updateTv | 更新时间 |

#### 处理结果记录

系统会记录以下信息：
- 总记录数
- 成功导入记录数
- 处理时间
- 错误信息（如果有）

处理完成后，文件会被移动到Backup目录，文件名保持不变。 
