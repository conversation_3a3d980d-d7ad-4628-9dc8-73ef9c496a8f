package clouesp.hes.common.DataRepository.Persistence.Data;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import clouesp.hes.common.DataEntity.Data.DataRegMinutely;
import clouesp.hes.common.DataEntity.Data.DataRegPK;

public interface DataRegMinutelyRepository extends JpaRepository<DataRegMinutely, DataRegPK>{
	List<DataRegMinutely> findByDataRegPKSdpIdAndDataRegPKTvIn(
			String sdpId, 
			List<Date> tvs
			);		
}
