package clouesp.hes.common.DataRepository.RealTime.Data;

import java.util.Date;
import java.util.List;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import clouesp.hes.common.DataEntity.Data.DataRegDaily;
import clouesp.hes.common.DataEntity.Data.DataRegPK;

public interface RtDataRegDailyRepository extends JpaRepository<DataRegDaily, DataRegPK>{
	List<DataRegDaily> findByDataRegPKSdpIdAndDataRegPKTvIn(
			String sdpId, 
			List<Date> tvs
			);		

	@Modifying
	@Transactional
	@Query(value = "delete from mdm_data_reg_dayly where tv < :tv"
			, nativeQuery = true)
	void clearExpired(
			@Param("tv")Date tv
			);
}
