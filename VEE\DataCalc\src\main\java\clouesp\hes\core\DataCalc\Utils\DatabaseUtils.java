package clouesp.hes.core.DataCalc.Utils;

import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

@Slf4j
public class DatabaseUtils {
	private static DatabaseUtils instance = null;
	private DatabaseUtils() {
		
	}
	public static DatabaseUtils getInstance() {
		if (instance == null) {
			instance = new DatabaseUtils();
		}
		return instance;
	}	
	
	public ResultSet queryForFetch(Connection conn, Statement st, String sql, int fs) {
		ResultSet rs = null;
		try {
			conn.setAutoCommit(false);
			fs = fs > st.getMaxRows() ? fs : st.getMaxRows();
			st.setFetchSize(fs);
			st.setFetchDirection(ResultSet.FETCH_FORWARD);
			rs = st.executeQuery(sql);
			st.setFetchSize(fs);
		} catch (SQLException e) {
			log.error("DataExport Error : " , e);
		}
		return rs;
	}	
}
