import clouesp.hes.common.CommonUtils.ObjectUtils
import clouesp.hes.common.DataEntity.Data.DataCalcObj
import clouesp.hes.common.DataEntity.Data.DataVEEEvent

def lossRateExceedsThreshold(Map<String, Object> ruleInfo) {
    Integer result = 0;
    DataVEEEvent event = null;
    if (ruleInfo == null ||
            !ruleInfo.containsKey("datas") ||
            !ruleInfo.containsKey("params")) {
        return null;
    }

    List<Map<String, Object>> ruleDatas = ruleInfo.get("datas");
    Map<String, Object> ruleParams = ruleInfo.get("params");
    double paramValue1 = -2;
    double paramValue2 = 2;
    if (ruleParams != null &&
            ruleParams.containsKey("paramValue1") &&
            ruleParams.containsKey("paramValue2")) {
        paramValue1 = ruleParams.get("paramValue1");
        paramValue2 = ruleParams.get("paramValue2");
    }
    for(Map<String, Object> ruleData : ruleDatas) {
        String targetClass = ruleData.get("targetClass");
        List<Map<String, Object>> dataList = ruleData.get("lossDataListDay");
        if(dataList == null) {
            return null;
        }
        if("LOSS" == targetClass) {
            if(dataList.size() > 0) {
                for(Map<String, Object> data : dataList) {
                    DataCalcObj dataCalcObj = ObjectUtils.convertMapToObject(data, DataCalcObj.class);
                    Double calcValue = dataCalcObj.getCalcValue()
                    if(calcValue != null) {
                        if (calcValue.doubleValue() < paramValue1) {
                            result = 1
                        } else if (calcValue.doubleValue() > paramValue2) {
                            result = 1
                        }
                    }
                }
            }
        }
    }

    if(result.intValue() == 1) {
        event = (DataVEEEvent) ruleInfo.get("event");
    }
    return event;
}