package clouesp.hes.common.DataModel.api;

import java.util.Date;

import io.swagger.annotations.ApiModel;

@ApiModel(value="mdmDataVeeBase",description="修补的数据")
public class MdmDataVeeBase {
	/**sdpId*/
	private java.lang.String sdpId;	
	/**tv*/
	private java.util.Date tv;
	/**r0p1*/
	private java.math.BigDecimal r0p1;
	/**r0p2*/
	private java.math.BigDecimal r0p2;
	/**r0p3*/
	private java.math.BigDecimal r0p3;
	/**r0p4*/
	private java.math.BigDecimal r0p4;
	/**r0p5*/
	private java.math.BigDecimal r0p5;
	/**r0p6*/
	private java.math.BigDecimal r0p6;
	/**r0p7*/
	private java.math.BigDecimal r0p7;
	/**r0p8*/
	private java.math.BigDecimal r0p8;
	/**r1p1*/
	private java.math.BigDecimal r1p1;
	/**r1p2*/
	private java.math.BigDecimal r1p2;
	/**r1p3*/
	private java.math.BigDecimal r1p3;
	/**r1p4*/
	private java.math.BigDecimal r1p4;
	/**r2p1*/
	private java.math.BigDecimal r2p1;
	/**r2p2*/
	private java.math.BigDecimal r2p2;
	/**r2p3*/
	private java.math.BigDecimal r2p3;
	/**r2p4*/
	private java.math.BigDecimal r2p4;
	/**r3p1*/
	private java.math.BigDecimal r3p1;
	/**r3p2*/
	private java.math.BigDecimal r3p2;
	/**r3p3*/
	private java.math.BigDecimal r3p3;
	/**r3p4*/
	private java.math.BigDecimal r3p4;
	/**r4p1*/
	private java.math.BigDecimal r4p1;
	/**r4p2*/
	private java.math.BigDecimal r4p2;
	/**r4p3*/
	private java.math.BigDecimal r4p3;
	/**r4p4*/
	private java.math.BigDecimal r4p4;
	
	private String schemeId;
	private java.math.BigDecimal schemeType;	
	private Integer dataType;
	private Integer dataVersion;
	
	private Date updateTv;
	private String operatorId;
	private String operatorName;
	private String operatorIp;
	private String operatorReason;
	
	public java.lang.String getSdpId() {
		return sdpId;
	}
	public void setSdpId(java.lang.String sdpId) {
		this.sdpId = sdpId;
	}
	public java.util.Date getTv() {
		return tv;
	}
	public void setTv(java.util.Date tv) {
		this.tv = tv;
	}
	public java.math.BigDecimal getR0p1() {
		return r0p1;
	}
	public void setR0p1(java.math.BigDecimal r0p1) {
		this.r0p1 = r0p1;
	}
	public java.math.BigDecimal getR0p2() {
		return r0p2;
	}
	public void setR0p2(java.math.BigDecimal r0p2) {
		this.r0p2 = r0p2;
	}
	public java.math.BigDecimal getR0p3() {
		return r0p3;
	}
	public void setR0p3(java.math.BigDecimal r0p3) {
		this.r0p3 = r0p3;
	}
	public java.math.BigDecimal getR0p4() {
		return r0p4;
	}
	public void setR0p4(java.math.BigDecimal r0p4) {
		this.r0p4 = r0p4;
	}
	public java.math.BigDecimal getR0p5() {
		return r0p5;
	}
	public void setR0p5(java.math.BigDecimal r0p5) {
		this.r0p5 = r0p5;
	}
	public java.math.BigDecimal getR0p6() {
		return r0p6;
	}
	public void setR0p6(java.math.BigDecimal r0p6) {
		this.r0p6 = r0p6;
	}
	public java.math.BigDecimal getR0p7() {
		return r0p7;
	}
	public void setR0p7(java.math.BigDecimal r0p7) {
		this.r0p7 = r0p7;
	}
	public java.math.BigDecimal getR0p8() {
		return r0p8;
	}
	public void setR0p8(java.math.BigDecimal r0p8) {
		this.r0p8 = r0p8;
	}
	public java.math.BigDecimal getR1p1() {
		return r1p1;
	}
	public void setR1p1(java.math.BigDecimal r1p1) {
		this.r1p1 = r1p1;
	}
	public java.math.BigDecimal getR1p2() {
		return r1p2;
	}
	public void setR1p2(java.math.BigDecimal r1p2) {
		this.r1p2 = r1p2;
	}
	public java.math.BigDecimal getR1p3() {
		return r1p3;
	}
	public void setR1p3(java.math.BigDecimal r1p3) {
		this.r1p3 = r1p3;
	}
	public java.math.BigDecimal getR1p4() {
		return r1p4;
	}
	public void setR1p4(java.math.BigDecimal r1p4) {
		this.r1p4 = r1p4;
	}
	public java.math.BigDecimal getR2p1() {
		return r2p1;
	}
	public void setR2p1(java.math.BigDecimal r2p1) {
		this.r2p1 = r2p1;
	}
	public java.math.BigDecimal getR2p2() {
		return r2p2;
	}
	public void setR2p2(java.math.BigDecimal r2p2) {
		this.r2p2 = r2p2;
	}
	public java.math.BigDecimal getR2p3() {
		return r2p3;
	}
	public void setR2p3(java.math.BigDecimal r2p3) {
		this.r2p3 = r2p3;
	}
	public java.math.BigDecimal getR2p4() {
		return r2p4;
	}
	public void setR2p4(java.math.BigDecimal r2p4) {
		this.r2p4 = r2p4;
	}
	public java.math.BigDecimal getR3p1() {
		return r3p1;
	}
	public void setR3p1(java.math.BigDecimal r3p1) {
		this.r3p1 = r3p1;
	}
	public java.math.BigDecimal getR3p2() {
		return r3p2;
	}
	public void setR3p2(java.math.BigDecimal r3p2) {
		this.r3p2 = r3p2;
	}
	public java.math.BigDecimal getR3p3() {
		return r3p3;
	}
	public void setR3p3(java.math.BigDecimal r3p3) {
		this.r3p3 = r3p3;
	}
	public java.math.BigDecimal getR3p4() {
		return r3p4;
	}
	public void setR3p4(java.math.BigDecimal r3p4) {
		this.r3p4 = r3p4;
	}
	public java.math.BigDecimal getR4p1() {
		return r4p1;
	}
	public void setR4p1(java.math.BigDecimal r4p1) {
		this.r4p1 = r4p1;
	}
	public java.math.BigDecimal getR4p2() {
		return r4p2;
	}
	public void setR4p2(java.math.BigDecimal r4p2) {
		this.r4p2 = r4p2;
	}
	public java.math.BigDecimal getR4p3() {
		return r4p3;
	}
	public void setR4p3(java.math.BigDecimal r4p3) {
		this.r4p3 = r4p3;
	}
	public java.math.BigDecimal getR4p4() {
		return r4p4;
	}
	public void setR4p4(java.math.BigDecimal r4p4) {
		this.r4p4 = r4p4;
	}
	public String getSchemeId() {
		return schemeId;
	}	
	public java.math.BigDecimal getSchemeType() {
		return schemeType;
	}
	public void setSchemeType(java.math.BigDecimal schemeType) {
		this.schemeType = schemeType;
	}
	public void setSchemeId(String schemeId) {
		this.schemeId = schemeId;
	}
	public Integer getDataType() {
		return dataType;
	}
	public void setDataType(Integer dataType) {
		this.dataType = dataType;
	}
	public Integer getDataVersion() {
		return dataVersion;
	}
	public void setDataVersion(Integer dataVersion) {
		this.dataVersion = dataVersion;
	}
	public Date getUpdateTv() {
		return updateTv;
	}
	public void setUpdateTv(Date updateTv) {
		this.updateTv = updateTv;
	}
	public String getOperatorId() {
		return operatorId;
	}
	public void setOperatorId(String operatorId) {
		this.operatorId = operatorId;
	}	
	public String getOperatorName() {
		return operatorName;
	}
	public void setOperatorName(String operatorName) {
		this.operatorName = operatorName;
	}
	public String getOperatorIp() {
		return operatorIp;
	}
	public void setOperatorIp(String operatorIp) {
		this.operatorIp = operatorIp;
	}
	public String getOperatorReason() {
		return operatorReason;
	}
	public void setOperatorReason(String operatorReason) {
		this.operatorReason = operatorReason;
	}	
}
