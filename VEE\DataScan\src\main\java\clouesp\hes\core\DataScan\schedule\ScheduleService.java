package clouesp.hes.core.DataScan.schedule;

import clouesp.hes.core.DataScan.Utils.SpringBeanUtils;
import jline.internal.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

@Service("scheduleService")
public class ScheduleService {
    @Autowired
    private CronTaskRegistrar cronTaskRegistrar;

    public void startSchedule(String serviceId, String startTv, int cycle, String beanName) throws Exception{
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = sdf.parse(startTv);
        Date endTime = sdf.parse("2020-12-31 23:59:59");
        String taskCycleType = "hourly";
        int taskCycle = cycle;

        Object target = SpringBeanUtils.getBean(beanName);
        Method method;
        method = target.getClass().getDeclaredMethod("init", serviceId.getClass());
        method.invoke(target, serviceId);

        Calendar cal = Calendar.getInstance();
        cal.setTime(startTime);
        int startSec = cal.get(Calendar.SECOND);
        int startMin = cal.get(Calendar.MINUTE);
        int startHour = cal.get(Calendar.HOUR_OF_DAY);
        int startDay = cal.get(Calendar.DAY_OF_MONTH);

        cal.setTime(endTime);
        int endHour = cal.get(Calendar.HOUR_OF_DAY);
        String cron = null;

        if (taskCycleType.equalsIgnoreCase("minutely")) {
            String timeSecs = startSec + " ";
            String timeMins = startMin + "/" + taskCycle + " ";
            String timeHours = startHour + "-" + endHour + " ";
            cron = timeSecs + timeMins + timeHours
                    + "* * ?";
        }
        else if (taskCycleType.equalsIgnoreCase("hourly")) {
            String timeSecs = startSec + " ";
            String timeMins = startMin + " ";
            String timeHours = startHour + "-" + endHour + "/" + taskCycle + " ";
            cron = timeSecs + timeMins + timeHours
                    + "* * ?";
        }
        else if (taskCycleType.equalsIgnoreCase("daily")) {
            String timeSecs = startSec + " ";
            String timeMins = startMin + " ";
            String timeHours = startHour + " ";
            String timeDays = startDay + "/" + taskCycle + " ";

            cron = timeSecs + timeMins + timeHours + timeDays
                    + "* ?";

        }
        else if (taskCycleType.equalsIgnoreCase("monthly")) {
            String timeSecs = startSec + " ";
            String timeMins = startMin + " ";
            String timeHours = startHour + " ";
            String timeDays = startDay + " ";
            String timeMonths = "1/" + taskCycle + " ";
            cron = timeSecs + timeMins + timeHours + timeDays + timeMonths
                    + "?";
        }

        if (cron != null) {
            SchedulingRunnable task = new SchedulingRunnable(beanName, "dispatch");
            cronTaskRegistrar.addCronTask(task, cron);
            Log.info("addCronTask: beanName ="  + beanName + " , cron = " + cron);
        }
    }
}
