package clouesp.hes.common.DataRepository.RealTime.System;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;


import clouesp.hes.common.DataEntity.System.MdmSysServiceAttribute;
import clouesp.hes.common.DataEntity.System.MdmSysServiceAttributePK;

public interface RtMdmSysServiceAttributeRepository extends JpaRepository<MdmSysServiceAttribute, MdmSysServiceAttributePK>{
	@Query(value = "select attribute_value from mdm_sys_service_attribute where "
			+ "id = :id and attribute_name = :attributeName "
			, nativeQuery = true)
	String findValue(
			@Param("id") String id,
			@Param("attributeName") String attributeName
			);
}
