package clouesp.hes.common.DataEntity.Statistics;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;
@Data
@Embeddable
public class ObjectStatisticsPK implements Serializable {
    private static final long serialVersionUID = 3424867071446909444L;
    @Column(name = "Object_id",nullable = false,columnDefinition = "varchar(32)")
    private String ObjectId ;
    @Column(name="org_id",nullable = false,columnDefinition ="varchar(32)")
    private String orgId ;
}
