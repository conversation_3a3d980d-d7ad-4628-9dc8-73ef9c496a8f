package clouesp.hes.core.DataImport.handler;

import clouesp.hes.common.DataEntity.Data.DataVEEEvent;
import clouesp.hes.common.DataEntity.Data.DataVEEEventPK;
import clouesp.hes.common.DataRepository.Persistence.Data.DataVEEEventRepository;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetVEERuleDataSourceRepository;
import clouesp.hes.common.DataRepository.RealTime.Data.RtDataVEEEventRepository;
import clouesp.hes.common.logger.logger.Logger;
import clouesp.hes.common.logger.logger.LoggerLevel;
import clouesp.hes.core.DataImport.config.ServerConfig;
import clouesp.hes.core.DataImport.service.Data.MeterSdpMapService;
import clouesp.hes.core.DataImport.utils.FileUtils;
import com.csvreader.CsvReader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
@Slf4j
@Component
public class EventFileHandler {
	private EventSaveProc eventSaveProc = null;
	private Thread eventSaveProcThread = null;	
	private long lastSaveTime = System.currentTimeMillis();

	@Autowired
	private RtMdmAssetVEERuleDataSourceRepository rtMdmAssetVEERuleDataSourceRepository;

	@Autowired
    private ServerConfig serverConfig;

	@Autowired
	private MeterSdpMapService meterSdpMapService ;

	@Autowired
	private DataVEEEventRepository dataVEEEventRepository;
	@Autowired
	private RtDataVEEEventRepository rtDataVEEEventRepository;


	private SimpleDateFormat sdflog = new SimpleDateFormat("MM-dd HH:mm:ss");
	
	private LinkedBlockingQueue<DataVEEEvent> eventQueue = new LinkedBlockingQueue<DataVEEEvent>();	
		
	public void init(String serviceId) {	
		if(eventSaveProcThread == null) {
			eventSaveProc = new EventSaveProc();
			eventSaveProcThread = new Thread(eventSaveProc);
			eventSaveProcThread.start();				
		}
	}

	public void handleCsvFiles(String filePath) {
		try {
			log.info("Start processing event files : " + filePath);
			CsvReader csvReader = new CsvReader(filePath, ',', Charset.forName("UTF-8"));
			List<DataVEEEvent> dataVEEEvents = new ArrayList<DataVEEEvent>();
			Date updateTv = new Date();
			List<String> errorMeterSdps = new ArrayList<>() ;
			String curSdpId = null ;
			boolean isHeader = true;
			while (csvReader.readRecord()) {					
				String[] values = csvReader.getValues();
				if(isHeader) {
					isHeader = false;
					continue;
				}

				String sn = values[0];
				curSdpId = meterSdpMapService.getSdpIdbyMeterSn(sn);
				if ( curSdpId == null ){
					if ( errorMeterSdps.isEmpty() || Collections.binarySearch(errorMeterSdps , sn  ) < 0 ) {
						errorMeterSdps.add(sn);
						Collections.sort(errorMeterSdps);
					}
					continue;
				}

				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				DataVEEEvent dataVEEEvent = new DataVEEEvent();
				DataVEEEventPK dataVEEEventPK = dataVEEEvent.getDataVEEEventPK();

				dataVEEEventPK.setTv(sdf.parse(values[1]));
				dataVEEEventPK.setEventId(values[2]);
				dataVEEEventPK.setSchemeType(1);
				dataVEEEventPK.setObjectId(curSdpId);

				dataVEEEvent.setUpdateTv(updateTv);
				dataVEEEvent.setDataSource(2);
				dataVEEEvent.setObjectType(7);
				dataVEEEvent.setDataType(109);
				dataVEEEvents.add(dataVEEEvent);
			}
			csvReader.close();
			if ( errorMeterSdps.size() >0 ){
				log.warn("Can not find event ServicePoint: FileName = " + filePath + " , Meter Count = " + errorMeterSdps.size() + "( ValidEventCount = " + dataVEEEvents.size() +" )  , Sn = " + errorMeterSdps.toString());
			}
			
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Set<DataVEEEvent> set = new TreeSet<DataVEEEvent>(new Comparator<DataVEEEvent>() {
				@Override
				public int compare(DataVEEEvent o1, DataVEEEvent o2) {
					DataVEEEventPK dataVEEEventPK1 = o1.getDataVEEEventPK();
					DataVEEEventPK dataVEEEventPK2 = o2.getDataVEEEventPK();

					String o1String = dataVEEEventPK1.getObjectId() + "#";
					o1String += (dataVEEEventPK1.getEventId() + "#");
					o1String += (sdf.format(dataVEEEventPK1.getTv()) + "#");
					o1String += dataVEEEventPK1.getSchemeType();
					String o2String = dataVEEEventPK2.getObjectId() + "#";
					o2String += (dataVEEEventPK2.getEventId() + "#");
					o2String += (sdf.format(dataVEEEventPK2.getTv()) + "#");
					o2String += dataVEEEventPK2.getSchemeType();
					return o1String.compareTo(o2String); 
				}
	        });
	        set.addAll(dataVEEEvents);
	        dataVEEEvents = new ArrayList<DataVEEEvent>(set);				
			
			eventQueue.addAll(dataVEEEvents);

			List<String> calEvnts = rtMdmAssetVEERuleDataSourceRepository.findCalEventIds() ;
			List<DataVEEEvent> eventList = new ArrayList<>() ;
			if ( calEvnts != null && calEvnts.size() > 0 ) {
				for (DataVEEEvent dataVEEEvent : dataVEEEvents) {
					DataVEEEventPK dataVEEEventPK = dataVEEEvent.getDataVEEEventPK();
					if ( calEvnts.indexOf(dataVEEEventPK.getEventId()) <= -1 )
						continue;

					eventList.add(dataVEEEvent) ;
					rtDataVEEEventRepository.save(dataVEEEvent) ;
				}
			}
			if (!eventList.isEmpty()){
				rtDataVEEEventRepository.saveAll(eventList) ;
			}
			log.info("Save VeeEvent to RealDb: Count = " + eventList.size() + " (EventTotal = " + dataVEEEvents.size() + " , DbCalcEventIdCount = " + calEvnts.size() +" )");
			FileUtils.getInstance().backupFile(filePath);
			log.info("End processing event files : " + filePath);
			
		}
		catch(Exception e) {
			log.error("DataImport Error : " , e);
		}
	}
	
	private class EventSaveProc implements Runnable{
		private int eventSave(int count) {
			if(eventQueue.size() == 0)
				return 0;
			if(System.currentTimeMillis() - lastSaveTime < 15000
				&& eventQueue.size() < 100)
				return 0;
			if(count > eventQueue.size()) 
				count = eventQueue.size();
			lastSaveTime = System.currentTimeMillis();
			List<DataVEEEvent> events = new ArrayList<DataVEEEvent>();
			eventQueue.drainTo(events, count);
			try {
				long startTime = System.currentTimeMillis();
				
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				Set<DataVEEEvent> set = new TreeSet<DataVEEEvent>(new Comparator<DataVEEEvent>() {
					@Override
					public int compare(DataVEEEvent o1, DataVEEEvent o2) {
						DataVEEEventPK dataVEEEventPK1 = o1.getDataVEEEventPK();
						DataVEEEventPK dataVEEEventPK2 = o2.getDataVEEEventPK();

						String o1String = dataVEEEventPK1.getObjectId() + "#";
						o1String += (dataVEEEventPK1.getEventId() + "#");
						o1String += (sdf.format(dataVEEEventPK1.getTv()) + "#");
						o1String += dataVEEEventPK1.getSchemeType();
						String o2String = dataVEEEventPK2.getObjectId() + "#";
						o2String += (dataVEEEventPK2.getEventId() + "#");
						o2String += (sdf.format(dataVEEEventPK2.getTv()) + "#");
						o2String += dataVEEEventPK2.getSchemeType();

						return o1String.compareTo(o2String);
					}
		        
		        });
		        set.addAll(events);
		        events = new ArrayList<DataVEEEvent>(set);
				dataVEEEventRepository.saveAll(events) ;
				String loggerInfo = "Into mdm_data_vee_event[count: " + count + ", time: "
						+ (System.currentTimeMillis() - startTime) + "]";
				log.info(loggerInfo) ;
				Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "Database", loggerInfo); 

			} catch (Exception e) {
				log.error("DataImport Error : " , e);
			}
			return count;
		}
		
		@Override
		public void run() {
			while(true){
				int retCount = eventSave(1000);
				if(retCount == 0){
					try {
						Thread.sleep(1000);
					} catch (InterruptedException e) {
						// TODO Auto-generated catch block
						//e.printStackTrace();
						log.error("DataImport Error : " , e);
					}
				}				
			}
		}
	
	}	
}
