package clouesp.hes.common.DataEntity.Data;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;

public class DataRegHistoryPK implements Serializable{
	private static final long serialVersionUID = -7089931109701745578L;

	@Column(name = "sdp_id", nullable = false, columnDefinition = "varchar(32)")
	private String sdpId;
	
	@Column(name = "tv")
	private Date tv;
	
	@Column(name = "scheme_type")
	private Integer schemeType;	
	
	@Column(name = "data_version")
	private Integer dataVersion;
	public String getSdpId() {
		return sdpId;
	}
	public void setSdpId(String sdpId) {
		this.sdpId = sdpId;
	}
	public Date getTv() {
		return tv;
	}
	public void setTv(Date tv) {
		this.tv = tv;
	}	
	public Integer getSchemeType() {
		return schemeType;
	}
	public void setSchemeType(Integer schemeType) {
		this.schemeType = schemeType;
	}
	public Integer getDataVersion() {
		return dataVersion;
	}
	public void setDataVersion(Integer dataVersion) {
		this.dataVersion = dataVersion;
	}
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((sdpId == null) ? 0 : sdpId.hashCode());
		result = prime * result
				+ ((tv == null) ? 0 : tv.hashCode());
		result = prime * result
				+ ((schemeType == null) ? 0 : schemeType.hashCode());		
		result = prime * result
				+ ((dataVersion == null) ? 0 : dataVersion.hashCode());
		return result;
	}
	
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		DataRegHistoryPK other = (DataRegHistoryPK) obj;
		if (sdpId == null) {
			if (other.sdpId != null)
				return false;
		} else if (!sdpId.equals(other.sdpId))
			return false;
		if (tv == null) {
			if (other.tv != null)
				return false;
		} else if (!tv.equals(other.tv))
			return false;
		if (schemeType == null) {
			if (other.schemeType != null)
				return false;
		} else if (!schemeType.equals(other.schemeType))
			return false;			
		if (dataVersion == null) {
			if (other.dataVersion != null)
				return false;
		} else if (!dataVersion.equals(other.dataVersion))
			return false;		
		return true;
	}						
}
