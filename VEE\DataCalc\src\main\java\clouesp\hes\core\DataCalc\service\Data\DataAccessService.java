package clouesp.hes.core.DataCalc.service.Data;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.List;

import javax.annotation.Resource;

import clouesp.hes.common.CommonUtils.StringUtils;
import clouesp.hes.common.DataEntity.Data.DataCalcProgress;
import clouesp.hes.common.DataEntity.Data.DataEnergyPK;

import clouesp.hes.core.DataCalc.Utils.SpringBeanUtils;
import clouesp.hes.core.DataCalc.config.ServerConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import clouesp.hes.common.DataEntity.Data.DataEnergy;
import clouesp.hes.core.DataCalc.dao.Persistence.DaoSupport;

@Slf4j
@Service("dataAccessService")
public class DataAccessService {

	@Resource(name = "persistenceJdbcTemplate")
	private JdbcTemplate jdbcTemplate;

	@Autowired
	private ServerConfig serverConfig;

	private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	private boolean putInStorage(String sql, Object[][] params) {
		Connection con = null;
		PreparedStatement pst = null;

		try {
			con = jdbcTemplate.getDataSource().getConnection();
			pst = con.prepareStatement(sql);
			int col = 0;
			for (Object[] param : params) {
				col = 0;
				for (Object obj : param) {

					pst.setObject(col + 1, obj);
					col++ ;
				}
				pst.addBatch();
			}
			con.setAutoCommit(false);
			pst.executeBatch();
			con.commit();
		}
		catch (Exception e) {
			if(e.getMessage() != null
					&& !e.getMessage().startsWith("ORA-00001")
					&& !e.getMessage().startsWith("Duplicate entry")) {
				log.info(sql);
				log.error("DataCalc Error : " , e);
				e.printStackTrace();
			}
			return false;
		} finally {
			try {
				if (pst != null) {
					pst.close();
				}
			} catch (Exception e) {

				return false;
			}
			try {
				if (con != null) {
					con.close();
				}
			} catch (Exception e) {

				return false;
			}
		}

		return true;
	}

	public boolean batchUpdateCalcProgressSaveOracle(String tableName, List<DataCalcProgress> dataCalcProgresss) throws Exception {
		int rowCount = 	dataCalcProgresss.size();
		if(rowCount == 0) {
			return true;
		}
		int columnCount = 5;

		StringBuffer sql = new StringBuffer();
		StringBuffer notSql = new StringBuffer();
		StringBuffer notVal = new StringBuffer();

		sql.append("merge into ");
		sql.append(tableName + " T1 ");
		sql.append("using(select ? as sdp_id, ? as scheme_id, ? as load_type from ");
		sql.append("dual) T2 ");
		sql.append("on (T1.sdp_id = T2.sdp_id and T1.scheme_id = T2.scheme_id and T1.load_type = T2.load_type ) ");
		sql.append("when matched then ");
		sql.append("update set tv = ?, update_tv = ? ");

		notSql.append("when not matched then ");
		notSql.append("insert (sdp_id, scheme_id, load_type, tv, update_tv) ");
		notSql.append("values (?, ? , ?, ?, ?)");

		sql.append(notSql).append(notVal);

		int row = 0;
		int col = 0;
		Object[][] params = new Object[rowCount][columnCount * 2];
		for (DataCalcProgress dataCalcProgress : dataCalcProgresss) {
			col = 0;
			for (int r = 0; r < 2; r++ ) {
				params[row][col++] = dataCalcProgress.getSdpId();
				params[row][col++] = dataCalcProgress.getSchemeId();
				params[row][col++] = dataCalcProgress.getLoadType();
				params[row][col++] = Timestamp.valueOf(sdf.format(dataCalcProgress.getTv()));
				params[row][col++] = Timestamp.valueOf(sdf.format(dataCalcProgress.getUpdateTv()));
			}
			row++;
		}

		String strSql = sql.toString();
		return putInStorage(strSql, params);
	}

	public boolean batchUpdateCalcProgressSaveMysql(String tableName, List<DataCalcProgress> dataCalcProgresss) throws Exception {
		int rowCount = 	dataCalcProgresss.size();
		if(rowCount == 0) {
			return true;
		}
		int columnCount = 5;

		StringBuffer sql = new StringBuffer();
		StringBuffer notSql = new StringBuffer();
		StringBuffer notVal = new StringBuffer();

		sql.append("replace into ");
		sql.append(tableName);
		notSql.append(" (sdp_id, scheme_id, load_type, tv, update_tv) ");
		notSql.append("values (?, ? , ?, ?, ?)");

		sql.append(notSql).append(notVal);

		int row = 0;
		int col = 0;
		Object[][] params = new Object[rowCount][columnCount];
		for (DataCalcProgress dataCalcProgress : dataCalcProgresss) {
			col = 0;
			params[row][col++] = dataCalcProgress.getSdpId();
			params[row][col++] = dataCalcProgress.getSchemeId();
			params[row][col++] = dataCalcProgress.getLoadType();
			params[row][col++] = Timestamp.valueOf(sdf.format(dataCalcProgress.getTv()));
			params[row][col++] = Timestamp.valueOf(sdf.format(dataCalcProgress.getUpdateTv()));
			row++;
		}

		String strSql = sql.toString();
		return putInStorage(strSql, params);
	}

	public boolean batchUpdateCalcProgressSave(String tableName, List<DataCalcProgress> dataCalcProgresss) throws Exception {
		if(serverConfig.isOracleDb()) {
			return batchUpdateCalcProgressSaveOracle(tableName, dataCalcProgresss);
		} else {
			return batchUpdateCalcProgressSaveMysql(tableName, dataCalcProgresss);
		}
	}

	public boolean batchSave(String tableName, List<DataEnergy> dataEnergys) throws Exception{

		int rowCount = 	dataEnergys.size();
		if(rowCount == 0) {
			return true;
		}

		Class clsPK = DataEnergyPK.class;
		Field[] fieldPKs = clsPK.getDeclaredFields();

		Class cls = DataEnergy.class;
		Field[] fields = cls.getDeclaredFields();

		int columnCount = fieldPKs.length + fields.length - 2;

		StringBuffer sql = new StringBuffer();
		StringBuffer val = new StringBuffer();

		sql.append("insert into ");
		sql.append(tableName);
		sql.append("( ");

		val.append("values (");

		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			String name = field.getName();
			name = StringUtils.humpToLine(name);
			sql.append(name);
			sql.append(", ");

			val.append("?");
			val.append(", ");
		}
		int index = 0;
		for (Field field : fields) {
			String name = field.getName();
			if("dataEnergyPK".equalsIgnoreCase(name)) {
				continue;
			}
			if(!name.startsWith("R")) {
				name = StringUtils.humpToLine(name);
			} else {
				name = name.toLowerCase();
			}

			sql.append(name);
			val.append("?");
			if(index < fields.length - 2) {
				sql.append(", ");
				val.append(", ");
			}
			index++;
		}
		sql.append(") ");
		val.append(") ");

		sql.append(val);

		int row = 0;
		int col = 0;
		Object[][] params = new Object[rowCount][columnCount];
		for (DataEnergy dataEnergy : dataEnergys) {
			col = 0;
			for (Field field : fieldPKs) {
				if(Modifier.isFinal(field.getModifiers())) {
					continue;
				}
				field.setAccessible(true);
				Object obj = field.get(dataEnergy.getDataEnergyPK());
				if(obj != null && "java.util.Date".equals(field.getType().getCanonicalName())){
					obj = Timestamp.valueOf(sdf.format(obj));
				}

				params[row][col++] = obj;
			}
			for (Field field : fields) {
				String name = field.getName();
				if("dataEnergyPK".equalsIgnoreCase(name)) {
					continue;
				}
				field.setAccessible(true);
				Object obj = field.get(dataEnergy);

				if(obj != null && "java.util.Date".equals(field.getType().getCanonicalName())){
					obj = Timestamp.valueOf(sdf.format(obj));
				}

				params[row][col++] = obj;
			}
			row++ ;
		}
		String strSql = sql.toString();
		return putInStorage(strSql, params);
	}

	public boolean batchUpdateSaveOracle(String tableName, List<DataEnergy> dataEnergys) throws Exception{
		int rowCount = 	dataEnergys.size();
		if(rowCount == 0) {
			return true;
		}
		Class clsPK = DataEnergyPK.class;
		Field[] fieldPKs = clsPK.getDeclaredFields();

		Class cls = DataEnergy.class;
		Field[] fields = cls.getDeclaredFields();

		int columnCount = fieldPKs.length + fields.length - 2;

		StringBuffer sql = new StringBuffer();
		StringBuffer notSql = new StringBuffer();
		StringBuffer notVal = new StringBuffer();

		sql.append("merge into ");
		sql.append(tableName + " T1 ");

		sql.append("using(select ");
		int index = 0;
		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			String name = field.getName();
			name = StringUtils.humpToLine(name);

			sql.append("? as ");
			sql.append(name);
			if(index < fieldPKs.length - 2) {
				sql.append(", ");
			}
			index++;
		}
		sql.append(" from dual) T2 ");

		sql.append("on (");

		index = 0;
		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			String name = field.getName();
			name = StringUtils.humpToLine(name);

			sql.append("T1.");
			sql.append(name);
			sql.append(" = ");
			sql.append("T2.");
			sql.append(name);
			if(index < fieldPKs.length - 2) {
				sql.append(" and ");
			}

			index++;
		}
		sql.append(") ");
		sql.append("when matched then update set ");


		notSql.append("when not matched then ");
		notSql.append("insert (");
		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			String name = field.getName();
			name = StringUtils.humpToLine(name);

			notSql.append(name);
			notSql.append(", ");
		}
		index = 0;
		for (Field field : fields) {
			String name = field.getName();

			if("dataEnergyPK".equalsIgnoreCase(name)) {
				continue;
			}
			if(!name.startsWith("R")) {
				name = StringUtils.humpToLine(name);
			} else {
				name = name.toLowerCase();
			}

			sql.append(name);
			sql.append(" = ?");

			notSql.append(name);

			if(index < fields.length - 2) {
				sql.append(", ");
				notSql.append(", ");
			}
			index++;
		}
		sql.append(" ");
		notSql.append(") values (");

		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			notVal.append("?");
			notVal.append(", ");
		}
		index = 0;
		for (Field field : fields) {
			String name = field.getName();
			if("dataEnergyPK".equalsIgnoreCase(name)) {
				continue;
			}
			notVal.append("?");
			if(index < fields.length - 2) {
				notVal.append(", ");
			}
			else {
				notVal.append(")");
			}

			index++;
		}
		sql.append(notSql).append(notVal);

		int row = 0;
		int col = 0;
		Object[][] params = new Object[rowCount][columnCount * 2];
		for (DataEnergy dataEnergy : dataEnergys) {
			col = 0;
			for (int r = 0; r < 2; r++) {
				for (Field field : fieldPKs) {
					if(Modifier.isFinal(field.getModifiers())) {
						continue;
					}
					field.setAccessible(true);
					Object obj = field.get(dataEnergy.getDataEnergyPK());
					if(obj != null && "java.util.Date".equals(field.getType().getCanonicalName())){
						obj = Timestamp.valueOf(sdf.format(obj));
					}
					params[row][col++] = obj;
				}

				for (Field field : fields) {
					String name = field.getName();
					if("dataEnergyPK".equalsIgnoreCase(name)) {
						continue;
					}
					field.setAccessible(true);
					Object obj = field.get(dataEnergy);
					if(obj != null && "java.util.Date".equals(field.getType().getCanonicalName())){
						obj = Timestamp.valueOf(sdf.format(obj));
					}
					params[row][col++] = obj;
				}
			}
			row++ ;
		}

		String strSql = sql.toString();
		return putInStorage(strSql, params);
	}

	public boolean batchUpdateSaveMysql(String tableName, List<DataEnergy> dataEnergys) throws Exception{
		int rowCount = 	dataEnergys.size();
		if(rowCount == 0) {
			return true;
		}
		Class clsPK = DataEnergyPK.class;
		Field[] fieldPKs = clsPK.getDeclaredFields();

		Class cls = DataEnergy.class;
		Field[] fields = cls.getDeclaredFields();

		int columnCount = fieldPKs.length + fields.length - 2;

		StringBuffer sql = new StringBuffer();

		sql.append("replace into ");
		sql.append(tableName);

		sql.append(" (");
		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			String name = field.getName();
			name = StringUtils.humpToLine(name);
			sql.append(name);
			sql.append(", ");
		}
		int index = 0;
		for (Field field : fields) {
			String name = field.getName();

			if("dataEnergyPK".equalsIgnoreCase(name)) {
				continue;
			}
			if(!name.startsWith("R")) {
				name = StringUtils.humpToLine(name);
			} else {
				name = name.toLowerCase();
			}
			sql.append(name);
			if(index < fields.length - 2) {
				sql.append(", ");

			}
			index++;
		}
		sql.append(") values (");

		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			sql.append("?");
			sql.append(", ");
		}
		index = 0;
		for (Field field : fields) {
			String name = field.getName();
			if("dataEnergyPK".equalsIgnoreCase(name)) {
				continue;
			}
			sql.append("?");
			if(index < fields.length - 2) {
				sql.append(", ");
			}
			else {
				sql.append(")");
			}

			index++;
		}

		int row = 0;
		int col = 0;
		Object[][] params = new Object[rowCount][columnCount];
		for (DataEnergy dataEnergy : dataEnergys) {
			col = 0;

			for (Field field : fieldPKs) {
				if (Modifier.isFinal(field.getModifiers())) {
					continue;
				}
				field.setAccessible(true);
				Object obj = field.get(dataEnergy.getDataEnergyPK());
				if (obj != null && "java.util.Date".equals(field.getType().getCanonicalName())) {
					obj = Timestamp.valueOf(sdf.format(obj));
				}
				params[row][col++] = obj;
			}

			for (Field field : fields) {
				String name = field.getName();
				if ("dataEnergyPK".equalsIgnoreCase(name)) {
					continue;
				}
				field.setAccessible(true);
				Object obj = field.get(dataEnergy);
				if (obj != null && "java.util.Date".equals(field.getType().getCanonicalName())) {
					obj = Timestamp.valueOf(sdf.format(obj));
				}
				params[row][col++] = obj;
			}

			row++;
		}

		String strSql = sql.toString();
		return putInStorage(strSql, params);
	}

	public boolean batchUpdateSave(String tableName, List<DataEnergy> dataEnergys) throws Exception{
		if(serverConfig.isOracleDb()) {
			return batchUpdateSaveOracle(tableName, dataEnergys);
		} else {
			return batchUpdateSaveMysql(tableName, dataEnergys);
		}
	}

}
