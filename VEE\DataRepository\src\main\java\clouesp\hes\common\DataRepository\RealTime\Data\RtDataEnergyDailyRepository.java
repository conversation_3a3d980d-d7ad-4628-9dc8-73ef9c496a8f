package clouesp.hes.common.DataRepository.RealTime.Data;

import java.util.Date;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import clouesp.hes.common.DataEntity.Data.DataEnergyDaily;
import clouesp.hes.common.DataEntity.Data.DataEnergyPK;

public interface RtDataEnergyDailyRepository extends JpaRepository<DataEnergyDaily, DataEnergyPK>{
	@Modifying
	@Transactional
	@Query(value = "delete from mdm_data_energy_dayly where tv < :tv"
			, nativeQuery = true)
	void clearExpired(
			@Param("tv")Date tv
			);		
}
