package clouesp.hes.common.DataEntity.System;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name= "sys_dataitem_export_key")
public class SysDataitemExportKey {
	@Id
	@Column(name = "column_key", nullable = false, columnDefinition = "varchar(64)")
	private String columnKey;

	@Column(name = "dst_column_key", columnDefinition = "varchar(64)")
	private String dstColumnKey;
	
	@Column(name = "sort_id")
	private Integer sortId;

	public String getColumnKey() {
		return columnKey;
	}
	public void setColumnKey(String columnKey) {
		this.columnKey = columnKey;
	}
	public String getDstColumnKey() {
		return dstColumnKey;
	}
	public void setDstColumnKey(String dstColumnKey) {
		this.dstColumnKey = dstColumnKey;
	}
	public Integer getSortId() {
		return sortId;
	}
	public void setSortId(Integer sortId) {
		this.sortId = sortId;
	}
}
