import clouesp.hes.common.CommonUtils.ObjectUtils
import clouesp.hes.common.DataEntity.Data.DataReg
import clouesp.hes.common.DataEntity.Data.DataVEEEvent

def regZeroConsumption(Map<String, Object> ruleInfo) {
    Integer result = 0;
    DataVEEEvent event = null;
    if (ruleInfo == null ||
            !ruleInfo.containsKey("datas") ||
            !ruleInfo.containsKey("params")) {
        return null;
    }

    List<Map<String, Object>> ruleDatas = ruleInfo.get("datas");
    Map<String, Object> ruleParams = ruleInfo.get("params");
    double resultValue = 0;
    if(ruleParams != null && ruleParams.containsKey("resultValue")) {
        resultValue = ruleParams.get("resultValue");
    }
    for(Map<String, Object> ruleData : ruleDatas) {
        String targetClass = ruleData.get("targetClass");
        //List<Map<String, Object>> dataList = ruleData.get("dataListDay");
        //获取数据，根据数据周期取值
        String dataKey = "dataListDay";
        Integer schemeType = new Integer(ruleInfo.get("schemeType").toString());
        if(schemeType == 1)
        {
            dataKey = "dataListMinute";
        }
        else if(schemeType == 2)
        {
            dataKey = "dataListDay";
        }
        else if(schemeType == 3)
        {
            dataKey = "dataListMonth";
        }
        List<Map<String, Object>> dataList = ruleData.get(dataKey);
        if(dataList == null) {
            return null;
        }
        if("REG" == targetClass) {
            if(dataList.size() > 0) {
                boolean isNotZeroConsumption = 0;
                Double lastR0p1 = null;
                for(Map<String, Object> data : dataList) {
                    DataReg dataReg = ObjectUtils.convertMapToObject(data, DataReg.class);
                    Double r0p1 = dataReg.getR0P1();
                    if(r0p1 != null && lastR0p1 != null) {
                       if(r0p1.doubleValue() - lastR0p1.doubleValue() > resultValue) {
                           isNotZeroConsumption = true;
                           break
                       }
                    }
                    lastR0p1 = r0p1;
                }
                if(!isNotZeroConsumption) {
                    result = 1;
                }
            }
        }
    }

    if(result.intValue() == 1) {
        event = (DataVEEEvent) ruleInfo.get("event");
    }
    return event;
}