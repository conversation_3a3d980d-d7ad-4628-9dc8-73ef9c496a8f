package clouesp.hes.common.DataMode.Statistics;

import lombok.Data;

//@Entity
//@Table(name="mdm_data_vee_event")
@Data
public class OutageEvtStatics {

   // @EmbeddedId
  //  private OutageEvtStaticsPK objectStatisticsPK = new OutageEvtStaticsPK();
    //   @Column(name = "org_id", nullable = false, columnDefinition = "varchar(32)")
    private String orgId;
    //  @Column(name = "line_id", columnDefinition = "varchar(32)")
    private String linId;

    // @Column(name = "sdp_total_num", nullable = false, columnDefinition = "INT default 0 ")
    private int sdpTotalNum;
  // @Column(name = "total_time", nullable = false, columnDefinition = "INT default 0 ")
    private int totalTime;
  //  @Column(name = "total_num", columnDefinition = "INT default 0 ")
    private int totalNum;
 //   @Column(name = "avg_time", nullable = false, columnDefinition = "INT default 0 ")
    private int avgTime;
 //   @Column(name = "avg_num", columnDefinition = "INT default 0 ")
    private int avgNum;

    public  String toString(){
        return "orgId = " + orgId + " , linId = " + linId
                + " , sdpTotalNum = " + sdpTotalNum
                + " , totalTime = " + totalTime
                + " , totalNum = " + totalNum
                + " , avgTime = " + avgTime
                + " , avgNum = " + avgNum ;
    }

}

