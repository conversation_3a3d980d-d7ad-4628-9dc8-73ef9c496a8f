/*
 * 文件名：LoggerAppenderConsole.java 版权：Copyright by Power7000 Team 描述： 修改人：jybai 修改时间：2017年10月25日
 * 跟踪单号： 修改单号： 修改内容：
 */

package clouesp.hes.common.logger.logger;


import org.apache.logging.log4j.Logger;


class LoggerConsoleAppender implements ILoggerAppender {

    private String key = "Console";

    private Logger logger = null;

    private LoggerAppenderController loggerController = new LoggerAppenderController();

    public LoggerConsoleAppender() {

    }

    @Override
    public void start() {
        logger = loggerController.getLogger(key);
        loggerController.start(key);
        loggerController.addConsoleAppender(key);
    }

    @Override
    public void stop() {
        loggerController.stop(key);
    }

    public void setLevel(LoggerLevel level) {
        loggerController.setLevel(level);
    }

    @Override
    public void writeLogInfo(LoggerLevel level, String serviceId, String deviceId,
                             String infoType, String info) {
        logger.log(LoggerUtils.getInstance().LoggerLevel2Level(level), info);
    }
    
    @Override
    public void appendLogger(){
        
    }

    @Override
    public void directAppendLogger() {
         
    }    
}
