package clouesp.hes.common.DataEntity.Asset;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;

@ApiModel(value="assetMeter",description="电表信息")
@Entity
@Table(name= "asset_meter", 
indexes = {@Index(name = "IDX_ASSET_METER_SN",  columnList="sn", unique = true)})
public class AssetMeter {
	@ApiModelProperty(value="表计ID", position=1)
	@Id
	@Column(name = "id", nullable = false, columnDefinition = "varchar(32)")
	private String id;
	@ApiModelProperty(value="表计编码", position=2)
	@Column(name = "sn", nullable = false, columnDefinition = "varchar(32)")
	private String sn;
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getSn() {
		return sn;
	}
	public void setSn(String sn) {
		this.sn = sn;
	}
	
}
