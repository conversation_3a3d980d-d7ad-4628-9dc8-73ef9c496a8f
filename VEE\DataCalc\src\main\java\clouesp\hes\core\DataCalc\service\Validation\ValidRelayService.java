package clouesp.hes.core.DataCalc.service.Validation;


import clouesp.hes.common.DataEntity.Asset.MdmAssetServicePoint;
import clouesp.hes.common.DataEntity.Asset.MdmAssetVEERule;
import clouesp.hes.common.DataEntity.Data.DataVEEEvent;
import clouesp.hes.common.DataEntity.Data.DataVEEEventPK;
import clouesp.hes.common.DataEntity.Data.MdmDataUpdateLog;
import clouesp.hes.common.DataEntity.Data.MdmDataUpdateLogPK;
import clouesp.hes.common.DataRepository.Persistence.Asset.MdmAssetServicePointRepository;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetServicePointRepository;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetVEERuleRepository;
import clouesp.hes.common.DataRepository.RealTime.Data.RtMdmDataUpdateLogRepository;
import clouesp.hes.core.DataCalc.dao.Persistence.DaoSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

@Service("validRelayService")
public class ValidRelayService {

    @Resource(name = "daoSupport")
    private DaoSupport dao;
    @Autowired
    private RtMdmDataUpdateLogRepository rtMdmDataUpdateLogRepository;

    @Autowired
    private RtMdmAssetServicePointRepository rtMdmAssetServicePointRepository;

    @Autowired
    private RtMdmAssetVEERuleRepository rtMdmAssetVEERuleRepository;

    @Autowired
    private MdmAssetServicePointRepository mdmAssetServicePointRepository;
    public void execute() throws Exception {

        Date now =new Date();
        Calendar nowCal = Calendar.getInstance();
        nowCal.setTime(now);
        nowCal.set(Calendar.HOUR_OF_DAY,0);
        nowCal.set(Calendar.MINUTE,0);
        nowCal.set(Calendar.SECOND,0);

        Date validDate = nowCal.getTime();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        String yyyyMMdd = sdf.format(validDate);

        //查询所有sdp信息，并且引用了延迟3天，延迟7天规则的组

        //延迟三天
        List<MdmAssetVEERule> relayRules3 =rtMdmAssetVEERuleRepository.findAllByEventId("1001002");

        //获取所有引用了这些校验组id
        List<String> groupIds3 = getDistinctValidGroupIds(relayRules3);

        //获取所有需要检查延迟3天的sdp,并且没有生成event事件的
        List<MdmAssetServicePoint> sdpRelay3 = mdmAssetServicePointRepository.findSdpListByValidGroupIds(groupIds3,yyyyMMdd,"1001002");

        //比对这些sdp
        List<DataVEEEvent> dataVEEEvents3 =validRelay(sdpRelay3,3,now);

        //处理延迟超过7天的
        List<MdmAssetVEERule> relayRules7 =rtMdmAssetVEERuleRepository.findAllByEventId("1001003");

        List<String> groupIds7 = getDistinctValidGroupIds(relayRules7);

        List<MdmAssetServicePoint> sdpRelay7 = mdmAssetServicePointRepository.findSdpListByValidGroupIds(groupIds7,yyyyMMdd,"1001003");

        List<DataVEEEvent> dataVEEEvents7 =validRelay(sdpRelay7,7,now);

        //保存这些事件，如果事件不存在，新增，存在，更新更新时间
        this.saveEvents(dataVEEEvents3);

        this.saveEvents(dataVEEEvents7);
    }

    private List<String> getDistinctValidGroupIds(List<MdmAssetVEERule> sourceList)
    {
        List<String> rntIds = new ArrayList<>();

        for (MdmAssetVEERule rule:sourceList
        ) {
            if(!rntIds.contains(rule.getMgId()))
            {
                rntIds.add(rule.getMgId());
            }
        }
        return rntIds;
    }

    private List<DataVEEEvent> validRelay(List<MdmAssetServicePoint> sdpList,Integer validType,Date referenceTime)
    {
        Calendar nowCal = Calendar.getInstance();
        nowCal.setTime(referenceTime);
        nowCal.set(Calendar.HOUR_OF_DAY,0);
        nowCal.set(Calendar.MINUTE,0);
        nowCal.set(Calendar.SECOND,0);

        Date validDate = nowCal.getTime();

        List<DataVEEEvent> events = new ArrayList<>();

        //根据当前sdp查询数据的上传进度
        for (MdmAssetServicePoint sdpPiont:sdpList
             ) {


            MdmDataUpdateLogPK pk = new MdmDataUpdateLogPK();
            //固定检测日的
            pk.setSchemeType(2);
            pk.setSdpId(sdpPiont.getId());
            //表码
            pk.setDataType(101);

            //异常等级
            Integer veeEventClass = 1;
            String eventId = "";
            if(validType == 3)
            {

                eventId = "1001002";

            }
            else if(validType == 7)
            {
                eventId = "1001003";

            }
            //查找当前sdp对应的vee规则信息
            Optional<MdmAssetServicePoint>  optionalMdmAssetServicePoint = rtMdmAssetServicePointRepository.findById(sdpPiont.getId());
            if(optionalMdmAssetServicePoint.isPresent())
            {

                String validGrpId = optionalMdmAssetServicePoint.get().getVeeValidationGroupId();


                List<MdmAssetVEERule> mdmAssetVEERuleList = rtMdmAssetVEERuleRepository.findAllByMgIdAndEventId(validGrpId,eventId);

                if(mdmAssetVEERuleList != null && mdmAssetVEERuleList.size() > 0)
                {
                    veeEventClass = mdmAssetVEERuleList.get(0).getClassId();
                }
            }


            Optional<MdmDataUpdateLog> optional = rtMdmDataUpdateLogRepository.findById(pk);
            Boolean hasErr = false;
            if(optional.isPresent())
            {
                //获取进度值
                Date lastDateTime = optional.get().getLastDataTime();
                Calendar calendar1 = Calendar.getInstance();
                calendar1.setTime(lastDateTime);






                //参照时间比当前时间大与3，小于7，认为大于3天
                if(validType == 3)
                {

                    calendar1.add(Calendar.DAY_OF_MONTH,3);
                    Date tmpDateAfter3 = calendar1.getTime();


                    if(tmpDateAfter3.before(referenceTime))
                    {
                        hasErr = true;
                    }
                }
                else if(validType == 7)
                {
                    calendar1.add(Calendar.DAY_OF_MONTH,7);
                    Date tmpDateAfter7 = calendar1.getTime();

                    if(tmpDateAfter7.before(referenceTime))
                    {
                        hasErr = true;
                    }
                }





            }
            else
            {
                hasErr = true;
            }

            if(hasErr)
            {
                //看当前天数与当前时间相比，是否超过指定天数
                DataVEEEvent event = new DataVEEEvent();
                DataVEEEventPK p = new DataVEEEventPK();
                if(validType == 3)
                {

                    p.setEventId("1001002");

                }
                else if(validType == 7)
                {
                    p.setEventId("1001003");
                }
                p.setObjectId(sdpPiont.getId());
                p.setSchemeType(2);
                //获取当前时间

                p.setTv(validDate);
                event.setDataVEEEventPK(p);

                event.setUpdateTv(new Date());
                //mdm
                event.setDataSource(3);
                //sdp
                event.setObjectType(7);
                //等级

                event.setVeeEventClass(veeEventClass);
                //寄存器
                event.setDataType(101);
                event.setEstimationStatus(0);

                events.add(event);
            }

        }

        return events;
    }

    private void saveEvents(List<DataVEEEvent> events) throws Exception {

        //对比更新
        if(events.size() > 0)
        {
            dao.save("ValidRelayAccessMapper.batchSaveMdmDataVeeEvent",events);

        }


    }
}
