package clouesp.hes.common.DataRepository.RealTime.Data;

import java.util.Date;
import java.util.List;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import clouesp.hes.common.DataEntity.Data.DataRegMinutely;
import clouesp.hes.common.DataEntity.Data.DataRegPK;

public interface RtDataRegMinutelyRepository extends JpaRepository<DataRegMinutely, DataRegPK>{
	List<DataRegMinutely> findByDataRegPKSdpIdAndDataRegPKTvIn(
			String sdpId, 
			List<Date> tvs
			);	
	@Modifying
	@Transactional
	@Query(value = "delete from mdm_data_reg_minutely where tv < :tv"
			, nativeQuery = true)
	void clearExpired(
			@Param("tv")Date tv
			);
}
