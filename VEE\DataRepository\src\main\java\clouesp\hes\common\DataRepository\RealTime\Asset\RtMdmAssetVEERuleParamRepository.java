package clouesp.hes.common.DataRepository.RealTime.Asset;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;

import clouesp.hes.common.DataEntity.Asset.MdmAssetVEERuleParam;
import clouesp.hes.common.DataEntity.Asset.MdmAssetVEERuleParamPK;

public interface RtMdmAssetVEERuleParamRepository extends JpaRepository<MdmAssetVEERuleParam, MdmAssetVEERuleParamPK>{
	List<MdmAssetVEERuleParam> findByRuleId(String ruleId);
}
