<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="IntegrityRateMapper">
    <!-- Miss Data Tracing ： Progress Delay Report   -->
    <select id="getIntegrityRateReportList" resultType="com.clou.common.DataEntity.Model.Data.IntegrityRateReportResult"  parameterType="PagingRequest">
        select am.SN as meterSn,profile.id as profileId , profile.name as profileName , di.tv as tv,  factory.name as factoryName ,ddm.NAME as modelName, acun.SN AS commuSn, commutype.name as commuType ,
        dsp.TV as progressTv , dsp.LAST_TASK_TV
        as lastTaskTv, dsp.TASK_STATE as taskState, dsp.FAILED_INFO as faileuerCause ,T5.update_tv as statuUpdateTv
        ,T5.com_status as comStatus,di.integrity as rate

        from DATA_INTEGRITY_METER di,
        ASSET_METER am,
        DATA_SCHEDULE_PROGRESS dsp,
        DICT_DEVICE_MODEL ddm,
        dict_communication_type commutype ,
        dict_manufacturer factory  ,
        dict_profile profile ,
        ASSET_COMMUNICATOR acun
        left join DATA_COMMINICATION_STATUS T5 on T5.COMMUNICATOR_ID = acun.ID
        where
         1 = 1
        <if test="params.orgIdList != null and params.orgIdList.size > 0">
            and am.org_id in
            <foreach collection="params.orgIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        and di.ID=am.ID
        AND di.ID=dsp.DEVICE_ID
        AND am.MODEL=ddm.ID
        AND AM.COMMUNICATOR_ID=acun.ID
        and  profile.id = dsp.PROFILE_ID
        AND di.PROFILE_ID=dsp.PROFILE_ID
        and   factory.id = am.manufacturer
        and  am.com_type = commutype.id
        <if test="entity">
            <if test="entity.meterSn != null and entity.meterSn != ''" >
                and am.sn  = #{entity.meterSn}
            </if>
            <if test="entity.communicatorSn != null and entity.communicatorSn != ''" >
                and acun.sn = #{entity.communicatorSn}
            </if>
            <if test="entity.profileIdList != null and entity.profileIdList.size > 0">
                and di.profile_id in
                <foreach collection="entity.profileIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>

            </if>


            <if test="entity.manufacturerID != null and entity.manufacturerID != ''">
                and factory.id = #{entity.manufacturerID}
            </if>
            <if test="entity.modelId != null and entity.modelId != ''">
                and ddm.id = #{entity.modelId}
            </if>
            <if test="entity.commuTypeId != null and entity.commuTypeId != ''">
                and commutype.id = #{entity.commuTypeId}
            </if>
            <if test="entity.integrityRateStart != null and entity.integrityRateStart != ''">
                <![CDATA[ and di.integrity >= #{entity.integrityRateStart} ]]>
            </if>
            <if test="entity.integrityRateEnd != null and entity.integrityRateEnd != '' ">
                <![CDATA[ and di.integrity <= #{entity.integrityRateEnd} ]]>
            </if>
            <if test="entity.tv != null   " >
                and  <![CDATA[ di.tv = #{entity.tv}  ]]>
            </if>

            <if test="entity.delayPrgStartTime != null   " >
                and  <![CDATA[ dsp.TV >= #{entity.delayPrgStartTime}  ]]>
            </if>
            <if test="entity.delayPrgEndTime != null   " >
                and  <![CDATA[ dsp.TV <= #{entity.delayPrgEndTime}  ]]>
            </if>

        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>
    </select>

    <select id="getUserMeterProfileIds" resultType="java.lang.String" parameterType="PagingRequest">
        SELECT PROFILE_ID FROM asset_measurement_profile
                          WHERE profile_type = 1 AND MG_ID IN (
                          SELECT group_id FROM asset_meter_group_map WHERE id in (
                          select id from ASSET_METER am
                          where 1=1
                                <if test="params.orgIdList != null and params.orgIdList.size > 0">
                                    and am.org_id in
                                    <foreach collection="params.orgIdList" item="item" separator="," open="(" close=")">
                                        #{item}
                                    </foreach>
                                </if>

                          ) AND type = 1)

                        <choose>
                            <when test="entity.profileId=='Daily' or entity.profileId=='Minutely' or entity.profileId=='Monthly'">
                            and PROFILE_CYCLE_TYPE = #{entity.profileId}
                            </when>
                            <when test="entity.profileId=='Total'"></when>
                            <otherwise>AND PROFILE_ID = #{entity.profileId}</otherwise>
                        </choose>

    </select>

</mapper>