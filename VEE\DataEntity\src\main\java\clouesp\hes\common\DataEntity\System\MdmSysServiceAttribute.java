package clouesp.hes.common.DataEntity.System;

import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name= "mdm_sys_service_attribute")
public class MdmSysServiceAttribute {
	
	@EmbeddedId
	private MdmSysServiceAttributePK mdmSysServiceAttributePK = new MdmSysServiceAttributePK();

	@Column(name = "attribute_value", columnDefinition = "varchar(64)")
	private String attributeValue;

	public MdmSysServiceAttributePK getMdmSysServiceAttributePK() {
		return mdmSysServiceAttributePK;
	}

	public void setMdmSysServiceAttributePK(MdmSysServiceAttributePK mdmSysServiceAttributePK) {
		this.mdmSysServiceAttributePK = mdmSysServiceAttributePK;
	}

	public String getAttributeValue() {
		return attributeValue;
	}

	public void setAttributeValue(String attributeValue) {
		this.attributeValue = attributeValue;
	}
	
}
