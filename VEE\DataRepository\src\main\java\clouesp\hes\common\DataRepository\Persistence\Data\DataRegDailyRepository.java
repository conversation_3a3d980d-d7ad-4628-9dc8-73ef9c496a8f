package clouesp.hes.common.DataRepository.Persistence.Data;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;

import clouesp.hes.common.DataEntity.Data.DataRegDaily;
import clouesp.hes.common.DataEntity.Data.DataRegPK;

public interface DataRegDailyRepository extends JpaRepository<DataRegDaily, DataRegPK>{	
	List<DataRegDaily> findByDataRegPKSdpIdAndDataRegPKTvIn(
			String sdpId, 
			List<Date> tvs
			);			
}
