@echo off
echo 正在安装RealTimeDB及其依赖项...
cd /D "%~dp0"

echo.
echo 正在安装依赖项...
echo.

echo 正在安装logger...
cd ..\logger
call mvn clean install -DskipTests

echo.
echo 正在安装CommonUtils...
cd ..\CommonUtils
call mvn clean install -DskipTests

echo.
echo 正在安装DataEntity...
cd ..\DataEntity
call mvn clean install -DskipTests

echo.
echo 正在安装DataModel...
cd ..\DataModel
call mvn clean install -DskipTests

echo.
echo 正在安装DataRepository...
cd ..\DataRepository
call mvn clean install -DskipTests

echo.
echo 正在安装RealTimeDB...
cd ..\RealTimeDB
call mvn clean install -DskipTests

echo.
echo RealTimeDB安装完成！