package clouesp.hes.common.DataRepository.RealTime.System;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;

import clouesp.hes.common.DataEntity.System.SysDataitemExport;

public interface RtSysDataitemExportRepository extends JpaRepository<SysDataitemExport, String>{
	List<SysDataitemExport> findByDataitemTypeAndExportFlag(Integer dataitemType, Integer exportFlag);
}
