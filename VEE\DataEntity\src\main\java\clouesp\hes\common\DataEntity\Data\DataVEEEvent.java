package clouesp.hes.common.DataEntity.Data;

import lombok.Data;

import javax.persistence.*;
import java.text.SimpleDateFormat;
import java.util.Date;
@Data
@Entity
@Table(name= "mdm_data_vee_event")
public class DataVEEEvent {

	@EmbeddedId
	private DataVEEEventPK dataVEEEventPK = new DataVEEEventPK() ;

	@Column(name = "update_tv")
	private Date updateTv;
	
	@Column(name = "data_source")
	private Integer dataSource;

	@Column(name = "object_type")
	private Integer objectType;
	
	@Column(name = "vee_event_class")
	private Integer veeEventClass;
	
	@Column(name = "data_type")
	private Integer dataType;
	
	@Column(name = "end_tv")
	private Date endTv;
	
	@Column(name = "estimation_status")
	private Integer estimationStatus;
	
	@Column(name = "duration_time")
	private Long durationTime;
}
