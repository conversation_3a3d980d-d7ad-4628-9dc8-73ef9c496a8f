/*
 * 文件名：FtpUploadController.java
 * 版权：Copyright by Power7000 Team
 * 描述：
 * 修改人：jybai
 * 修改时间：2018年4月13日
 * 跟踪单号：
 * 修改单号：
 * 修改内容：
 */

package clouesp.hes.core.DataImport.service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import clouesp.hes.common.CommonUtils.FtpUtils;
import clouesp.hes.common.CommonUtils.SftpUtils;
import clouesp.hes.common.DataEntity.Asset.MdmAssetHes;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetHesRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * FTP服务
 */
@Slf4j
@Service("ftpService")
public class FtpService {

    private FtpDownloadProc ftpDownloadProc = null;
    private Thread ftpDownloadProcThread = null;

	@Value("${ftp.type}")
	private String type;

	@Value("${ftp.localDir}")
	private String localDir;

	@Value("${ftp.manualDir}")
	private String manualDir;	

	@Value("${ftp.ftpDir}")
	private String ftpDir;

	@Value("${ftp.ftpManualDir}")
	private String ftpManualDir;	

	@Value("${ftp.ftpIp}")
	private String ftpIp;

	@Value("${ftp.ftpPort}")
	private int ftpPort;

	@Value("${ftp.ftpAccount}")
	private String ftpAccount;

	@Value("${ftp.ftpPassword}")
	private String ftpPassword;

	@Autowired
	private RtMdmAssetHesRepository rtMdmAssetHesRepository;

	public void startService(String serviceId) {
		if (ftpDownloadProc != null && ftpDownloadProcThread != null && ftpDownloadProcThread.isAlive()) {
			return;
		}
		ftpDownloadProc = new FtpDownloadProc();
		ftpDownloadProcThread = new Thread(ftpDownloadProc);
		ftpDownloadProcThread.start();
	}

    private class FtpDownloadProc implements Runnable {
        private DateFormat sdflog = new SimpleDateFormat("MM-dd HH:mm:ss");    
		public void run() {
			String loggerInfo = "Data Import Service Ftp Task run";			
			System.out.println("[" + sdflog.format(new Date()) + "] " + loggerInfo);
			
			boolean login = false;
			while (true) {
				try {
					// download file ftp
					if("sftp".equals(type)) {
						login = SftpUtils.getInstance().login(ftpIp, ftpPort, ftpAccount, ftpPassword);
					} else {
						login = FtpUtils.getInstance().login(ftpIp, ftpPort, ftpAccount, ftpPassword);
					}
					
					if (login) {
						loggerInfo = "Login FTP successful ";
                        if(ftpManualDir != null && !ftpManualDir.trim().equals("")){
						    if("sftp".equals(type)) {
						    	SftpUtils.getInstance().downLoadFtps(ftpManualDir, ".csv", manualDir, true, 0);							
						    } else {
						    	FtpUtils.getInstance().downLoadFtps(ftpManualDir, ".csv", manualDir, true, 0);
						    }
							SftpUtils.getInstance().close();
                        }
						

						Thread.sleep(5000);
					    login = false;
						if("sftp".equals(type)) {
							login = SftpUtils.getInstance().login(ftpIp, ftpPort, ftpAccount, ftpPassword);
						} else {
							login = FtpUtils.getInstance().login(ftpIp, ftpPort, ftpAccount, ftpPassword);
						}

						if (login) {
							if("sftp".equals(type)) {
								SftpUtils.getInstance().downLoadFtps(ftpDir, ".csv", localDir, true, 0);
								SftpUtils.getInstance().close();
							}
							else {
								FtpUtils.getInstance().downLoadFtps(ftpDir, ".csv", localDir, true, 0);
								FtpUtils.getInstance().close();
							}	
						}else {
							loggerInfo = "Failed ftp login ";
							System.out.println("[" + sdflog.format(new Date()) + "] " + loggerInfo);
							log.warn(loggerInfo);
							try {
								Thread.sleep(10 * 60 * 1000);
							} catch (InterruptedException e) {
								// TODO Auto-generated catch block
								//e.printStackTrace();
								log.error("DataImport Error : " , e);
							}
							continue;
						}												
						
					}else {
						loggerInfo = "Failed ftp login ";
						System.out.println("[" + sdflog.format(new Date()) + "] " + loggerInfo);
						log.warn(loggerInfo);
						try {
							Thread.sleep(10 * 60 * 1000);
						} catch (InterruptedException e) {
							// TODO Auto-generated catch block
							//e.printStackTrace();
							log.error("DataImport Error : " , e);
						}
						continue;
					}		
					
				 
					//check all assetHes
					List<String> localDirList = new ArrayList<>();		
					List<MdmAssetHes> mdmAssetHess = rtMdmAssetHesRepository.findAll();
					for (MdmAssetHes mdmAssetHes : mdmAssetHess) {
						Thread.sleep(5000);
						login = false;
						if("sftp".equals(type)) {
							login = SftpUtils.getInstance().login(ftpIp, ftpPort, ftpAccount, ftpPassword);
						} else {
							login = FtpUtils.getInstance().login(ftpIp, ftpPort, ftpAccount, ftpPassword);
						}
						
						if (login) {
							if("sftp".equals(type)) {
								SftpUtils.getInstance().downLoadFtps(mdmAssetHes.getFtpFileDir(), ".csv", mdmAssetHes.getLocalFileDir(), true, 0);
								SftpUtils.getInstance().close();
							}
							else {
								FtpUtils.getInstance().downLoadFtps(mdmAssetHes.getFtpFileDir(), ".csv", mdmAssetHes.getLocalFileDir(), true, 0);
								FtpUtils.getInstance().close();
							}
						}
						else {
							loggerInfo = "Failed ftp login (ftpDir:  " + mdmAssetHes.getFtpFileDir()  + " , localDir: "+ mdmAssetHes.getLocalFileDir() +  ")" ;
							System.out.println("[" + sdflog.format(new Date()) + "] " + loggerInfo);
							log.warn(loggerInfo);
							try {
								Thread.sleep(10 * 60 * 1000);
							} catch (InterruptedException e) {
								// TODO Auto-generated catch block
								//e.printStackTrace();
								log.error("DataImport Error : " , e);
							}
						}				 
					}
					 
					
					Thread.sleep(10 * 60 * 1000);
				} catch (InterruptedException e) {
					// TODO Auto-generated catch block
					//e.printStackTrace();
					log.error("DataImport Error : " , e);
				}catch (Exception e){
					log.error("DataImport Ftp Error : " , e);
				}
			}
		}
    }
	
}
