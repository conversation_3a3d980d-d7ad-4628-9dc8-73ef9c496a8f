package clouesp.hes.core.DataScan.schedule.Task;

import clouesp.hes.common.CommonUtils.ObjectUtils;
import clouesp.hes.common.CommonUtils.StringUtils;
import clouesp.hes.common.DataEntity.Data.DataCalcProgress;
import clouesp.hes.common.DataEntity.Data.DataReg;
import clouesp.hes.common.DataEntity.Data.DataVEEEvent;
import clouesp.hes.common.DataRepository.Persistence.Data.DataCalcProgressRepository;
import clouesp.hes.common.MqBus.MQMsg;
import clouesp.hes.common.MqBus.ServiceProducer;
import clouesp.hes.common.logger.logger.Logger;
import clouesp.hes.common.logger.logger.LoggerLevel;
import clouesp.hes.core.DataScan.Utils.DatabaseUtils;
import clouesp.hes.core.DataScan.config.ServerConfig;
import jline.internal.Log;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;
import java.util.concurrent.ConcurrentHashMap;

@Component("scheduleScanTask")
public class ScheduleScanTask {

    @Resource(name = "persistenceJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ServerConfig serverConfig;

    @Autowired
    private DataCalcProgressRepository DataCalcProgressRepository;

    @Autowired
    private ServiceProducer serviceProducer;
    private String producerGroup;
    private String producerTopic;
    private String producerTags;
    private DefaultMQProducer producer;

    private boolean executing = false;

    private SimpleDateFormat sdflog = new SimpleDateFormat("MM-dd HH:mm:ss");

    private Map<String, Date> mapDataScanProgress = new ConcurrentHashMap<>();

    @Value("${schedule.scanMinute}")
    private int scanMinute;

    @Value("${schedule.scanDay}")
    private int scanDay;

    @Value("${schedule.scanMonth}")
    private int scanMonth;

    @Value("${schedule.scanEvent}")
    private int scanEvent;

    public void init(String serviceId) {

        String prefix = "VEE";
        String producerInfo = "IMPORT.DATAIMPORT.DATA";
        String[] infos = producerInfo.split("\\.");
        producerGroup = prefix + "_" + infos[0];
        producerTopic = prefix + "_" + infos[1];
        producerTags = prefix + "_" + infos[2];
        String namesrvAddr = serverConfig.getNamesrvAddr();
        producer = serviceProducer.start(namesrvAddr, producerGroup);

    }

    public void dispatch() {
        String logInfo = null;
        if (executing) {
            return;
        }
        executing = true;

        logInfo = "Start Scan Data and Event";
        Log.info(logInfo);
        Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "Calculation", logInfo);

        try {
            List<DataCalcProgress> calcProgressList = DataCalcProgressRepository.findAll();
            mapDataScanProgress.clear();
            for(DataCalcProgress calcProgress : calcProgressList) {
                String key = calcProgress.getSdpId();
                key += "#";
                key += calcProgress.getSchemeId();
                key += "#";
                key += calcProgress.getLoadType();
                mapDataScanProgress.put(key, calcProgress.getTv());
            }
            logInfo = "Start Scan Monthly Data";
            Log.info(logInfo);
            Date curTime = new Date();
            Calendar cal = Calendar.getInstance();
            cal.set(Calendar.DAY_OF_MONTH, 1);
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            cal.add(Calendar.MONTH, -scanMonth);

            if ( serverConfig.isOracleDb() )
                scanDataOracle("mdm_data_reg_monthly", cal.getTime(), "8", Calendar.MONTH, "MONTH");
            else
                scanDataMysql("mdm_data_reg_monthly", cal.getTime(), "8", Calendar.MONTH, "MONTH");
            logInfo = "End Scan Monthly Data";

            Log.info(logInfo);

            logInfo = "Start Scan Daily Data";
            Log.info(logInfo);

            cal.setTime(curTime);
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            cal.add(Calendar.DAY_OF_MONTH, -scanDay);

            if ( serverConfig.isOracleDb() )
                scanDataOracle("mdm_data_reg_dayly", cal.getTime(), "7", Calendar.DAY_OF_MONTH, "DAY");
            else
                scanDataMysql("mdm_data_reg_dayly", cal.getTime(), "7", Calendar.DAY_OF_MONTH, "DAY");
            logInfo = "End Scan Daily Data";

            Log.info(logInfo);

            logInfo = "Start Scan Minutely Data";
            Log.info(logInfo);

            cal.setTime(curTime);
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            cal.add(Calendar.DAY_OF_MONTH, -scanMinute);

            if ( serverConfig.isOracleDb() )
                scanDataOracle("mdm_data_reg_minutely", cal.getTime(), "6", Calendar.DAY_OF_MONTH, "MINUTE");
            else
                scanDataMysql("mdm_data_reg_minutely", cal.getTime(), "6", Calendar.DAY_OF_MONTH, "MINUTE");
            logInfo = "End Scan Minutely Data";

            Log.info(logInfo);

            logInfo = "Start Scan Event";
            Log.info(logInfo);
            cal.setTime(curTime);
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            cal.add(Calendar.MONTH, -scanEvent);

            if (serverConfig.isOracleDb() )
                scanEventOracle(cal.getTime());
            else
                scanEventMysql(cal.getTime());
            logInfo = "End Scan Event";
            Log.info(logInfo);
            
        } catch (Exception e) {

        }

        executing = false;

        logInfo = "End Scan Data and Event";
        Log.info(logInfo);
        Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "Calculation", logInfo);

    }

    private long handleDataReg(ResultSet rs, String schemeId, int field, String timeType) {
        String loadType = "Register";
        ResultSetMetaData rsmd;
        long count = 0;
        try {
            rsmd = rs.getMetaData();
            List<DataReg> dataRegs = new ArrayList<DataReg>();
            while (rs.next()) {
                count++;
                DataReg dataReg = new DataReg();
                for (int i = 1; i <= rsmd.getColumnCount(); i++ ) {
                    String value = rs.getString(i);
                    String colName = rsmd.getColumnName(i);

                    if("rn".equalsIgnoreCase(colName)) {
                        continue;
                    }
                    if(colName.startsWith("R")) {
                        colName = colName.toUpperCase();
                    } else {
                        colName = colName.toLowerCase();
                    }

                    String fieldName = StringUtils.lineToHump(colName);

                    if("sdpId".equals(fieldName) || "tv".equals(fieldName)) {
                        fieldName = ObjectUtils.getFieldName(dataReg.getDataRegPK(), fieldName);
                        ObjectUtils.invokeSet(dataReg.getDataRegPK(), fieldName, value);
                    }
                    else {
                        fieldName = ObjectUtils.getFieldName(dataReg, fieldName);
                        ObjectUtils.invokeSet(dataReg, fieldName, value);
                    }
                }
                String key = dataReg.getDataRegPK().getSdpId();
                key += "#";
                key += schemeId;
                key += "#";
                key += loadType;
                if(mapDataScanProgress.containsKey(key)) {
                    Date calcProgressTv = mapDataScanProgress.get(key);
                    Calendar cal = Calendar.getInstance();
                    cal.setTime(calcProgressTv);
                    cal.add(field, 1);
                    if(dataReg.getDataRegPK().getTv().getTime() <= calcProgressTv.getTime()) {
                        continue;
                    }
                }
                dataReg.setTimeType(timeType);
                dataRegs.add(dataReg);
            }
            for(DataReg dataReg : dataRegs) {
                sendMsg(dataReg.getDataRegPK().getSdpId(), loadType, dataReg);
            }

        } catch (Exception e) {

        }
        return count;
    }

    private void scanDataOracle(String tableName, Date startTv, String schemeId, int field, String timeType) throws Exception {
        Connection conn = null;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        conn = jdbcTemplate.getDataSource().getConnection();
        Statement st = null;
        st = conn.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
        ResultSet rs = null;

        long startRn = 1;
        while (true) {
            StringBuffer sqlStrBuff = new StringBuffer();
            sqlStrBuff.append("select t.* from (select rownum rn, dt.* from ");
            sqlStrBuff.append(tableName + " dt ");
            sqlStrBuff.append("where dt.tv >= to_date('");

            sqlStrBuff.append(sdf.format(startTv));
            sqlStrBuff.append("','yyyy-mm-dd hh24:mi:ss') order by dt.tv, dt.sdp_id) t where rn between ");
            sqlStrBuff.append(startRn);
            sqlStrBuff.append(" and ");
            sqlStrBuff.append(startRn + 100000);
            String sql = sqlStrBuff.toString();
            rs = DatabaseUtils.getInstance().queryForFetch(conn, st, sql, 100000);
        if (rs == null) {
                break;
            }
            long count = handleDataReg(rs, schemeId, field, timeType);
            if(count < 100000) {
                break;
            }
            startRn += 100000;
        }

        try {
            if (st != null)
                st.close();
            if (rs != null)
                rs.close();
            if (conn != null)
                conn.close();
        } catch (SQLException e) {

        }
    }

    private void scanDataMysql(String tableName, Date startTv, String schemeId, int field, String timeType) throws Exception {
        Connection conn = null;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        conn = jdbcTemplate.getDataSource().getConnection();
        Statement st = null;
        st = conn.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
        ResultSet rs = null;

        long startRn = 1;
        while (true) {
            StringBuffer sqlStrBuff = new StringBuffer();
            sqlStrBuff.append("select t.* from (select dt.* from ");
            sqlStrBuff.append(tableName + " dt");
            sqlStrBuff.append(" where dt.tv >=  '");
            sqlStrBuff.append(sdf.format(startTv));
            sqlStrBuff.append("'  order by dt.tv, dt.sdp_id) t limit  ");
            sqlStrBuff.append(startRn -1 );
            sqlStrBuff.append(" , 100000 ");

            String sql = sqlStrBuff.toString();
            rs = DatabaseUtils.getInstance().queryForFetch(conn, st, sql, 100000);
            if (rs == null) {
                break;
            }
            long count = handleDataReg(rs, schemeId, field, timeType);
            if(count < 100000) {
                break;
            }
            startRn += 100000;
        }

        try {
            if (st != null)
                st.close();
            if (rs != null)
                rs.close();
            if (conn != null)
                conn.close();
        } catch (SQLException e) {

        }
    }

    private long handleEvent(ResultSet rs) {
        String loadType = "VeeEvent";
        ResultSetMetaData rsmd;
        long count = 0;
        try {
            rsmd = rs.getMetaData();
            List<DataVEEEvent> events = new ArrayList<DataVEEEvent>();
            while (rs.next()) {
                count++;
                DataVEEEvent event = new DataVEEEvent();
                for (int i = 1; i <= rsmd.getColumnCount(); i++ ) {
                    String value = rs.getString(i);
                    String colName = rsmd.getColumnName(i);

                    if("rn".equalsIgnoreCase(colName)) {
                        continue;
                    }
                    colName = colName.toLowerCase();
                    String fieldName = StringUtils.lineToHump(colName);

                    if("objectId".equals(fieldName) ||
                            "tv".equals(fieldName) ||
                            "eventId".equals(fieldName) ||
                            "schemeType".equals(fieldName)) {
                        fieldName = ObjectUtils.getFieldName(event.getDataVEEEventPK(), fieldName);
                        ObjectUtils.invokeSet(event.getDataVEEEventPK(), fieldName, value);
                    }
                    else {
                        fieldName = ObjectUtils.getFieldName(event, fieldName);
                        if(fieldName == null) {
                            continue;
                        }
                        ObjectUtils.invokeSet(event, fieldName, value);
                    }
                }
                events.add(event);
            }
            for(DataVEEEvent event : events) {
                sendMsg(event.getDataVEEEventPK().getObjectId(), loadType, event);
            }

        } catch (Exception e) {

        }
        return count;
    }

    private void scanEventOracle(Date startTv) throws Exception {
        Connection conn = null;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        conn = jdbcTemplate.getDataSource().getConnection();
        Statement st = null;
        st = conn.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
        ResultSet rs = null;
        String tableName = "mdm_data_vee_event";

        long startRn = 1;
        while (true) {
            StringBuffer sqlStrBuff = new StringBuffer();
            sqlStrBuff.append("select t.* from (select rownum rn, dt.* from ");
            sqlStrBuff.append(tableName + " dt ");
            sqlStrBuff.append("where dt.tv >= to_date('");

            sqlStrBuff.append(sdf.format(startTv));
            sqlStrBuff.append("','yyyy-mm-dd hh24:mi:ss') order by dt.tv, dt.object_id) t where rn between ");
            sqlStrBuff.append(startRn);
            sqlStrBuff.append(" and ");
            sqlStrBuff.append(startRn + 100000);
            String sql = sqlStrBuff.toString();
            rs = DatabaseUtils.getInstance().queryForFetch(conn, st, sql, 100000);
            if (rs == null) {
                break;
            }
            long count = handleEvent(rs);
            if(count < 100000) {
                break;
            }
            startRn += 100000;
        }

        try {
            if (st != null)
                st.close();
            if (rs != null)
                rs.close();
            if (conn != null)
                conn.close();
        } catch (SQLException e) {

        }
    }

    private void scanEventMysql(Date startTv) throws Exception {
        Connection conn = null;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        conn = jdbcTemplate.getDataSource().getConnection();
        Statement st = null;
        st = conn.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
        ResultSet rs = null;
        String tableName = "mdm_data_vee_event";

        long startRn = 1;
        while (true) {
            StringBuffer sqlStrBuff = new StringBuffer();
            sqlStrBuff.append("select t.* from (select dt.* from ");
            sqlStrBuff.append(tableName + " dt");
            sqlStrBuff.append(" where dt.tv >=  '");
            sqlStrBuff.append(sdf.format(startTv));
            sqlStrBuff.append("'  order by dt.tv, dt.object_id) t limit  ");
            sqlStrBuff.append(startRn -1 );
            sqlStrBuff.append(" , 100000 ");

            String sql = sqlStrBuff.toString();
            rs = DatabaseUtils.getInstance().queryForFetch(conn, st, sql, 100000);
            if (rs == null) {
                break;
            }
            long count = handleEvent(rs);
            if(count < 100000) {
                break;
            }
            startRn += 100000;
        }

        try {
            if (st != null)
                st.close();
            if (rs != null)
                rs.close();
            if (conn != null)
                conn.close();
        } catch (SQLException e) {

        }
    }

    private void sendMsg(
            String sdpId,
            String loadType,
            Object data) {
        MQMsg<Object> mqMsg = new MQMsg<Object>();
        mqMsg.setLoadType(loadType);
        mqMsg.setFromServiceId(serverConfig.getServiceId());
        mqMsg.setFromServiceType("DATAIMPORT");
        mqMsg.setFromId(sdpId);
        mqMsg.setFromType("SDPID");
        mqMsg.setTopic(producerTopic);
        mqMsg.setTags(producerTags);
        mqMsg.setLoad(data);
        serviceProducer.sendMsg(producer, mqMsg);
    }
}
