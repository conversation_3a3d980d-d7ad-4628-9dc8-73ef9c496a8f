package clouesp.hes.common.DataEntity.Asset;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.IdClass;
import javax.persistence.Table;

/**
 * <AUTHOR>
 *
 */
@Entity
@Table(name= "mdm_asset_vee_rule_param")
@IdClass(MdmAssetVEERuleParamPK.class)
public class MdmAssetVEERuleParam {
	@Id
	@Column(name = "rule_id", nullable = false, columnDefinition = "varchar(32)")
	private String ruleId;
	
	@Id
	@Column(name = "param_key", columnDefinition = "varchar(64)")
	private String paramKey;
	
	@Column(name = "param_value")
	private Double paramValue;
	
	@Column(name = "defaut_value")
	private Double defautValue;
	
	@Column(name = "param_desc", columnDefinition = "varchar(256)")
	private String paramDesc;

	public String getRuleId() {
		return ruleId;
	}

	public void setRuleId(String ruleId) {
		this.ruleId = ruleId;
	}

	public String getParamKey() {
		return paramKey;
	}

	public void setParamKey(String paramKey) {
		this.paramKey = paramKey;
	}

	public Double getParamValue() {
		return paramValue;
	}

	public void setParamValue(Double paramValue) {
		this.paramValue = paramValue;
	}

	public Double getDefautValue() {
		return defautValue;
	}

	public void setDefautValue(Double defautValue) {
		this.defautValue = defautValue;
	}

	public String getParamDesc() {
		return paramDesc;
	}

	public void setParamDesc(String paramDesc) {
		this.paramDesc = paramDesc;
	}

}
