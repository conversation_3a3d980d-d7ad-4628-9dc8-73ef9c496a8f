package clouesp.hes.common.DataEntity.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;

public class DataCalcObjPK implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = -3642306299250717291L;
	
	@Column(name = "calc_obj_id", nullable = false, columnDefinition = "varchar(32)")
	private String calcObjId;
	
	@Column(name = "tv")
	private Date tv;
	
	@Column(name = "scheme_id", columnDefinition = "varchar(32)")
	private String schemeId;
	
	public String getCalcObjId() {
		return calcObjId;
	}

	public void setCalcObjId(String calcObjId) {
		this.calcObjId = calcObjId;
	}

	public Date getTv() {
		return tv;
	}

	public void setTv(Date tv) {
		this.tv = tv;
	}

	public String getSchemeId() {
		return schemeId;
	}

	public void setSchemeId(String schemeId) {
		this.schemeId = schemeId;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((calcObjId == null) ? 0 : calcObjId.hashCode());
		result = prime * result
				+ ((tv == null) ? 0 : tv.hashCode());
		result = prime * result
				+ ((schemeId == null) ? 0 : schemeId.hashCode());
		return result;
	}

	@Override
	public String toString(){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String value = "calcObjId = " + calcObjId + " , tv = " + sdf.format(tv) + " , schemeId = " + schemeId    ;
		return  value ;

	}
	
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		DataCalcObjPK other = (DataCalcObjPK) obj;
		if (calcObjId == null) {
			if (other.calcObjId != null)
				return false;
		} else if (!calcObjId.equals(other.calcObjId))
			return false;
		if (tv == null) {
			if (other.tv != null)
				return false;
		} else if (!tv.equals(other.tv))
			return false;
		if (schemeId == null) {
			if (other.schemeId != null)
				return false;
		} else if (!schemeId.equals(other.schemeId))
			return false;		
		return true;
	}							
}
