package clouesp.hes.core.DataCalc.service.Handler;

import clouesp.hes.common.MqBus.MQMsg;
import clouesp.hes.common.MqBus.ServiceConsumer;
import clouesp.hes.core.DataCalc.Utils.SpringBeanUtils;
import clouesp.hes.core.DataCalc.config.ServerConfig;
import clouesp.hes.core.DataCalc.handler.BaseHandler;
import clouesp.hes.core.DataCalc.handler.HandlerFactory;
import clouesp.hes.core.DataCalc.service.BaseService;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import jline.internal.Log;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

public class HandlerService implements BaseService, MessageListenerOrderly{
	
	private ServiceConsumer serviceConsumer = SpringBeanUtils.getBean(ServiceConsumer.class);
	private HandlerFactory handlerFactory = SpringBeanUtils.getBean(HandlerFactory.class);
	private ServerConfig serverConfig = SpringBeanUtils.getBean(ServerConfig.class);;	
	private List<BaseHandler> handlers = new ArrayList<BaseHandler>();
	
	@Override
	public void startService(String serviceId) {
		String handlerTypeStr = null;
		String consumerInfo = null;
		if ("1001".equals(serviceId)) {
			handlerTypeStr = "VALIDATION";
			consumerInfo = "VALIDATION.DATAIMPORT.DATA";
		} else if ("1003".equals(serviceId)) {
			handlerTypeStr = "CALCENERGY";
			consumerInfo = "CALCENERGY.DATAIMPORT.DATA";
		} else if ("1004".equals(serviceId)) {
			handlerTypeStr = "CALCOBJ";
			consumerInfo = "CALCOBJ.CALCULATION.CALC";
		} else if ("1005".equals(serviceId)) {
			handlerTypeStr = "AUTOESTIMATION";
			consumerInfo = "AUTOESTIMATION.GENERATE.EVENT";
		}
		
		String[] handlerTypes = handlerTypeStr.split(",");		
		for(String handlerType : handlerTypes) {
			BaseHandler handler = handlerFactory.getHandler(serviceId, handlerType);		
			if(handler == null)
				continue;
			handlers.add(handler);
		}
		if(handlers.size() == 0)
			return;
	
		String[] infos = consumerInfo.split("\\.");
		if(infos.length != 3)
			return;	
		
		String prefix = "VEE";
		String consumerGroup = prefix + "_" + infos[0];
		String consumerTopic = prefix + "_" + infos[1];
		
		String[] tags = infos[2].split("\\|\\|");
		String consumerTags = "";
		for(String tag : tags) {
			consumerTags += (prefix + "_" + tag + "||");	
		}
		consumerTags = consumerTags.substring(0, consumerTags.length() - 2);	
		
		String namesrvAddr = serverConfig.getNamesrvAddr();
		serviceConsumer.start(
				namesrvAddr, 
				consumerGroup, 
				consumerTopic, 
				consumerTags,
				MessageModel.CLUSTERING,
				this);
		Log.info("start: serviceId = " + serviceId + " , namesrvAddr = " + namesrvAddr + " , consumerGroup = " + consumerGroup + " , consumerTopic = " + consumerTopic + " , consumerTags = " + consumerTags);
	}

	@Override
	public ConsumeOrderlyStatus consumeMessage(List<MessageExt> msgs, ConsumeOrderlyContext context) {
		try {
			for (MessageExt msg : msgs) {	
				String jsonStr = new String(msg.getBody(), StandardCharsets.UTF_8);
				MQMsg<String> mqMsg = JSONObject.parseObject(jsonStr, new TypeReference<MQMsg<String>>(){});
				if(mqMsg == null)
					continue;
				for(BaseHandler handler : handlers)
					handler.handler(mqMsg);
			}
		} 
		catch (Exception e) {
			
		}
		return ConsumeOrderlyStatus.SUCCESS;
	}

}
