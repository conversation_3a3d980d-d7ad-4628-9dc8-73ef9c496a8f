<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="AssetMeterGroupMapper">
    <select id="getStepTariffData"  parameterType="AssetMeterGroupValue" resultType="AssetMeterGroupValue">
        select *
        from ASSET_METER_GROUP_VALUE  AMGV
        LEFT JOIN DICT_DATAITEM  DD ON DD.ID=AMGV.DATAITEM_ID
        <where>
            1=1
            <if test="entity.groupId !=null and entity.groupId != ''">
                AND AMGV.GROUP_ID =  #{entity.groupId}
            </if>
            <if test="entity.dataitemId !=null and entity.dataitemId != ''">
                AND AMGV.DATAITEM_ID LIKE CONCAT(#{entity.dataitemId},'%')
            </if>
            <if test="entity.xmlValue !=null and entity.xmlValue != ''">
                AND AMGV.XML_VALUE LIKE CONCAT(CONCAT('%', #{entity.xmlValue}),'%')
            </if>
        </where>
        order by DD.NAME
        </select>
    <select id="datagridStepTariff" resultMap="AssetMeterGroupMap"  parameterType="PagingRequest">
        select * from
        (select AMG.*,(SELECT count(*) from ASSET_METER_GROUP_MAP WHERE GROUP_ID=AMG.ID and TYPE=AMG.TYPE)AS meterNumber,
        AMGV.XML_VALUE AS stepStariffTime
        from ASSET_METER_GROUP AMG
        LEFT JOIN ASSET_METER_GROUP_VALUE AMGV ON AMG.ID = AMGV.GROUP_ID AND AMGV.DATAITEM_ID = #{entity.dataitemId}
        WHERE AMG.TYPE = #{entity.type} ) temp
        where 1=1
        <if test="entity.id !=null and entity.id != ''">
            AND temp.ID =  #{entity.id}
        </if>
        <if test="entity.name !=null and entity.name != ''">
            AND temp.NAME LIKE CONCAT(CONCAT('%', #{entity.name}),'%')
        </if>
        <if test="entity.protocolId !=null and entity.protocolId != ''">
            AND temp.PROTOCOL_ID = #{entity.protocolId}
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>
    </select>

    <select id="getMeterGroupList" resultMap="AssetMeterGroupMap"  parameterType="PagingRequest">
        select
            amg.*,
            dp.name as protocolName,
            ddm.name as meterModelName
        from
        asset_meter_group amg
        left join dict_protocol dp on amg.protocol_id = dp.id
        left join dict_device_model ddm on amg.meter_model = ddm.id
        where
        1 = 1
        <if test="entity">
            <if test="entity.id != null and entity.id != ''">
                and amg.id = #{entity.id}
            </if>
            <if test="entity.type != null and entity.type != ''">
                and amg.type = #{entity.type}
            </if>
            <if test="entity.name != null and entity.name != ''">
                and amg.name like concat(concat('%', #{entity.name}),'%')
            </if>
            <if test="entity.protocolId != null and entity.protocolId != ''">
                and amg.protocol_id = #{entity.protocolId}
            </if>
            <if test="entity.meterModel != null and entity.meterModel != ''">
                and amg.meter_model = #{entity.meterModel}
            </if>

        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <resultMap type="com.clou.common.DataEntity.Entity.Store.hes.Asset.AssetMeterGroup" id="AssetMeterGroupMap">
        <id column="id" property="id" />

        <association property="meterCount" column="id"
                     javaType="java.lang.Integer" select="getMeterCount">
        </association>
    </resultMap>

    <select id="getMeterCount" parameterType="String" resultType="java.lang.Integer">
        select count(*) from asset_meter_group_map where type != 0 and group_id=#{id}
    </select>


    <select id="getProfileList" resultType="AssetMeasurementProfile"  parameterType="PagingRequest">
        select
        amp.*,
        dp.name as profileName
        from
        asset_measurement_profile amp left join dict_profile dp on amp.profile_id = dp.id
        where
        1 = 1
        <if test="entity">
            <if test="entity.mgId != null and entity.mgId != ''">
                and amp.mg_id = #{entity.mgId}
            </if>
            <if test="entity.profileType != null">
                and amp.profile_type = #{entity.profileType}
            </if>

            <if test="entity.profileId != null">
                and amp.profile_id = #{entity.profileId}
            </if>

            <if test="entity.protocolCode != null and entity.protocolCode != ''">
                and amp.protocol_code = #{entity.protocolCode}
            </if>
        </if>
        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>
    </select>

    <select id="getUsedProfileDiList" resultType="DictDataitem"  parameterType="PagingRequest">
        select
            amp.dataitem_id as id,
            dd.protocol_id as protocolId,
            dd.protocol_code as protocolCode,
            dd.name as name,
            dd.parse_type as parseType,
            dd.scale as scale,
            dd.show_unit as showUnit

        from
        asset_measurement_profile_di amp
        left join dict_dataitem dd on  amp.dataitem_id = dd.id
        left join dict_profile dp on amp.profile_id = dp.id
        where
        1 = 1
        <if test="entity">
            <if test="entity.profileId != null and entity.profileId != ''">
                and amp.profile_id =  #{entity.profileId}
            </if>
            <if test="entity.mgId != null and entity.mgId != ''">
                and amp.mg_id =  #{entity.mgId}
            </if>

        </if>
        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <select id="getUnusedProfileDiList" resultType="DictDataitem"  parameterType="PagingRequest">
        select
        dpdi.dataitem_id as id,
        dd.protocol_id as protocolId,
        dd.protocol_code as protocolCode,
        dd.name as name,
        dd.parse_type as parseType,
        dd.scale as scale,
        dd.show_unit as showUnit

        from
        dict_profile_data_item dpdi
        left join dict_dataitem dd on  dpdi.dataitem_id = dd.id
        left join dict_profile dp on dpdi.profile_id = dp.id
        where
        1 = 1
        and dpdi.sort_id > 0

        <if test="entity">
            <if test="entity.profileId != null and entity.profileId != ''">
                and dpdi.profile_id =  #{entity.profileId}
                <if test="entity.mgId != null and entity.mgId != ''">
                    and dd.id not in (
                    select dataitem_id from	asset_measurement_profile_di
                    where mg_id = #{entity.mgId}
                    and profile_id = #{entity.profileId})
                </if>
            </if>

        </if>
        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>
















    <select id="getGroupLimiterList" parameterType="AssetMeterGroupValue" resultType="AssetMeterGroupValue">
		select
		amgv.dataitem_id as dataitemId,
		amgv.xml_value as xmlValue ,
		dd.name as name,
		dd.protocol_code as protocolId
		from
			ASSET_METER_GROUP_VALUE amgv
		LEFT JOIN DICT_DATAITEM dd ON dd.id = amgv.DATAITEM_ID
		where 1 = 1
		<if test="groupId != null and groupId != ''">
            and amgv.GROUP_ID = #{groupId}
        </if>
		<if test="dataitemId != null and dataitemId != ''">
            and amgv.DATAITEM_ID like CONCAT(CONCAT('%',#{dataitemId}),'%')
        </if>

	</select>

    <select id="getDictDataItemList" parameterType="PagingRequest" resultType="DictDataitem">
        select * from DICT_DATAITEM 	where 1=1

        <if test="entity.id !=null and entity.id != ''">
            AND ID LIKE CONCAT(CONCAT('%', #{entity.id}),'%')
        </if>
        <if test="entity.name !=null and entity.name != ''">
            AND NAME LIKE CONCAT(CONCAT('%', #{entity.name}),'%')
        </if>
        <if test="entity.protocolCode !=null and entity.protocolCode != ''">
            AND PROTOCOL_CODE LIKE CONCAT(CONCAT('%', #{entity.protocolCode}),'%')
        </if>
        <if test="entity.dataitemType !=null and entity.dataitemType != ''">
            AND DATAITEM_TYPE =#{entity.dataitemType}
        </if>
        <if test="entity.protocolId !=null and entity.protocolId != ''">
            AND protocol_id =#{entity.protocolId}
        </if>
        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>
    </select>

</mapper>