package clouesp.hes.core.DataScan.schedule;

import java.lang.reflect.Method;
import java.util.Objects;

import org.springframework.util.ReflectionUtils;

import clouesp.hes.core.DataScan.Utils.SpringBeanUtils;

public class SchedulingRunnable implements Runnable {
	private String beanName;

	private String methodName;

	private Object[] params;

	public SchedulingRunnable(String beanName, String methodName, Object... params) {
		try {
			this.beanName = beanName;
			this.methodName = methodName;
			this.params = params;
		}
		catch (Exception ex) {
			ex.printStackTrace();
		}		
	}

	@Override
	public void run() {
		try {

			Object target = SpringBeanUtils.getBean(beanName);
			Method method = null;
			if (null != params && params.length > 0) {
				Class<?>[] paramCls = new Class[params.length];
				for (int i = 0; i < params.length; i++) {
					paramCls[i] = params[i].getClass();
				}
				method = target.getClass().getDeclaredMethod(methodName, paramCls);
			} 
			else {
				method = target.getClass().getDeclaredMethod(methodName);
			}
			ReflectionUtils.makeAccessible(method);
			if (null != params && params.length > 0) {
				method.invoke(target, params);
			} else {
				method.invoke(target);
			}

		}
		catch (Exception ex) {
			ex.printStackTrace();
		}
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || getClass() != o.getClass())
			return false;
		SchedulingRunnable that = (SchedulingRunnable) o;
		if (params == null) {
			return beanName.equals(that.beanName) &&
					methodName.equals(that.methodName) &&
					that.params == null;
		}
		return beanName.equals(that.beanName) &&
				methodName.equals(that.methodName) &&
				params.equals(that.params);

	}

	@Override
	public int hashCode() {
		if (params == null) {
			return Objects.hash(beanName, methodName);
		}
		return Objects.hash(beanName, methodName, params);
	}
}
