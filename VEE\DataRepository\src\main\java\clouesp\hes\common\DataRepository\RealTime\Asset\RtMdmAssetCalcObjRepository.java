package clouesp.hes.common.DataRepository.RealTime.Asset;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import clouesp.hes.common.DataEntity.Asset.MdmAssetCalcObj;

public interface RtMdmAssetCalcObjRepository extends JpaRepository<MdmAssetCalcObj, String>{
	List<MdmAssetCalcObj> findByCalcEnable(int calcEnable);
	
	@Query(value = "select * from mdm_asset_calc_obj where "
			+ "id in (select metering_id from mdm_asset_calc_obj_map "
			+ "where id = :calcObjId and metering_type = 2) "
			, nativeQuery = true)
	List<MdmAssetCalcObj> findSubObjs(
			@Param("calcObjId") String calcObjId);
}
