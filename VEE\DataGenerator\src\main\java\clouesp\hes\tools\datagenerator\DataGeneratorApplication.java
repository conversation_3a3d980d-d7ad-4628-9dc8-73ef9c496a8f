package clouesp.hes.tools.datagenerator;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.PropertySource;
import org.springframework.web.client.RestOperations;

import java.io.File;
import java.io.IOException;

@PropertySource("file:${user.dir}/config/application.yml")
@SpringBootApplication
@EnableAutoConfiguration
public class DataGeneratorApplication {

	public static void main(String[] args) {

		try {
			File directory = new File("");// 参数为空
			String courseFile;
			courseFile = directory.getCanonicalPath();
			File parent = new File(courseFile);
			String parentPath = parent.getParent();
			String addClassPath = "spring.config.additional-location:classpath:/";
			addClassPath += "," +parentPath + "/config/";
			new SpringApplicationBuilder(DataGeneratorApplication.class).properties("spring.config.name:application", addClassPath).build().run(args);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		//SpringApplication.run(DataGeneratorApplication.class, args);
	}

	@Bean
	public RestOperations restTemplate (RestTemplateBuilder builder) {
		return builder.build();

	}
}
