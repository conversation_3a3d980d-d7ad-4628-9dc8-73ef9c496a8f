<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="DataAccessMapper">
	<select id="getMeterSnSdpMapList" resultType="clouesp.hes.common.DataMode.Data.ModeMeterSnSdpMap">
		select meter.sn as MeterSn , sdp.id as SdpId from mdm_asset_service_point sdp , asset_meter meter where sdp.meter_id = meter.id
	</select>

</mapper>