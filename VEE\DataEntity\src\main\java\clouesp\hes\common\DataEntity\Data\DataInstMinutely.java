package clouesp.hes.common.DataEntity.Data;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name= "mdm_data_inst_minutely")
public class DataInstMinutely {
	@EmbeddedId
	private DataInstPK dataInstPK = new DataInstPK();
	
	@Column(name = "update_tv")
	private Date updateTv;
	
	@Column(name = "u_a")
	private Double UA;
	
	@Column(name = "u_b")
	private Double UB;
	
	@Column(name = "u_c")
	private Double UC;
	
	@Column(name = "i_a")
	private Double IA;
	
	@Column(name = "i_b")
	private Double IB;
	
	@Column(name = "i_c")
	private Double IC;
	
	@Column(name = "i_0")
	private Double I0;
	
	@Column(name = "ap")
	private Double AP;
	
	@Column(name = "ap_a")
	private Double APA;
	
	@Column(name = "ap_b")
	private Double APB;
	
	@Column(name = "ap_c")
	private Double APC;
	
	@Column(name = "rp")
	private Double RP;
	
	@Column(name = "rp_a")
	private Double RPA;
	
	@Column(name = "rp_b")
	private Double RPB;
	
	@Column(name = "rp_c")
	private Double RPC;
	
	@Column(name = "cos")
	private Double COS;
	
	@Column(name = "cos_a")
	private Double COSA;
	
	@Column(name = "cos_b")
	private Double COSB;
	
	@Column(name = "cos_c")
	private Double COSC;
	
	@Column(name = "sp")
	private Double SP;
	
	@Column(name = "sp_a")
	private Double SPA;
	
	@Column(name = "sp_b")
	private Double SPB;
	
	@Column(name = "sp_c")
	private Double SPC;
	
	@Column(name = "data_source")
	private Integer dataSource = 0;
	
	@Column(name = "data_version")
	private Integer dataVersion = 0;

	public DataInstPK getDataInstPK() {
		return dataInstPK;
	}

	public void setDataInstPK(DataInstPK dataInstPK) {
		this.dataInstPK = dataInstPK;
	}

	public Date getUpdateTv() {
		return updateTv;
	}

	public void setUpdateTv(Date updateTv) {
		this.updateTv = updateTv;
	}

	public Double getUA() {
		return UA;
	}

	public void setUA(Double uA) {
		UA = uA;
	}

	public Double getUB() {
		return UB;
	}

	public void setUB(Double uB) {
		UB = uB;
	}

	public Double getUC() {
		return UC;
	}

	public void setUC(Double uC) {
		UC = uC;
	}

	public Double getIA() {
		return IA;
	}

	public void setIA(Double iA) {
		IA = iA;
	}

	public Double getIB() {
		return IB;
	}

	public void setIB(Double iB) {
		IB = iB;
	}

	public Double getIC() {
		return IC;
	}

	public void setIC(Double iC) {
		IC = iC;
	}

	public Double getI0() {
		return I0;
	}

	public void setI0(Double i0) {
		I0 = i0;
	}

	public Double getAP() {
		return AP;
	}

	public void setAP(Double aP) {
		AP = aP;
	}

	public Double getAPA() {
		return APA;
	}

	public void setAPA(Double aPA) {
		APA = aPA;
	}

	public Double getAPB() {
		return APB;
	}

	public void setAPB(Double aPB) {
		APB = aPB;
	}

	public Double getAPC() {
		return APC;
	}

	public void setAPC(Double aPC) {
		APC = aPC;
	}

	public Double getRP() {
		return RP;
	}

	public void setRP(Double rP) {
		RP = rP;
	}

	public Double getRPA() {
		return RPA;
	}

	public void setRPA(Double rPA) {
		RPA = rPA;
	}

	public Double getRPB() {
		return RPB;
	}

	public void setRPB(Double rPB) {
		RPB = rPB;
	}

	public Double getRPC() {
		return RPC;
	}

	public void setRPC(Double rPC) {
		RPC = rPC;
	}

	public Double getCOS() {
		return COS;
	}

	public void setCOS(Double cOS) {
		COS = cOS;
	}

	public Double getCOSA() {
		return COSA;
	}

	public void setCOSA(Double cOSA) {
		COSA = cOSA;
	}

	public Double getCOSB() {
		return COSB;
	}

	public void setCOSB(Double cOSB) {
		COSB = cOSB;
	}

	public Double getCOSC() {
		return COSC;
	}

	public void setCOSC(Double cOSC) {
		COSC = cOSC;
	}

	public Double getSP() {
		return SP;
	}

	public void setSP(Double sP) {
		SP = sP;
	}

	public Double getSPA() {
		return SPA;
	}

	public void setSPA(Double sPA) {
		SPA = sPA;
	}

	public Double getSPB() {
		return SPB;
	}

	public void setSPB(Double sPB) {
		SPB = sPB;
	}

	public Double getSPC() {
		return SPC;
	}

	public void setSPC(Double sPC) {
		SPC = sPC;
	}

	public Integer getDataSource() {
		return dataSource;
	}

	public void setDataSource(Integer dataSource) {
		this.dataSource = dataSource;
	}

	public Integer getDataVersion() {
		return dataVersion;
	}

	public void setDataVersion(Integer dataVersion) {
		this.dataVersion = dataVersion;
	}
}
