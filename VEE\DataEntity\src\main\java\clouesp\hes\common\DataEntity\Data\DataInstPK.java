package clouesp.hes.common.DataEntity.Data;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Embeddable;

@Embeddable
public class DataInstPK implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = -6786198496589423686L;
	@Column(name = "sdp_id", nullable = false, columnDefinition = "varchar(32)")
	private String sdpId;
	@Column(name = "tv")
	private Date tv;
	@Column(name = "data_type")
	private Integer dataType = 0;	
	
	public String getSdpId() {
		return sdpId;
	}
	public void setSdpId(String sdpId) {
		this.sdpId = sdpId;
	}
	public Date getTv() {
		return tv;
	}
	public void setTv(Date tv) {
		this.tv = tv;
	}
	
	public Integer getDataType() {
		return dataType;
	}
	public void setDataType(Integer dataType) {
		this.dataType = dataType;
	}
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((sdpId == null) ? 0 : sdpId.hashCode());
		result = prime * result
				+ ((tv == null) ? 0 : tv.hashCode());
		result = prime * result
				+ ((dataType == null) ? 0 : dataType.hashCode());
		return result;
	}
	
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		DataInstPK other = (DataInstPK) obj;
		if (sdpId == null) {
			if (other.sdpId != null)
				return false;
		} else if (!sdpId.equals(other.sdpId))
			return false;
		if (tv == null) {
			if (other.tv != null)
				return false;
		} else if (!tv.equals(other.tv))
			return false;
		if (dataType == null) {
			if (other.dataType != null)
				return false;
		} else if (!dataType.equals(other.dataType))
			return false;		
		return true;
	}						
}
