package clouesp.hes.common.DataEntity.Asset;

import java.io.Serializable;

public class MdmAssetCalcObjMapPK implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 3459107531474581286L;
	private String id;
	private Integer type; 
	private String meteringId;
	private Integer calcFormula;
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	public String getMeteringId() {
		return meteringId;
	}
	public void setMeteringId(String meteringId) {
		this.meteringId = meteringId;
	}
	public Integer getCalcFormula() {
		return calcFormula;
	}
	public void setCalcFormula(Integer calcFormula) {
		this.calcFormula = calcFormula;
	}
	
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		result = prime * result
				+ ((type == null) ? 0 : type.hashCode());
		result = prime * result
				+ ((meteringId == null) ? 0 : meteringId.hashCode());
		result = prime * result
				+ ((calcFormula == null) ? 0 : calcFormula.hashCode());
		return result;
	}
	
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		MdmAssetCalcObjMapPK other = (MdmAssetCalcObjMapPK) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		if (type == null) {
			if (other.type != null)
				return false;
		} else if (!type.equals(other.type))
			return false;
		if (meteringId == null) {
			if (other.meteringId != null)
				return false;
		} else if (!meteringId.equals(other.meteringId))
			return false;
		if (calcFormula == null) {
			if (other.calcFormula != null)
				return false;
		} else if (!calcFormula.equals(other.calcFormula))
			return false;
		return true;
	}					
}
