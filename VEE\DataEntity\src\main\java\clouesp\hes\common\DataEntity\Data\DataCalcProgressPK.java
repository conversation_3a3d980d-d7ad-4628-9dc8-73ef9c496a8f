package clouesp.hes.common.DataEntity.Data;

import java.io.Serializable;
import java.util.Objects;

public class DataCalcProgressPK implements Serializable {

    private String sdpId;

    private String schemeId;


    private String loadType;

    public String getSdpId() {
        return sdpId;
    }

    public void setSdpId(String sdpId) {
        this.sdpId = sdpId;
    }

    public String getSchemeId() {
        return schemeId;
    }

    public void setSchemeId(String schemeId) {
        this.schemeId = schemeId;
    }

    public String getLoadType() {
        return loadType;
    }

    public void setLoadType(String loadType) {
        this.loadType = loadType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DataCalcProgressPK that = (DataCalcProgressPK) o;
        return Objects.equals(sdpId, that.sdpId) &&
                Objects.equals(schemeId, that.schemeId) &&
                Objects.equals(loadType, that.loadType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(sdpId, schemeId, loadType);
    }

}
