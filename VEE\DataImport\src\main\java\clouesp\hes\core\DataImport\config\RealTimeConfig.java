package clouesp.hes.core.DataImport.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.apache.ibatis.mapping.VendorDatabaseIdProvider;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.Properties;

@Slf4j
@Configuration
@MapperScan(basePackages = {"clouesp.hes.core.DataImport.dao.RealTime"}, sqlSessionFactoryRef = "realTimeSqlSessionFactory")
public class RealTimeConfig {
	@Bean(name = "realTimeManager")
    @Resource
    public PlatformTransactionManager realTimeManager(@Qualifier("realtimeDS")DataSource dataSource){
        return new DataSourceTransactionManager(dataSource);
    }
	
	 /**
     * 配置Mapper路径
     * @param dataSource
     * @return
     * @throws Exception
     */
    @Bean(name = "realTimeSqlSessionFactory")
    public  SqlSessionFactory realTimeSqlSessionFactory(@Qualifier("realtimeDS") DataSource dataSource) throws Exception  {
        SqlSessionFactoryBean  bean  =  new  SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        //添加XML目录
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        try{
            bean.setMapperLocations(resolver.getResources("classpath:mybatis/RealTime/*/*.xml"));
            bean.setConfigLocation(resolver.getResource("classpath:mybatis/RealTime/mybatis-config.xml"));
            bean.setTypeAliasesPackage("clouesp.hes.common.DataEntity");
            bean.getObject().getConfiguration().setMapUnderscoreToCamelCase(true);
            return  bean.getObject();
        }catch(Exception  e){
           // e.printStackTrace();
            log.error("DataImport Error : " , e);
            throw new RuntimeException(e);
        }
    }
    
    @Bean(name = "realTimeSqlSessionTemplate")
    public SqlSessionTemplate realTimeSqlSessionTemplate(@Qualifier("realTimeSqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        SqlSessionTemplate template = new SqlSessionTemplate(sqlSessionFactory); // 使用上面配置的Factory
        return template;
    }
}
