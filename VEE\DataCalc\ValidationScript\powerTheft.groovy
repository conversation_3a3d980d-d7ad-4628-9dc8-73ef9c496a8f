import clouesp.hes.common.DataEntity.Data.DataVEEEvent

def powerTheft(Map<String, Object> ruleInfo) {
    Integer result = 0;
    DataVEEEvent event = null;
    if (ruleInfo == null ||
            !ruleInfo.containsKey("datas") ||
            !ruleInfo.containsKey("params")) {
        return null;
    }

    List<Map<String, Object>> ruleDatas = ruleInfo.get("datas");
//    Map<String, Object> ruleParams = ruleInfo.get("params");
//    double resultValue = 0;
//    if(ruleParams != null && ruleParams.containsKey("resultValue")) {
//        resultValue = ruleParams.get("resultValue");
//    }
    for(Map<String, Object> ruleData : ruleDatas) {
        String targetClass = ruleData.get("targetClass");
        List<Map<String, Object>> eventList = ruleData.get("meterCoverRemoved");
        if(eventList == null) {
            return null;
        }
        if("EVENT" == targetClass) {
            if(eventList.size() > 0) {
                result = 1;
            }
        }
    }

    if(result.intValue() == 1) {
        event = (DataVEEEvent) ruleInfo.get("event");
    }
    return event;
}