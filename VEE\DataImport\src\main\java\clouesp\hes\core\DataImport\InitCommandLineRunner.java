package clouesp.hes.core.DataImport;

import clouesp.hes.common.DataEntity.Data.DataReg;
import clouesp.hes.common.DataEntity.Data.DataRegPK;
import clouesp.hes.common.DataEntity.System.MdmSysService;
import clouesp.hes.common.DataRepository.Persistence.System.MdmSysServiceAttributeRepository;
import clouesp.hes.common.DataRepository.Persistence.System.MdmSysServiceRepository;
import clouesp.hes.common.DataRepository.RealTime.System.RtMdmSysServiceRepository;
import clouesp.hes.common.logger.logger.Logger;
import clouesp.hes.common.logger.logger.LoggerAppenderType;
import clouesp.hes.common.logger.logger.LoggerLevel;
import clouesp.hes.core.DataImport.config.ServerConfig;
import clouesp.hes.core.DataImport.handler.DataFileHandler;
import clouesp.hes.core.DataImport.handler.EventFileHandler;
import clouesp.hes.core.DataImport.schedule.CronTaskRegistrar;
import clouesp.hes.core.DataImport.schedule.SchedulingRunnable;
import clouesp.hes.core.DataImport.service.Data.DataAccessService;
import clouesp.hes.core.DataImport.service.Data.MeterSdpMapService;
import clouesp.hes.core.DataImport.service.EventImportService;
import clouesp.hes.core.DataImport.service.FtpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Component
public class InitCommandLineRunner implements CommandLineRunner {
	@Autowired
	private DataFileHandler dataFileHandler;
	
	@Autowired
	private EventFileHandler eventFileHandler;
	
	@Autowired
	private CronTaskRegistrar cronTaskRegistrar;
	
	@Resource(name="ftpService")
	private FtpService ftpService;
	
	@Value("${schedule.startTime}")
	private String startTimeStr;
	
	@Value("${schedule.cycle}")
	private int cycle;
	
	@Autowired
    private ServerConfig serverConfig;	
	
	@Autowired
	private RtMdmSysServiceRepository rtMdmSysServiceRepository;	
	
	@Autowired
	private MdmSysServiceRepository mdmSysServiceRepository;		
	
	@Autowired
	private MdmSysServiceAttributeRepository mdmSysServiceAttributeRepository;

	@Autowired
	private MeterSdpMapService meterSdpMapService ;
	
	@Resource(name="eventImportService")
	private EventImportService eventImportService;	
	
	private int updateServiceCount = 0;

//	@Resource(name="dataAccessService")
//	private DataAccessService dataAccessService;
			
	@Override
	public void run(String... args) throws Exception {

//		Calendar cal = Calendar.getInstance();
//		cal.set(Calendar.HOUR_OF_DAY, 0);
//		cal.set(Calendar.MINUTE, 0);
//		cal.set(Calendar.SECOND, 0);
//		cal.set(Calendar.MILLISECOND, 0);
//
//		List<DataReg> dataRegs = new ArrayList<>();
//		DataReg dataReg = new DataReg();
//		DataRegPK pk = new DataRegPK();
//		pk.setSdpId("1");
//		pk.setTv(cal.getTime());
//		dataReg.setDataRegPK(pk);
//		dataReg.setR0P1(1d);
//		dataReg.setR0P1(1.5d);
//		dataReg.setUpdateTv(new Date());
//		dataRegs.add(dataReg);
//
//		dataReg = new DataReg();
//		pk = new DataRegPK();
//		pk.setSdpId("2");
//		pk.setTv(cal.getTime());
//		dataReg.setDataRegPK(pk);
//		dataReg.setR0P1(2d);
//		dataReg.setR0P1(2.5d);
//		dataReg.setUpdateTv(new Date());
//		dataRegs.add(dataReg);
//
//		dataAccessService.batchUpdateSaveRegOracle("mdm_data_reg_dayly", dataRegs);


		log.info("") ;
		log.info("************************************************************");
		log.info("        Ver DataImport 2025041101");
		log.info("************************************************************");
		log.info("") ;
		log.info("Server IP: " + serverConfig.getServerAddress());
		log.info("Server Port: " + serverConfig.getServerPort());
		try {

			startLogger();
			if (initService(2) == -1) {
				return;
			}
			updateServiceState();
			dataFileHandler.init(serverConfig.getServiceId());
			eventFileHandler.init(serverConfig.getServiceId());
			startService();
			meterSdpMapService.Load();
			startSchedule("4001", startTimeStr, "dataImportTask");

		}catch (Exception e){
			log.error("DataImport Error : " , e);
		}
	}
	
	private int initService(int serviceType) {
		String serviceId = rtMdmSysServiceRepository.findServiceId(serviceType, serverConfig.getServerAddress());
 		serviceId = "50010002";
		if (serviceId == null) {
			 log.info("Failed to get the service");
	         Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, null, "0", "System", "Failed to get the service");
			return -1;
		}	
		serverConfig.setServiceId(serviceId);
//		serverConfig.loadCfg();
		String logInfo;
		logInfo = "Startup Param[Service id: " + serverConfig.getServiceId() + "]";
		log.info(logInfo);
		Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "System", logInfo);
		
//		logInfo = "Startup Param[RocketMQ namesrvAddr: " + serverConfig.getNamesrvAddr() + "]";
//		log.info(logInfo);
//		Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "System", logInfo);
		return 0;
	}	
	
    private int startLogger(){
    	
    	try {
	    	File directory = new File("");// 参数为空
	        String courseFile;
			
			courseFile = directory.getCanonicalPath();
			File parent = new File(courseFile);
			String parentPath = parent.getParent() + "//log//";
			 //设置写日志路径
	        Logger.getInstance().setIndexPath(parentPath);
	        //输出源固定为LoggerAppenderType.Lucene
	        Logger.getInstance().setAppenderType(LoggerAppenderType.Lucene);
	        //启动日志
	        Logger.getInstance().start();
	        //设置日志级别
	        Logger.getInstance().setLevel(LoggerLevel.INFO);       
			
		} catch (Exception e) {
			// TODO Auto-generated catch block
			//e.printStackTrace();
			log.error("DataImport Error : " , e);

		}
        return 0;
    }	
	
	private void startService() {
		ftpService.startService(serverConfig.getServiceId());
	}
	
	private void startSchedule(String serviceId, String startTv, String beanName) throws Exception{
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		
		Date startTime = sdf.parse(startTimeStr);
		Date endTime = sdf.parse("2020-12-31 23:59:59");
		String taskCycleType = "hourly";
		int taskCycle = cycle;
				
		Calendar cal = Calendar.getInstance();
        cal.setTime(startTime);
        int startSec = cal.get(Calendar.SECOND);
        int startMin = cal.get(Calendar.MINUTE);
        int startHour = cal.get(Calendar.HOUR_OF_DAY);
        int startDay = cal.get(Calendar.DAY_OF_MONTH);
        
        cal.setTime(endTime);
        int endHour = cal.get(Calendar.HOUR_OF_DAY);
        String cron = null;
        
        if (taskCycleType.equalsIgnoreCase("minutely")) {     
            String timeSecs = startSec + " ";
            String timeMins = startMin + "/" + taskCycle + " ";
            String timeHours = startHour + "-" + endHour + " ";                   
            cron = timeSecs + timeMins + timeHours
                + "* * ?";                 	
        }
        else if (taskCycleType.equalsIgnoreCase("hourly")) {
            String timeSecs = startSec + " ";
            String timeMins = startMin + " ";
            String timeHours = startHour + "-" + endHour + "/" + taskCycle + " ";                   
            cron = timeSecs + timeMins + timeHours
                + "* * ?";          
        }
        else if (taskCycleType.equalsIgnoreCase("daily")) {        
            String timeSecs = startSec + " ";
            String timeMins = startMin + " ";                       
            String timeHours = startHour + " ";                   
            String timeDays = startDay + "/" + taskCycle + " ";
            
            cron = timeSecs + timeMins + timeHours + timeDays
                   + "* ?";
             
        }
        else if (taskCycleType.equalsIgnoreCase("monthly")) {              
            String timeSecs = startSec + " ";
            String timeMins = startMin + " ";                       
            String timeHours = startHour + " ";
            String timeDays = startDay + " ";
            String timeMonths = "1/" + taskCycle + " ";        
            cron = timeSecs + timeMins + timeHours + timeDays + timeMonths                 
                   + "?";            
        }
                
		if (cron != null) {
			SchedulingRunnable task = new SchedulingRunnable(beanName, "dispatch");
			cronTaskRegistrar.addCronTask(task, cron);
		}
	}
	
	@Scheduled(fixedDelay = 60000)
	private void scheduledServiceState() {
		if (serverConfig.getServiceId() == null) {
			return;
		}

		int cycle = 10;
		try {
			List<MdmSysService> mdmSysServices = rtMdmSysServiceRepository.findByServiceType(6);
			String appServiceId = null;
			if (mdmSysServices != null && mdmSysServices.size() > 0) {
				appServiceId = mdmSysServices.get(0).getId();
			}
			if (appServiceId != null) {
				String strCycle = mdmSysServiceAttributeRepository.findValue(appServiceId,
						"Application.UpdateModuleStatus.Cycle");

				try {
					cycle = Integer.parseInt(strCycle);
				} catch (Exception e) {
                   log.error("Error Message : " ,e);
				}
			}

			if (updateServiceCount < cycle) {
				updateServiceCount++;
				return;
			}
			updateServiceCount = 0;
			updateServiceState();
		} catch (Exception e) {
			log.error("Error Message : ",e);
		}
	}
	
	private void updateServiceState() {
		Optional<MdmSysService> optionalMdmSysService = rtMdmSysServiceRepository.findById(serverConfig.getServiceId());
		if (!optionalMdmSysService.isPresent()) {
			return;
		}
		MdmSysService mdmSysService = optionalMdmSysService.get();
		mdmSysService.setOnlineTime(new Date());
		mdmSysServiceRepository.save(mdmSysService);
	}	
}
