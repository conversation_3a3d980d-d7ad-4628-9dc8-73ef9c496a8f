package clouesp.hes.common.DataMode.Statistics;

import lombok.Data;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
@Data
public class MdmStatisticsObjPK implements Serializable {

    private static final long serialVersionUID = -364230622343717291L;
    private String  orgId ;//组织机构ID
    private String  id ;//统计类型ID，字典值1145
    private String  idType ;//统计子类型ID, 根据统计类型确定
    private Date tv ; //统计时间

    public  void SetTvOnlyDate(Date date){
        Calendar calendar=Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        tv = calendar.getTime();
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public Date getTv() {
        return tv;
    }

    public void setTv(Date tv) {
        this.tv = tv;
    }
}
