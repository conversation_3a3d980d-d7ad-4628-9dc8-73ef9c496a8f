package clouesp.hes.common.MqBus;

import java.io.Serializable;

public class MQMsg<T> implements Serializable{
	private static final long serialVersionUID = 3846764805399648470L;
	private String msgId;
	private String fromServiceId;
	private String fromServiceType;
	private String fromId;
	private String fromType;
	private String topic;
	private String tags;
	private String transMode;
	private String toServiceId;
	private String toServiceType;
	private String toId;
	private String toType;		
	private String loadType;
	
	private T load;
	public String getMsgId() {
		return msgId;
	}
	public void setMsgId(String msgId) {
		this.msgId = msgId;
	}	
	public String getFromServiceId() {
		return fromServiceId;
	}
	public void setFromServiceId(String fromServiceId) {
		this.fromServiceId = fromServiceId;
	}
	public String getFromServiceType() {
		return fromServiceType;
	}
	public void setFromServiceType(String fromServiceType) {
		this.fromServiceType = fromServiceType;
	}
	public String getFromId() {
		return fromId;
	}
	public void setFromId(String fromId) {
		this.fromId = fromId;
	}
	public String getFromType() {
		return fromType;
	}
	public void setFromType(String fromType) {
		this.fromType = fromType;
	}
	public String getTopic() {
		return topic;
	}
	public void setTopic(String topic) {
		this.topic = topic;
	}	
	public String getTags() {
		return tags;
	}
	public void setTags(String tags) {
		this.tags = tags;
	}	
	public String getTransMode() {
		return transMode;
	}
	public void setTransMode(String transMode) {
		this.transMode = transMode;
	}
	public String getToServiceId() {
		return toServiceId;
	}
	public void setToServiceId(String toServiceId) {
		this.toServiceId = toServiceId;
	}
	public String getToServiceType() {
		return toServiceType;
	}
	public void setToServiceType(String toServiceType) {
		this.toServiceType = toServiceType;
	}
	public String getToId() {
		return toId;
	}
	public void setToId(String toId) {
		this.toId = toId;
	}
	public String getToType() {
		return toType;
	}
	public void setToType(String toType) {
		this.toType = toType;
	}
	public String getLoadType() {
		return loadType;
	}
	public void setLoadType(String loadType) {
		this.loadType = loadType;
	}	
	public T getLoad() {
		return load;
	}
	public void setLoad(T load) {
		this.load = load;
	}
}
