package clouesp.hes.core.DataCalc.service.StatisData;


import clouesp.hes.common.DataMode.Statistics.OutageEvtStatics;
import clouesp.hes.common.DataMode.Statistics.MdmDataOutageStatistics;
import clouesp.hes.common.DataMode.Statistics.MdmStatisticsObj;
import clouesp.hes.core.DataCalc.dao.Persistence.DaoSupport;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("statisticsAccessService")
public class StatisticsAccessService {
    @Resource(name = "daoSupport")
    private DaoSupport dao;

    public  void batchSaveMdmStatisticsObj(List<MdmStatisticsObj> mdmStatisticsObjs) throws Exception{
        dao.save("StatisticsAccessMapper.batchSaveMdmStatisticsObj",mdmStatisticsObjs);
    }

    public  void batchSaveMdmStatisticsOutageObj(List<MdmDataOutageStatistics> mdmStatisticsObjs) throws Exception{
        dao.save("StatisticsAccessMapper.batchSaveOutageStatisticsObj",mdmStatisticsObjs);
    }

    public  void deleteAllProgessStatistics()  throws Exception{
        dao.save("StatisticsAccessMapper.deleteAllProgessStatistics",null) ;
    }

    public  List<OutageEvtStatics> findCurMonthOutageStatics()  throws Exception{
       return  (List<OutageEvtStatics>)(dao.findForList("StatisticsAccessMapper.findCurMonthOutageStatics",null) );
    }

    public  List<OutageEvtStatics> findYesterdayOutageStatics()  throws Exception{
        return  (List<OutageEvtStatics>)(dao.findForList("StatisticsAccessMapper.findYesterdayOutageStatics",null) );
    }
}
