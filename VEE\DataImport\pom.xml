<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.4.0</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>clouesp.hes.core</groupId>
	<artifactId>DataImport</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>DataImport</name>
	<description>DataExport</description>

	<properties>
		<java.version>1.8</java.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-to-slf4j</artifactId>
                </exclusion>
            </exclusions>					
		</dependency>
		<dependency>
			<groupId>org.mybatis.spring.boot</groupId>
			<artifactId>mybatis-spring-boot-starter</artifactId>
			<version>2.1.4</version>
		</dependency>
		
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>		

		<dependency>
			<groupId>com.h2database</groupId>
			<artifactId>h2</artifactId>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>com.oracle.database.jdbc</groupId>
			<artifactId>ojdbc8</artifactId>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		
		<dependency>
	      <groupId>net.sourceforge.javacsv</groupId>
	      <artifactId>javacsv</artifactId>
	      <version>2.0</version>
   	 	</dependency>
   	 	
 		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>
			
		<dependency>
			<groupId>clouesp.hes.common</groupId>
			<artifactId>CommonUtils</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>	

		<dependency>
			<groupId>clouesp.hes.common</groupId>
			<artifactId>DataRepository</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		
		<dependency>
			<groupId>clouesp.hes.common</groupId>
			<artifactId>DataModel</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		
<!--		<dependency>-->
<!--			<groupId>clouesp.hes.common</groupId>-->
<!--			<artifactId>MqBus</artifactId>-->
<!--			<version>0.0.1-SNAPSHOT</version>-->
<!--		</dependency>-->
		
		<dependency>
			<groupId>clouesp.hes.common.logger</groupId>
			<artifactId>logger</artifactId>
			<version>0.0.1-SNAPSHOT</version>		
		</dependency>	
		
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

</project>
