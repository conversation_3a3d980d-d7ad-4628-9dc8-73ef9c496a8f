package clouesp.hes.common.MqBus;

import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;
import org.springframework.stereotype.Component;

@Component
public class ServiceConsumer {	

	public void start(
			String namesrvAddr,
			String mqGroup,
			String mqTopic,
			String mqTag,
			MessageModel messageModel,
			MessageListenerOrderly listener
			) {

		try {
			DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(mqGroup);
			consumer.setNamesrvAddr(namesrvAddr);
			consumer.subscribe(mqTopic, mqTag);
			consumer.setConsumeThreadMin(10);
			consumer.setConsumeThreadMax(20);
			consumer.setMessageModel(messageModel);
			consumer.setMessageListener(listener);
			consumer.setConsumeTimeout(60000);
			consumer.setMqClientApiTimeout(300000);
			consumer.start();			
			
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

}
