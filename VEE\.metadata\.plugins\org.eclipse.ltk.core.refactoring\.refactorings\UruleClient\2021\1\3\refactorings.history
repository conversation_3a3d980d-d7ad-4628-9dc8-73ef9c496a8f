<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;UruleClient&apos;&#x0D;&#x0A;- Original project: &apos;UruleClient&apos;&#x0D;&#x0A;- Original element: &apos;RuleUtils.java&apos;" description="Delete element" element1="src/main/java/clouesp/hes/core/UruleClient/utils/RuleUtils.java" elements="0" flags="589830" id="org.eclipse.jdt.ui.delete" resources="1" stamp="1610426503953" subPackages="false" version="1.0"/>&#x0A;<refactoring comment="Move &apos;RuleHandler.java&apos; to &apos;RuleMsg&apos;" description="Move &apos;RuleHandler.java&apos; to &apos;RuleMsg&apos;" destination="src/main/java/clouesp/hes/core/UruleClient/handler/RuleMsg" element1="src/main/java/clouesp/hes/core/UruleClient/handler/RuleHandler.java" flags="7" id="org.eclipse.ltk.core.refactoring.move.resources" resources="1" stamp="1610445461874" updateReferences="true"/>
</session>