<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="UpgradeMapper">

    <select id="getFwuPlanList" resultType="DataFwuPlan"  parameterType="Request">
        select
        dfp.*,
        ddm.name as modelName,
        dm.name as manufacturerName,

        (SELECT COUNT(*) done FROM DATA_FWU_JOB j WHERE j.PLAN_ID=dfp.ID AND j.STATE='2') as doneCount,
        (SELECT COUNT(*) expired FROM DATA_FWU_JOB j WHERE j.PLAN_ID=dfp.ID AND j.STATE='3') as cancelCount,
        (SELECT COUNT(*) running FROM DATA_FWU_JOB j WHERE j.PLAN_ID=dfp.ID AND j.STATE='1') as runningCount,
        (SELECT COUNT(*) running FROM DATA_FWU_JOB j WHERE j.PLAN_ID=dfp.ID AND j.STATE='4') as waitingCount
        from
        data_fwu_plan dfp
        left join dict_device_model ddm on dfp.device_model = ddm.id
        left join dict_manufacturer dm on dfp.manufacturer_id = dm.id



        where
        1 = 1
        <if test="entity">
            <if test="entity.manufacturerId != null and entity.manufacturerId != ''">
                and dfp.manufacturer_id = #{entity.manufacturerId}
            </if>
            <if test="entity.modelId != null and entity.modelId != ''">
                and dfp.device_model = #{entity.modelId}
            </if>
            <if test="entity.deviceType != null and entity.deviceType != ''">
                and dfp.device_type = #{entity.deviceType}
            </if>
            <if test="entity.currentVesion != null and entity.currentVesion != ''">
                and dfp.current_vesion = #{entity.currentVesion}
            </if>
            <if test="entity.newVersion != null and entity.newVersion != ''">
                and dfp.new_version = #{entity.newVersion}
            </if>
            <if test="entity.expired == 0">
                and dfp.expiry_time &gt; #{params.curTime}
            </if>
            <if test="entity.expired == 1">
                and dfp.expiry_time &lt;= #{params.curTime}
            </if>
            <if test="entity.state != null and entity.state != ''">
                and dfp.state = #{entity.state}
            </if>
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <select id="getFwuJobList_old" resultType="DataFwuJob"  parameterType="Request">
        select
            a.*,
            ddm.name as modelName,
            dm.name as manufacturerName
        from
            (select
            dfj.*,

            case when am.id is not null
            then am.sn
            when ac.id is not null
            then ac.sn
            else
            null
            end as deviceSn,

            case when am.id is not null
            then am.model
            when ac.id is not null
            then ac.model
            else
            null
            end as model,

            case when am.id is not null
            then am.manufacturer
            when ac.id is not null
            then ac.manufacturer
            else
            null
            end as manufacturer

            from
            data_fwu_job dfj
            left join asset_meter am on dfj.device_type in ('1', '3', '5', '10','11') and dfj.device_id = am.id
            left join asset_communicator ac on dfj.device_type in ('2', '8') and dfj.device_id = ac.id) a
            left join dict_device_model ddm on a.model = ddm.id
            left join dict_manufacturer dm on a.manufacturer = dm.id
        where
            a.deviceSn is not null
        <if test="entity">
            <if test="entity.manufacturerId != null and entity.manufacturerId != ''">
                and dm.id = #{entity.manufacturerId}
            </if>
            <if test="entity.modelId != null and entity.modelId != ''">
                and ddm.id = #{entity.modelId}
            </if>
            <if test="entity.deviceSn != null and entity.deviceSn != ''">
                and a.deviceSn like concat(concat('%', #{entity.deviceSn}),'%')
            </if>

            <if test="entity.deviceType != null and entity.deviceType != ''">
                and a.device_type = #{entity.deviceType}
            </if>
            <if test="entity.currentVesion != null and entity.currentVesion != ''">
                and a.current_vesion = #{entity.currentVesion}
            </if>
            <if test="entity.newVersion != null and entity.newVersion != ''">
                and a.new_version = #{entity.newVersion}
            </if>
            <if test="entity.planId != null and entity.planId != ''">
                and a.plan_id = #{entity.planId}
            </if>
            <if test="entity.state != null and entity.state != ''">
                and a.state = #{entity.state}
            </if>
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>
    <select id="getFwuJobList" resultType="DataFwuJob"  parameterType="Request">
        <choose>
            <when test="entity.meterType=='Commnuicator'">
               select * from (
               <include refid="getFwuJobList_Commnuicator_ID" />
                )a
            </when>
            <when test="entity.meterType=='Meter'">
                select * from (
               <include refid="getFwuJobList_Meter_ID"/>
                )a
            </when>
            <otherwise>
                select * from (
                <include refid="getFwuJobList_All_ID"/>
                )a
            </otherwise>
        </choose>




    </select>
    <select id="getFwuJobList_COUNT" resultType="java.lang.Long"  parameterType="Request">
        <choose>
            <when test="entity.meterType=='Commnuicator'">

                select count(1) from (
                <include refid="getFwuJobList_Commnuicator_ID"/>
                    ) a
            </when>
            <when test="entity.meterType=='Meter'">
                select count(1) from (
                <include refid="getFwuJobList_Meter_ID" />
                ) a
            </when>
            <when test="entity.meterType=='All'">
                select count(1) from (
                <include refid="getFwuJobList_All_ID" />
                ) a
            </when>
        </choose>
    </select>
    <select id="getCurVerList" resultType="java.lang.String"  parameterType="Request">
    select
       distinct fw_version
    from
        asset_meter
    where
        fw_version is not null

        order by fw_version
    </select>


    <select id="getCommunicatorCurVerList" resultType="java.lang.String"  parameterType="Request">
    select
        distinct fw_version
    from
        asset_communicator
    where
        fw_version is not null

        order by fw_version
    </select>

    <select id="getNewVerList" resultType="java.lang.String"  parameterType="Request">
    select
       distinct new_version
    from
        data_fwu_plan
    where
        new_version is not null

        order by new_version
    </select>

    <sql id="getFwuJobList_Meter_ID">
        SELECT
        dfj.id
        FROM
        data_fwu_job dfj
        WHERE
        (  device_type = '1'
        OR device_type = '3'
        OR device_type = '5'
        OR device_type = '10'
        OR device_type = '11' )
        <if test="(entity.modelId != null and entity.modelId != '') or (entity.manufacturerId != null and entity.manufacturerId != '') or (entity.deviceSn != null and entity.deviceSn != '')">
            and dfj.DEVICE_ID in (
                select id from asset_meter ac where 1=1
            <if test="entity.modelId != null and entity.modelId != ''">
                AND ac.MODEL = #{entity.modelId}
            </if>
            <if test="entity.manufacturerId != null and entity.manufacturerId != ''">
                AND ac.MANUFACTURER = #{entity.manufacturerId}
            </if>

            <if test="entity.deviceSn != null and entity.deviceSn != ''">
                and ac.sn like concat(concat('%', #{entity.deviceSn}),'%')
            </if>
            )
        </if>
        <if test="entity.currentVesion != null and entity.currentVesion != ''">
            AND CURRENT_VESION = #{entity.currentVesion}
        </if>
        <if test="entity.newVersion != null and entity.newVersion != ''">
            AND NEW_VERSION = #{entity.newVersion}
        </if>
        <if test="entity.state != null and entity.state != ''">
            AND STATE = #{entity.state}
        </if>
        <if test="entity.deviceType != null and entity.deviceType != ''">
            AND device_type = #{entity.deviceType}
        </if>
        <if test="entity.planId != null and entity.planId != ''">
            and plan_id = #{entity.planId}
        </if>
        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>
    </sql>
    <sql id="getFwuJobList_Commnuicator_ID">
        SELECT
        dfj.id
        FROM
        data_fwu_job dfj
        WHERE
        ( device_type = '2' OR device_type = '8' )
        <if test="(entity.modelId != null and entity.modelId != '') or (entity.manufacturerId != null and entity.manufacturerId != '') or (entity.deviceSn != null and entity.deviceSn != '')">
            and dfj.DEVICE_ID in (
            select id from asset_communicator ac  where 1=1
            <if test="entity.modelId != null and entity.modelId != ''">
                AND ac.MODEL = #{entity.modelId}
            </if>
            <if test="entity.manufacturerId != null and entity.manufacturerId != ''">
                AND ac.MANUFACTURER = #{entity.manufacturerId}
            </if>

            <if test="entity.deviceSn != null and entity.deviceSn != ''">
                and ac.sn like concat(concat('%', #{entity.deviceSn}),'%')
            </if>
            )
        </if>
        <if test="entity.currentVesion != null and entity.currentVesion != ''">
            AND CURRENT_VESION = #{entity.currentVesion}
        </if>
        <if test="entity.newVersion != null and entity.newVersion != ''">
            AND NEW_VERSION = #{entity.newVersion}
        </if>
        <if test="entity.state != null and entity.state != ''">
            AND STATE = #{entity.state}
        </if>
        <if test="entity.deviceType != null and entity.deviceType != ''">
            AND device_type = #{entity.deviceType}
        </if>
        <if test="entity.planId != null and entity.planId != ''">
            and plan_id = #{entity.planId}
        </if>


        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>
    </sql>
    <sql id="getFwuJobList_All_ID">
        select a.id from (<include refid="getFwuJobList_Meter_ID"/>) a
        union all
        select b.id from (<include refid="getFwuJobList_Commnuicator_ID"/>) b
    </sql>
    <sql id="getFwuJobList_All_ID_bak">
        SELECT
        id
        FROM
        data_fwu_job
        WHERE
        1=1
        <if test="(entity.modelId != null and entity.modelId != '') or (entity.manufacturerId != null and entity.manufacturerId != '') or (entity.deviceSn != null and entity.deviceSn != '')">
          and  (DEVICE_ID IN (
            SELECT
            ac.id
            FROM
            asset_meter ac
            WHERE
            1 = 1
            AND ac.sn IS NOT NULL
            <if test="entity.modelId != null and entity.modelId != ''">
                AND ac.MODEL = #{entity.modelId}
            </if>
            <if test="entity.manufacturerId != null and entity.manufacturerId != ''">
                AND ac.MANUFACTURER = #{entity.manufacturerId}
            </if>

            <if test="entity.deviceSn != null and entity.deviceSn != ''">
                and ac.sn like concat(concat('%', #{entity.deviceSn}),'%')
            </if>
            )
            or
            DEVICE_ID IN (
            SELECT
            ac.id
            FROM
            asset_communicator ac
            WHERE
            1 = 1
            AND ac.sn IS NOT NULL
            <if test="entity.modelId != null and entity.modelId != ''">
                AND ac.MODEL = #{entity.modelId}
            </if>
            <if test="entity.manufacturerId != null and entity.manufacturerId != ''">
                AND ac.MANUFACTURER = #{entity.manufacturerId}
            </if>

            <if test="entity.deviceSn != null and entity.deviceSn != ''">
                and ac.sn like concat(concat('%', #{entity.deviceSn}),'%')
            </if>
            ))
        </if>

        <if test="entity.currentVesion != null and entity.currentVesion != ''">
            AND CURRENT_VESION = #{entity.currentVesion}
        </if>
        <if test="entity.newVersion != null and entity.newVersion != ''">
            AND NEW_VERSION = #{entity.newVersion}
        </if>
        <if test="entity.state != null and entity.state != ''">
            AND STATE = #{entity.state}
        </if>
        <if test="entity.deviceType != null and entity.deviceType != ''">
            AND device_type = #{entity.deviceType}
        </if>
        <if test="entity.planId != null and entity.planId != ''">
            and plan_id = #{entity.planId}
        </if>


        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>
    </sql>
</mapper>