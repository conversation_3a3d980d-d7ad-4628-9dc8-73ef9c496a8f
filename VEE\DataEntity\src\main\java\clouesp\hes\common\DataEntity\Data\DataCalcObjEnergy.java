package clouesp.hes.common.DataEntity.Data;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name= "mdm_data_calc_obj_energy")
public class DataCalcObjEnergy {
	@EmbeddedId
	private DataCalcObjPK dataCalcObjPK = new DataCalcObjPK();
	
	@Column(name = "R0P1")
	private Double R0P1 = 0d;
	@Column(name = "R0P2")
	private Double R0P2 = 0d;

	@Column(name = "R1P1")
	private Double R1P1 = 0d;
	@Column(name = "R1P2")
	private Double R1P2 = 0d;
	
	@Column(name = "R2P1")
	private Double R2P1 = 0d;
	@Column(name = "R2P2")
	private Double R2P2 = 0d;
	
	@Column(name = "R3P1")
	private Double R3P1 = 0d;
	@Column(name = "R3P2")
	private Double R3P2 = 0d;
	
	@Column(name = "R4P1")
	private Double R4P1 = 0d;
	@Column(name = "R4P2")
	private Double R4P2 = 0d;
	
	@Column(name = "R0P1A1", columnDefinition = "decimal(21,6)")
	private Double R0P1A1;
	@Column(name = "R0P1A2", columnDefinition = "decimal(21,6)")
	private Double R0P1A2;
	@Column(name = "R0P1A3", columnDefinition = "decimal(21,6)")
	private Double R0P1A3;
	@Column(name = "R0P1A4", columnDefinition = "decimal(21,6)")
	private Double R0P1A4;
	
	@Column(name = "R1P1A1", columnDefinition = "decimal(21,6)")
	private Double R1P1A1;
	@Column(name = "R1P1A2", columnDefinition = "decimal(21,6)")
	private Double R1P1A2;
	@Column(name = "R1P1A3", columnDefinition = "decimal(21,6)")
	private Double R1P1A3;
	@Column(name = "R1P1A4", columnDefinition = "decimal(21,6)")
	private Double R1P1A4;
	
	
	@Column(name = "R2P1A1", columnDefinition = "decimal(21,6)")
	private Double R2P1A1;
	@Column(name = "R2P1A2", columnDefinition = "decimal(21,6)")
	private Double R2P1A2;
	@Column(name = "R2P1A3", columnDefinition = "decimal(21,6)")
	private Double R2P1A3;
	@Column(name = "R2P1A4", columnDefinition = "decimal(21,6)")
	private Double R2P1A4;
	
	@Column(name = "R3P1A1", columnDefinition = "decimal(21,6)")
	private Double R3P1A1;
	@Column(name = "R3P1A2", columnDefinition = "decimal(21,6)")
	private Double R3P1A2;
	@Column(name = "R3P1A3", columnDefinition = "decimal(21,6)")
	private Double R3P1A3;
	@Column(name = "R3P1A4", columnDefinition = "decimal(21,6)")
	private Double R3P1A4;
	
	@Column(name = "R4P1A1", columnDefinition = "decimal(21,6)")
	private Double R4P1A1;
	@Column(name = "R4P1A2", columnDefinition = "decimal(21,6)")
	private Double R4P1A2;
	@Column(name = "R4P1A3", columnDefinition = "decimal(21,6)")
	private Double R4P1A3;
	@Column(name = "R4P1A4", columnDefinition = "decimal(21,6)")
	private Double R4P1A4;
	
	@Column(name = "dataSource")
	private Integer dataSource;
	
	@Column(name = "update_tv")
	private Date updateTv;
	
	@Column(name = "data_version")
	private Integer dataVersion;
	
	@Column(name = "total_number")
	private Integer totalNumber;
	
	@Column(name = "miss_number")
	private Integer missNumber;
	
	@Column(name = "integrity_rate")
	private Double integrityRate;

	public DataCalcObjPK getDataCalcObjPK() {
		return dataCalcObjPK;
	}

	public void setDataCalcObjPK(DataCalcObjPK dataCalcObjPK) {
		this.dataCalcObjPK = dataCalcObjPK;
	}

	public Double getR0P1() {
		return R0P1;
	}

	public void setR0P1(Double r0p1) {
		R0P1 = r0p1;
	}

	public Double getR0P2() {
		return R0P2;
	}

	public void setR0P2(Double r0p2) {
		R0P2 = r0p2;
	}

	public Double getR1P1() {
		return R1P1;
	}

	public void setR1P1(Double r1p1) {
		R1P1 = r1p1;
	}

	public Double getR1P2() {
		return R1P2;
	}

	public void setR1P2(Double r1p2) {
		R1P2 = r1p2;
	}

	public Double getR2P1() {
		return R2P1;
	}

	public void setR2P1(Double r2p1) {
		R2P1 = r2p1;
	}

	public Double getR2P2() {
		return R2P2;
	}

	public void setR2P2(Double r2p2) {
		R2P2 = r2p2;
	}

	public Double getR3P1() {
		return R3P1;
	}

	public void setR3P1(Double r3p1) {
		R3P1 = r3p1;
	}

	public Double getR3P2() {
		return R3P2;
	}

	public void setR3P2(Double r3p2) {
		R3P2 = r3p2;
	}

	public Double getR4P1() {
		return R4P1;
	}

	public void setR4P1(Double r4p1) {
		R4P1 = r4p1;
	}

	public Double getR4P2() {
		return R4P2;
	}

	public void setR4P2(Double r4p2) {
		R4P2 = r4p2;
	}
	
	public Double getR0P1A1() {
		return R0P1A1;
	}

	public void setR0P1A1(Double r0p1a1) {
		R0P1A1 = r0p1a1;
	}

	public Double getR0P1A2() {
		return R0P1A2;
	}

	public void setR0P1A2(Double r0p1a2) {
		R0P1A2 = r0p1a2;
	}

	public Double getR0P1A3() {
		return R0P1A3;
	}

	public void setR0P1A3(Double r0p1a3) {
		R0P1A3 = r0p1a3;
	}

	public Double getR0P1A4() {
		return R0P1A4;
	}

	public void setR0P1A4(Double r0p1a4) {
		R0P1A4 = r0p1a4;
	}

	public Double getR1P1A1() {
		return R1P1A1;
	}

	public void setR1P1A1(Double r1p1a1) {
		R1P1A1 = r1p1a1;
	}

	public Double getR1P1A2() {
		return R1P1A2;
	}

	public void setR1P1A2(Double r1p1a2) {
		R1P1A2 = r1p1a2;
	}

	public Double getR1P1A3() {
		return R1P1A3;
	}

	public void setR1P1A3(Double r1p1a3) {
		R1P1A3 = r1p1a3;
	}

	public Double getR1P1A4() {
		return R1P1A4;
	}

	public void setR1P1A4(Double r1p1a4) {
		R1P1A4 = r1p1a4;
	}

	public Double getR2P1A1() {
		return R2P1A1;
	}

	public void setR2P1A1(Double r2p1a1) {
		R2P1A1 = r2p1a1;
	}

	public Double getR2P1A2() {
		return R2P1A2;
	}

	public void setR2P1A2(Double r2p1a2) {
		R2P1A2 = r2p1a2;
	}

	public Double getR2P1A3() {
		return R2P1A3;
	}

	public void setR2P1A3(Double r2p1a3) {
		R2P1A3 = r2p1a3;
	}

	public Double getR2P1A4() {
		return R2P1A4;
	}

	public void setR2P1A4(Double r2p1a4) {
		R2P1A4 = r2p1a4;
	}

	public Double getR3P1A1() {
		return R3P1A1;
	}

	public void setR3P1A1(Double r3p1a1) {
		R3P1A1 = r3p1a1;
	}

	public Double getR3P1A2() {
		return R3P1A2;
	}

	public void setR3P1A2(Double r3p1a2) {
		R3P1A2 = r3p1a2;
	}

	public Double getR3P1A3() {
		return R3P1A3;
	}

	public void setR3P1A3(Double r3p1a3) {
		R3P1A3 = r3p1a3;
	}

	public Double getR3P1A4() {
		return R3P1A4;
	}

	public void setR3P1A4(Double r3p1a4) {
		R3P1A4 = r3p1a4;
	}

	public Double getR4P1A1() {
		return R4P1A1;
	}

	public void setR4P1A1(Double r4p1a1) {
		R4P1A1 = r4p1a1;
	}

	public Double getR4P1A2() {
		return R4P1A2;
	}

	public void setR4P1A2(Double r4p1a2) {
		R4P1A2 = r4p1a2;
	}

	public Double getR4P1A3() {
		return R4P1A3;
	}

	public void setR4P1A3(Double r4p1a3) {
		R4P1A3 = r4p1a3;
	}

	public Double getR4P1A4() {
		return R4P1A4;
	}

	public void setR4P1A4(Double r4p1a4) {
		R4P1A4 = r4p1a4;
	}

	public Integer getDataSource() {
		return dataSource;
	}

	public void setDataSource(Integer dataSource) {
		this.dataSource = dataSource;
	}

	public Date getUpdateTv() {
		return updateTv;
	}

	public void setUpdateTv(Date updateTv) {
		this.updateTv = updateTv;
	}

	public Integer getDataVersion() {
		return dataVersion;
	}

	public void setDataVersion(Integer dataVersion) {
		this.dataVersion = dataVersion;
	}

	public Integer getTotalNumber() {
		return totalNumber;
	}

	public void setTotalNumber(Integer totalNumber) {
		this.totalNumber = totalNumber;
	}

	public Integer getMissNumber() {
		return missNumber;
	}

	public void setMissNumber(Integer missNumber) {
		this.missNumber = missNumber;
	}

	public Double getIntegrityRate() {
		return integrityRate;
	}

	public void setIntegrityRate(Double integrityRate) {
		this.integrityRate = integrityRate;
	}	
}
