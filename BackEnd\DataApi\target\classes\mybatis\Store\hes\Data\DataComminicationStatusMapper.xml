<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="DataComminicationStatusMapper">
<!--    <select id="getCommunicationStatusReportList_COUNT" parameterType="PagingRequest"-->
<!--            resultType="java.lang.Long">-->
<!--        <if test="entity.deviceType == 1 ">-->
<!--            select-->

<!--                count(*)-->

<!--            from asset_meter meter-->
<!--            left join data_comminication_status statu on statu.Communicator_Id=meter.Id-->

<!--            where-->
<!--            1 = 1-->

<!--            <if test="params.orgIdList != null and params.orgIdList.size > 0">-->
<!--                and meter.org_id in-->
<!--                <foreach collection="params.orgIdList" item="item" separator="," open="(" close=")">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--            </if>-->

<!--            AND meter.org_id  in (select id from sys_org where org_code like  concat(#{entity.orgCode},'%'))-->
<!--            <if test="entity.devSn!='' and entity.devSn!=null">-->
<!--                <if test="entity.devSnType == null  or  entity.devSnType == 1">-->
<!--                    AND meter.sn =  #{entity.devSn}-->
<!--                </if>-->
<!--                <if test="entity.devSnType != null  and entity.devSnType == 2">-->
<!--                    and dcu.sn  = #{entity.devSn}-->
<!--                </if>-->
<!--            </if>-->
<!--         </if>-->
<!--        <if test="entity.deviceType == 2 ">-->
<!--            select-->
<!--                count(*)-->
<!--            from-->
<!--                asset_communicator dcu-->
<!--                left join data_comminication_status statu on statu.Communicator_Id=dcu.Id-->
<!--            where-->
<!--            1 = 1-->

<!--            <if test="params.orgIdList != null and params.orgIdList.size > 0">-->
<!--                and dcu.org_id in-->
<!--                <foreach collection="params.orgIdList" item="item" separator="," open="(" close=")">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--            </if>-->

<!--            AND dcu.org_id  in (select id from sys_org where org_code like  concat(#{entity.orgCode},'%'))-->
<!--            AND dcu.Device_Type !='201'-->
<!--            <if test="entity.devSn!='' and entity.devSn!=null  ">-->
<!--                AND dcu.sn =  #{entity.devSn}-->
<!--            </if>-->
<!--        </if>-->

<!--        <if test="entity.comStatus ==0">-->
<!--            <if test="entity.offlineTime != null   ">-->
<!--                AND (statu.COM_STATUS is null or ( statu.Com_Status='0' and <![CDATA[  statu.UPDATE_TV <= #{entity.offlineTime} ]]>))-->
<!--            </if>-->
<!--            <if test="entity.offlineTime == null  ">-->
<!--                AND (statu.COM_STATUS is null or statu.Com_Status='0')-->
<!--            </if>-->
<!--        </if>-->

<!--        <if test="entity.comStatus ==1">-->
<!--            AND statu.COM_STATUS = #{entity.comStatus}-->
<!--        </if>-->

<!--        <if test="sortList != null and sortList.size > 0">-->
<!--            order by-->
<!--            <foreach collection="sortList" item="item" separator="," open=" " close=" ">-->
<!--                ${item.field} ${item.sort}-->
<!--            </foreach>-->
<!--        </if>-->
<!--    </select>-->


    <select id="getCommunicationStatusReportList" parameterType="PagingRequest"
            resultType="com.clou.common.DataEntity.Model.Data.DataComminicationStatusResult">
        <if test="entity.deviceType == 1 ">
            select 'Meter' as devTypeName , meter.sn as deviceSn, mcommutype.name as meterCommuType,dcommutype.name as dcuCommuType,dcu.Sn as commuSn,statu.Update_Tv as statuUpdateTv,
            (case  when  ip_addr is null   then null else concat(concat(statu.ip_addr,':' ),  statu.ip_port) end ) as ipAddress ,dcu.Sim_Num as simNum,org.name as orgName,
                statu.Com_Status  AS comStatus
                from asset_meter meter
                    left join asset_communicator dcu on meter.Communicator_Id=dcu.Id
                    left join data_comminication_status statu on statu.Communicator_Id=meter.Id
                left join sys_org org on org.id=dcu.org_id
                left join dict_communication_type mcommutype on  meter.com_type = mcommutype.id
                 left join dict_communication_type dcommutype on  dcu.com_type = dcommutype.id
                where
                    1 = 1

                <if test="params.orgIdList != null and params.orgIdList.size > 0">
                    and meter.org_id in
                    <foreach collection="params.orgIdList" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>

                 AND meter.org_id  in (select id from sys_org where org_code like  concat(#{entity.orgCode},'%'))
                <if test="entity.devSn!='' and entity.devSn!=null">
                    <if test="entity.devSnType == null  or  entity.devSnType == 1">
                     AND meter.sn =  #{entity.devSn}
                    </if>
                    <if test="entity.devSnType != null  and entity.devSnType == 2">
                        and dcu.sn  = #{entity.devSn}
                    </if>
                </if>
        </if>
        <if test="entity.deviceType == 2 ">
            select 'Communicator' as devTypeName , dcu.Sn as deviceSn, commutype.name as dcuCommuType,statu.Update_Tv as statuUpdateTv,
            (case  when  ip_addr is null   then null else concat(concat(statu.ip_addr,':' ),  statu.ip_port) end ) as ipAddress ,dcu.Sim_Num as simNum,org.name as orgName,
            statu.Com_Status AS comStatus
            from  asset_communicator dcu
            left join data_comminication_status statu on statu.Communicator_Id=dcu.Id
            left join sys_org org on org.id=dcu.org_id
            left join dict_communication_type commutype on  dcu.com_type = commutype.id
            where
                1 = 1

            <if test="params.orgIdList != null and params.orgIdList.size > 0">
                and dcu.org_id in
                <foreach collection="params.orgIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            AND dcu.org_id  in (select id from sys_org where org_code like  concat(#{entity.orgCode},'%'))
            AND dcu.Device_Type !='201'
            <if test="entity.devSn!='' and entity.devSn!=null  ">
                AND dcu.sn =  #{entity.devSn}
            </if>
        </if>

            <if test="entity.comStatus ==0">
                <if test="entity.offlineTime != null   ">
                    AND (statu.COM_STATUS is null or ( statu.Com_Status='0' and <![CDATA[  statu.UPDATE_TV <= #{entity.offlineTime} ]]>))
                </if>
                <if test="entity.offlineTime == null  ">
                    AND (statu.COM_STATUS is null or statu.Com_Status='0')
                </if>
            </if>

            <if test="entity.comStatus ==1">
                AND statu.COM_STATUS = #{entity.comStatus}
            </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>
    </select>

</mapper>