import clouesp.hes.common.CommonUtils.ObjectUtils
import clouesp.hes.common.DataEntity.Data.DataReg
import clouesp.hes.common.DataEntity.Data.DataVEEEvent

def regAbnormalSmaller(Map<String, Object> ruleInfo) {
    Integer result = 0;
    DataVEEEvent event = null;
    if (ruleInfo == null ||
            !ruleInfo.containsKey("datas") ||
            !ruleInfo.containsKey("params")) {
        return null;
    }

    List<Map<String, Object>> ruleDatas = ruleInfo.get("datas");
    Map<String, Object> ruleParams = ruleInfo.get("params");
    double resultValue = 0;
    if(ruleParams != null && ruleParams.containsKey("resultValue")) {
        resultValue = ruleParams.get("resultValue");
    }
    for(Map<String, Object> ruleData : ruleDatas) {
        String targetClass = ruleData.get("targetClass");
        //List<Map<String, Object>> dataList = ruleData.get("dataListDay");
        //获取数据，根据数据周期取值
        String dataKey = "dataListDay";
        Integer schemeType = new Integer(ruleInfo.get("schemeType").toString());
        if(schemeType == 1)
        {
            dataKey = "dataListMinute";
        }
        else if(schemeType == 2)
        {
            dataKey = "dataListDay";
        }
        else if(schemeType == 3)
        {
            dataKey = "dataListMonth";
        }
        List<Map<String, Object>> dataList = ruleData.get(dataKey);
        if(dataList == null) {
            return null;
        }
        if("REG" == targetClass) {
            if(dataList.size() == 3) {
                Double[] values = new Double[dataList.size()]
                int index = 0;
                for(Map<String, Object> data : dataList) {
                    DataReg dataReg = ObjectUtils.convertMapToObject(data, DataReg.class);
                    values[index] = dataReg.getR0P1();
                    if(values[index] == null) {
                        break;
                    }
                    index++;
                }
                if(index == values.length) {
                    //一定要是递增的三个数
                    if(values[2].doubleValue() > values[1].doubleValue()
                            && values[1].doubleValue() > values[0].doubleValue())
                    {
                        double diff1 = values[1].doubleValue() - values[0].doubleValue();
                        double diff2 = values[2].doubleValue() - values[1].doubleValue();
                        if(diff2 < diff1 / resultValue) {
                            result = 1
                        }
                    }

                }
            }
        }
    }

    if(result.intValue() == 1) {
        event = (DataVEEEvent) ruleInfo.get("event");
    }
    return event;
}