package clouesp.hes.common.DataRepository.Persistence.Data;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import clouesp.hes.common.DataEntity.Data.DataRegMonthly;
import clouesp.hes.common.DataEntity.Data.DataRegPK;

public interface DataRegMonthlyRepository extends JpaRepository<DataRegMonthly, DataRegPK>{
	List<DataRegMonthly> findByDataRegPKSdpIdAndDataRegPKTvIn(
			String sdpId, 
			List<Date> tvs
			);	
}
