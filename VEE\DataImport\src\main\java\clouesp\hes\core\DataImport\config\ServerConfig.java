package clouesp.hes.core.DataImport.config;

import clouesp.hes.common.DataEntity.System.MdmSysService;
import clouesp.hes.common.DataRepository.RealTime.System.RtMdmSysServiceAttributeRepository;
import clouesp.hes.common.DataRepository.RealTime.System.RtMdmSysServiceRepository;
import clouesp.hes.common.logger.logger.Logger;
import clouesp.hes.common.logger.logger.LoggerLevel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.web.context.WebServerInitializedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.net.InetAddress;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
@Slf4j
@Component
public class ServerConfig implements ApplicationListener<WebServerInitializedEvent>{
	private int serverPort;
	private String serviceId;
	private String namesrvAddr;

	@Autowired
	@Qualifier("persistenceDS")
	private DataSource dataSource;
	
	@Autowired
	private RtMdmSysServiceRepository rtMdmSysServiceRepository;	
	
	@Autowired
	private RtMdmSysServiceAttributeRepository rtMdmSysServiceAttributeRepository;	
	
	@Override
	public void onApplicationEvent(WebServerInitializedEvent event) {
		// TODO Auto-generated method stub
		serverPort = event.getWebServer().getPort();
	}
	
//	public void loadCfg() {
//		List<MdmSysService> mdmSysServices = rtMdmSysServiceRepository.findByServiceType(10);
//		if (mdmSysServices == null || mdmSysServices.size() == 0) {
//			String logInfo = "Not find Rocket MQ service!";
//		//	System.out.println(logInfo);
//			log.error(logInfo);
//			Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, getServiceId(), "0", "System", logInfo);
//			return;
//		}
//		MdmSysService mdmSysService = mdmSysServices.get(0);
//		String namesrvIp = rtMdmSysServiceAttributeRepository.findValue(mdmSysService.getId(), "RocketMQ.IP");
//		String namesrvPort = rtMdmSysServiceAttributeRepository.findValue(mdmSysService.getId(), "RocketMQ.Port");
//		setNamesrvAddr(namesrvIp + ":" + namesrvPort);
//	}

	public String getServerAddress() {
		InetAddress address = null;
        try {
            address = InetAddress.getLocalHost();
        } catch (Exception e) {
            //e.printStackTrace();
			log.error("DataImport Error : " , e);
        }
        return address.getHostAddress();
	}

	public int getServerPort() {
		return serverPort;
	}

	public String getServiceId() {
		return serviceId;
	}

	public void setServiceId(String serviceId) {
		this.serviceId = serviceId;
	}

//	public String getNamesrvAddr() {
//		return namesrvAddr;
//	}
//
//	public void setNamesrvAddr(String namesrvAddr) {
//		this.namesrvAddr = namesrvAddr;
//	}

	public String getDatabaseType() throws Exception {
		Connection connection = dataSource.getConnection();
		try {
			DatabaseMetaData metaData = connection.getMetaData();
			String databaseProductName = metaData.getDatabaseProductName().toLowerCase();
			if (databaseProductName.contains("mysql")) {
				return "MySQL";
			} else if (databaseProductName.contains("oracle")) {
				return "Oracle";
			}
			return databaseProductName;
		} finally {
			connection.close();
		}
	}
}
