package clouesp.hes.core.DataCalc.handler.Calculation;

import clouesp.hes.common.DataEntity.Asset.MdmAssetCalcObj;
import clouesp.hes.common.DataEntity.Asset.MdmAssetCalcScheme;
import clouesp.hes.common.DataEntity.Data.DataCalcObj;
import clouesp.hes.common.DataEntity.Data.DataCalcObjEnergy;
import clouesp.hes.common.DataEntity.Data.DataCalcObjMiss;
import clouesp.hes.common.DataEntity.Dict.DictDetail;
import clouesp.hes.common.DataRepository.Persistence.Asset.MdmAssetCalcObjRepository;
import clouesp.hes.common.DataRepository.Persistence.Data.DataCalcObjMissRepository;
import clouesp.hes.common.DataRepository.Persistence.Dict.DictDetailRepository;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetCalcObjRepository;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetCalcSchemeRepository;
import clouesp.hes.common.MqBus.MQMsg;
import clouesp.hes.common.MqBus.ServiceProducer;
import clouesp.hes.common.logger.logger.Logger;
import clouesp.hes.common.logger.logger.LoggerLevel;
import clouesp.hes.core.DataCalc.Utils.SpringBeanUtils;
import clouesp.hes.core.DataCalc.config.ServerConfig;
import clouesp.hes.core.DataCalc.handler.BaseHandler;
import clouesp.hes.core.DataCalc.service.Data.CalcAccessService;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import jline.internal.Log;
import org.apache.rocketmq.client.producer.DefaultMQProducer;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.LinkedBlockingQueue;

public class CalcObjHandler implements BaseHandler {

    private HandlerProc handlerProc = null;
    private Thread handlerProcThread = null;

    private DataSaveProc dataSaveProc = null;
    private Thread dataSaveProcThread = null;
    private long lastSaveTime = System.currentTimeMillis();
    private long lastSaveEnergyTime = System.currentTimeMillis();

    private LinkedBlockingQueue<MdmAssetCalcObj> assetCalcObjQueue = new LinkedBlockingQueue<MdmAssetCalcObj>();
    private LinkedBlockingQueue<DataCalcObj> dataCalcObjQueue = new LinkedBlockingQueue<DataCalcObj>();
    private LinkedBlockingQueue<DataCalcObjEnergy> dataCalcObjEnergyQueue = new LinkedBlockingQueue<DataCalcObjEnergy>();


    private RtMdmAssetCalcObjRepository rtMdmAssetCalcObjRepository = SpringBeanUtils.getBean(RtMdmAssetCalcObjRepository.class);
    private CalcAccessService calcAccessService = SpringBeanUtils.getBean(CalcAccessService.class);
    private ServerConfig serverConfig = SpringBeanUtils.getBean(ServerConfig.class);

    private Map<Integer, String> mapCalcFormula = new HashMap<Integer, String>();
    private Map<Integer, String> mapAssetColName = new HashMap<Integer, String>();

    private SimpleDateFormat sdflog = new SimpleDateFormat("MM-dd HH:mm:ss");

    private RtMdmAssetCalcSchemeRepository rtMdmAssetCalcSchemeRepository = SpringBeanUtils.getBean(RtMdmAssetCalcSchemeRepository.class);
    private DictDetailRepository dictDetailRepository = SpringBeanUtils.getBean(DictDetailRepository.class);

    private DataCalcObjMissRepository dataCalcObjMissRepository = SpringBeanUtils.getBean(DataCalcObjMissRepository.class);

    private MdmAssetCalcObjRepository mdmAssetCalcObjRepository = SpringBeanUtils.getBean(MdmAssetCalcObjRepository.class);
    private String producerGroup;
    private String producerTopic;
    private String producerTags;
    private DefaultMQProducer producer;

    private ServiceProducer serviceProducer = SpringBeanUtils.getBean(ServiceProducer.class);



    @Override
    public void init(String serviceId) {
        String prefix = "VEE";
        String producerInfo = "CALCOBJECT.DATAIMPORT.DATA";
        String[] infos = producerInfo.split("\\.");
        producerGroup = prefix + "_" + infos[0];
        producerTopic = prefix + "_" + infos[1];
        producerTags = prefix + "_" + infos[2];
        String namesrvAddr = serverConfig.getNamesrvAddr();
        producer = serviceProducer.start(namesrvAddr, producerGroup);

        List<DictDetail> dictDetailList = dictDetailRepository.findByDictId("1100");
        for (DictDetail dictDetail : dictDetailList) {
            mapCalcFormula.put(dictDetail.getInnerValue(), dictDetail.getReadme());
        }

        mapAssetColName.put(1, "industry_type");

        if (dataSaveProcThread == null) {
            dataSaveProc = new DataSaveProc();
            dataSaveProcThread = new Thread(dataSaveProc);
            dataSaveProcThread.start();
        }
        if (handlerProcThread == null) {
            handlerProc = new HandlerProc();
            handlerProcThread = new Thread(handlerProc);
            handlerProcThread.start();
        }
    }

    @Override
    public <T> void handler(MQMsg<T> mqMsg) {
        if (mqMsg == null)
            return;
        if (mqMsg.getLoad() == null)
            return;
        String jsonStr = mqMsg.getLoad().toString();
        MdmAssetCalcObj calcObj = JSONObject.parseObject(jsonStr, new TypeReference<MdmAssetCalcObj>() {
        });
        try {
            Log.info("#####------------------------------------------------------------------------" + calcObj.getId());
            Log.info("Put calc obj id:" + calcObj.getId());
            Log.info("#####------------------------------------------------------------------------" + calcObj.getId());
            assetCalcObjQueue.put(calcObj);

        } catch (InterruptedException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    private class HandlerProc implements Runnable {

        private void handleCalc(
                DataCalcObj dataObj,
                MdmAssetCalcObj calcObj,
                Date calcTv,
                int schemeType,
                String schemeId) {

            String tableName = null;
            if (schemeType == 1) {
                tableName = "mdm_data_energy_minutely";
            } else if (schemeType == 2) {
                tableName = "mdm_data_energy_dayly";
            } else if (schemeType == 3) {
                tableName = "mdm_data_energy_monthly";
            }

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            try {
                Map<Object, Object> params = new HashMap<Object, Object>();
                params.put("objectId", calcObj.getId());
                params.put("veeCalculationGroupId", calcObj.getVeeCalculationGroupId());
                params.put("getAsset", calcObj.getGetAsset());

                Integer assetType = calcObj.getAssetType();
                if (assetType != null) {
                    if (mapAssetColName.containsKey(assetType)) {
                        params.put("assetColName", mapAssetColName.get(assetType));
                        params.put("assetColValue", calcObj.getAssetId());
                    }
                }
                int totalCount = calcAccessService.querySdpCount(params);
                int dataCount = 0;

                params.put("tableName", tableName);
                params.put("calcTv", "'" + sdf.format(calcTv) + "'");
                params.put("schemeId", schemeId);

                List<DataCalcObjMiss> missList = calcAccessService.queryMissList(params);
                if (missList != null && missList.size() > 0) {
                    dataCalcObjMissRepository.saveAll(missList);
                }

                for (Entry<Integer, String> entery : mapCalcFormula.entrySet()) {

                    int inTotalCount = 0;
                    int outTotalCount = 0;

                    params.put("ioType", 1);
                    params.put("colName", entery.getValue());
                    params.put("calcFormula", entery.getKey());

                    Map<Object, Object> inData = calcAccessService.querySumValue(params);
                    if (inData.containsKey("CALCVALUE")) {
                        double inTotal = Double.parseDouble(inData.get("CALCVALUE").toString());
                        dataObj.setInTotal(dataObj.getInTotal() + inTotal);
                        inTotalCount = Integer.parseInt(inData.get("COUNTTOTAL").toString());
                    }

                    if (calcObj.getGetAsset() == null || calcObj.getGetAsset() != 1) {
                        params.put("ioType", 2);
                        Map<Object, Object> outData = calcAccessService.querySumValue(params);
                        if (outData.containsKey("CALCVALUE")) {
                            double outValue = Double.parseDouble(outData.get("CALCVALUE").toString());
                            dataObj.setOutTotal(dataObj.getOutTotal() + outValue);
                            outTotalCount = Integer.parseInt(outData.get("COUNTTOTAL").toString());
                        }
                    }
                    dataCount += (inTotalCount + outTotalCount);
                }
                int missCount = totalCount - dataCount;
                dataObj.setTotalNumber(dataObj.getTotalNumber() + totalCount);
                dataObj.setMissData(dataObj.getMissData() + missCount);
                List<MdmAssetCalcObj> subCalcObjs = mdmAssetCalcObjRepository.findSubObjs(calcObj.getId());
                if (subCalcObjs == null || subCalcObjs.size() == 0)
                    return;
                for (MdmAssetCalcObj subCalcObj : subCalcObjs) {
                    handleCalc(dataObj, subCalcObj, calcTv, schemeType, schemeId);
                }

            } catch (Exception e) {
                // TODO Auto-generated catch block
				e.printStackTrace();
            }
        }

        private int handle(int count) {
            if (assetCalcObjQueue.size() == 0)
                return 0;
            if (count > assetCalcObjQueue.size())
                count = assetCalcObjQueue.size();

            Log.info("-------assetCalcObjQueue Size:-------: " + assetCalcObjQueue.size());

            List<MdmAssetCalcObj> calcObjs = new ArrayList<MdmAssetCalcObj>();
            assetCalcObjQueue.drainTo(calcObjs, count);

            for (MdmAssetCalcObj calcObj : calcObjs) {

                Log.info("-------Calc Obj Id Start-------: " + calcObj.getId());
                long startTime = System.currentTimeMillis();

                String schemeIds = calcObj.getSchemeIds();
                if (schemeIds == null)
                    continue;

                String[] schemeIdSplit = schemeIds.split(",");
                for (String schemeId : schemeIdSplit) {

                    Optional<MdmAssetCalcScheme> optionalScheme = rtMdmAssetCalcSchemeRepository.findById(schemeId);
                    if (!optionalScheme.isPresent()) {
                        continue;
                    }
                    MdmAssetCalcScheme scheme = optionalScheme.get();
                    int schemeType = scheme.getType();


                    if (schemeType == 1) {
                        continue;
                    }


                    int field = 0;
                    int amount = 0;
                    if (schemeType == 1) {
                        field = Calendar.MINUTE;
                        amount = scheme.getCycleNum();
                    } else if (schemeType == 2) {
                        field = Calendar.DAY_OF_MONTH;
                        amount = 1;
                    } else if (schemeType == 3) {
                        field = Calendar.MONTH;
                        amount = 1;
                    }
                    if (field == 0) {
                        continue;
                    }

                    Calendar calStart = Calendar.getInstance();
                    Date calcStartTv = calcObj.getCalcStartTv();
                    Date calcEndTv = calcObj.getCalcEndTv();

                    if (calcStartTv == null && calcEndTv == null) {
                        calStart.set(Calendar.MILLISECOND, 0);
                        calStart.set(Calendar.SECOND, 0);

                        if (schemeType == 1) {
                            calStart.set(Calendar.MINUTE, 0);
                            calStart.add(Calendar.HOUR_OF_DAY, 0);
                        } else if (schemeType == 2) {
                            calStart.set(Calendar.MINUTE, 0);
                            calStart.set(Calendar.HOUR_OF_DAY, 0);
                            calStart.add(Calendar.DAY_OF_MONTH, -1);
                        } else if (schemeType == 3) {
                            calStart.set(Calendar.MINUTE, 0);
                            calStart.set(Calendar.HOUR_OF_DAY, 0);
                            calStart.set(Calendar.DAY_OF_MONTH, 1);
                            calStart.add(Calendar.MONTH, -1);
                        }
                        calcEndTv = calStart.getTime();
                        if (schemeType == 1) {
                            calStart.add(Calendar.DAY_OF_MONTH, -1);
                        } else if (schemeType == 2) {
                            calStart.add(Calendar.DAY_OF_MONTH, -15);
                        } else if (schemeType == 3) {
                            calStart.add(Calendar.MONTH, -6);
                        }

                    } else {
                        calStart.setTime(calcStartTv);
                        Calendar calEnd = Calendar.getInstance();
                        calEnd.setTime(calcEndTv);

                        calStart.set(Calendar.MILLISECOND, 0);
                        calStart.set(Calendar.SECOND, 0);

                        calEnd.set(Calendar.MILLISECOND, 0);
                        calEnd.set(Calendar.SECOND, 0);

                        if (schemeType == 1) {
                            calStart.set(Calendar.MINUTE, 0);
                            calEnd.set(Calendar.MINUTE, 0);
                        } else if (schemeType == 2) {
                            calStart.set(Calendar.MINUTE, 0);
                            calStart.set(Calendar.HOUR_OF_DAY, 0);

                            calEnd.set(Calendar.MINUTE, 0);
                            calEnd.set(Calendar.HOUR_OF_DAY, 0);
                        } else if (schemeType == 3) {
                            calStart.set(Calendar.MINUTE, 0);
                            calStart.set(Calendar.HOUR_OF_DAY, 0);
                            calStart.set(Calendar.DAY_OF_MONTH, 1);

                            calEnd.set(Calendar.MINUTE, 0);
                            calEnd.set(Calendar.HOUR_OF_DAY, 0);
                            calEnd.set(Calendar.DAY_OF_MONTH, 1);
                        }
                    }
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                    while (true) {
                        Date calcTv = calStart.getTime();

                        Log.info("Calc Tv: " + sdf.format(calcTv));

                        DataCalcObj dataCalcObj = new DataCalcObj();
                        dataCalcObj.getDataCalcObjPK().setCalcObjId(calcObj.getId());
                        dataCalcObj.getDataCalcObjPK().setSchemeId(schemeId);
                        dataCalcObj.getDataCalcObjPK().setTv(calcTv);

                        if ( serverConfig.isOracleDb() )
                              dataCalcObjMissRepository.oracleClearMiss(calcObj.getId(), sdf.format(calcTv), schemeId);
                        else
                            dataCalcObjMissRepository.mysqlClearMiss(calcObj.getId(), sdf.format(calcTv), schemeId);

                        handleCalc(dataCalcObj, calcObj, calcTv, schemeType, schemeId);
                        try {
                            double calcValue = 0;
                            if (calcObj.getType() == 1) {
                                if (dataCalcObj.getInTotal() != 0)
                                    calcValue = (dataCalcObj.getInTotal() - dataCalcObj.getOutTotal())
                                            / dataCalcObj.getInTotal();
                                calcValue = (double) Math.round(10000 * calcValue) / 100;
                            } else {
                                calcValue = dataCalcObj.getInTotal() + dataCalcObj.getOutTotal();
                            }

                            dataCalcObj.setCalcValue(calcValue);
                            dataCalcObj.setUpdateTv(new Date());
                            if (calcObj.getType() == 1) {
                                dataCalcObjQueue.put(dataCalcObj);
                                sendMsg(dataCalcObj.getDataCalcObjPK().getCalcObjId(), "LOSS", dataCalcObj);

                            } else if (calcObj.getType() == 2) {
                                DataCalcObjEnergy dataCalcObjEnergy = new DataCalcObjEnergy();
                                dataCalcObjEnergy.setDataCalcObjPK(dataCalcObj.getDataCalcObjPK());
                                dataCalcObjEnergy.setR0P1(dataCalcObj.getCalcValue());
                                dataCalcObjEnergy.setUpdateTv(dataCalcObj.getUpdateTv());
                                dataCalcObjEnergy.setDataSource(0);
                                dataCalcObjEnergy.setDataVersion(0);

                                dataCalcObjEnergy.setMissNumber(dataCalcObj.getMissData());
                                dataCalcObjEnergy.setTotalNumber(dataCalcObj.getTotalNumber());
                                double integrityRate = 0;
                                if (dataCalcObj.getTotalNumber() > 0) {
                                    integrityRate = (double) Math
                                            .round(10000 * (dataCalcObj.getTotalNumber() - dataCalcObj.getMissData())
                                                    / dataCalcObj.getTotalNumber())
                                            / 100;
                                }
                                dataCalcObjEnergy.setIntegrityRate(integrityRate);
                                dataCalcObjEnergyQueue.put(dataCalcObjEnergy);
                            }

                        } catch (InterruptedException e) {
                            // TODO Auto-generated catch block
                            e.printStackTrace();
                        }

                        calStart.add(field, amount);
                        if (calStart.getTime().getTime() > calcEndTv.getTime()) {
                            break;
                        }
                    }

                    Log.info("-------Calc Obj Id End-------: " + calcObj.getId() + "[" + (System.currentTimeMillis() - startTime) + "]");

                }
            }

            return count;
        }

        private void sendMsg(
                String calcObjId,
                String loadType,
                Object data) {
            MQMsg<Object> mqMsg = new MQMsg<Object>();
            mqMsg.setLoadType(loadType);
            mqMsg.setFromServiceId(serverConfig.getServiceId());
            mqMsg.setFromServiceType("CALCOBJECT");
            mqMsg.setFromId(calcObjId);
            mqMsg.setFromType("CALCOBJID");
            mqMsg.setTopic(producerTopic);
            mqMsg.setTags(producerTags);
            mqMsg.setLoad(data);
            serviceProducer.sendMsg(producer, mqMsg);
        }

        @Override
        public void run() {
            long tv = System.currentTimeMillis() ;
            while (true) {
                int ret = 0;
                ret = handle(10);
                if (ret == 0) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        // TODO Auto-generated catch block
//						e.printStackTrace();
                    }
                }
            }

        }

    }

    private class DataSaveProc implements Runnable {
        private int dataSaveObj(int count) {
            if (dataCalcObjQueue.size() == 0)
                return 0;
            if (System.currentTimeMillis() - lastSaveTime < 15000
                    && dataCalcObjQueue.size() < 100)
                return 0;
            if (count > dataCalcObjQueue.size())
                count = dataCalcObjQueue.size();
            lastSaveTime = System.currentTimeMillis();
            List<DataCalcObj> datas = new ArrayList<DataCalcObj>();
            dataCalcObjQueue.drainTo(datas, count);
            try {
                long startTime = System.currentTimeMillis();
                calcAccessService.batchSaveCalcObj(datas);
                String loggerInfo = "Into mdm_data_calc_obj[count: " + count + ", time: "
                        + (System.currentTimeMillis() - startTime) + "]";
                Log.info(loggerInfo);
                Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "Database", loggerInfo);


            } catch (Exception e) {
//				e.printStackTrace();
                dataCalcObjQueue.addAll(datas);
            }
            return count;
        }

        private int dataSaveObjEnergy(int count) {
            if (dataCalcObjEnergyQueue.size() == 0)
                return 0;
            if (System.currentTimeMillis() - lastSaveEnergyTime < 15000
                    && dataCalcObjEnergyQueue.size() < 100)
                return 0;
            if (count > dataCalcObjEnergyQueue.size())
                count = dataCalcObjEnergyQueue.size();
            lastSaveEnergyTime = System.currentTimeMillis();
            List<DataCalcObjEnergy> datas = new ArrayList<DataCalcObjEnergy>();
            dataCalcObjEnergyQueue.drainTo(datas, count);
            try {
                long startTime = System.currentTimeMillis();

                calcAccessService.batchSaveCalcObjEnergy(datas);

                String loggerInfo = "Into mdm_data_calc_obj_energy[count: " + count + ", time: "
                        + (System.currentTimeMillis() - startTime) + "]";
                Log.info(loggerInfo);
                Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "Database", loggerInfo);


            } catch (Exception e) {
//				e.printStackTrace();
                dataCalcObjEnergyQueue.addAll(datas);
            }
            return count;
        }

        @Override
        public void run() {
            while (true) {
                int retCount = 0;
                retCount = dataSaveObj(1000);
                retCount = dataSaveObjEnergy(1000);
                if (retCount == 0) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        // TODO Auto-generated catch block
                        e.printStackTrace();
                    }
                }
            }
        }
    }
}
