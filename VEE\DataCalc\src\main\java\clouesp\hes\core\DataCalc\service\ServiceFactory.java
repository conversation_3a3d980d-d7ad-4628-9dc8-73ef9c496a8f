package clouesp.hes.core.DataCalc.service;

import org.springframework.stereotype.Service;

import clouesp.hes.core.DataCalc.service.Handler.HandlerService;

@Service("serviceFactory")
public class ServiceFactory {
	public BaseService getService(String serviceType) {
		BaseService baseService = null;
		
		switch(serviceType) {
		case "HANDLER":
			baseService = new HandlerService();
			break;	
		}
		return baseService;
	}
}
