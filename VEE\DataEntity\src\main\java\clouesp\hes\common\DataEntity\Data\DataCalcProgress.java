package clouesp.hes.common.DataEntity.Data;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name= "mdm_data_calc_progress")
@IdClass(DataCalcProgressPK.class)
public class DataCalcProgress {
    @Id
    @Column(name = "sdp_id", nullable = false, columnDefinition = "varchar(32)")
    private String sdpId;

    @Id
    @Column(name = "scheme_id", columnDefinition = "varchar(32)")
    private String schemeId;

    @Id
    @Column(name = "load_type", columnDefinition = "varchar(32)")
    private String loadType;

    @Column(name = "tv")
    private Date tv;

    @Column(name = "update_tv")
    private Date updateTv;

    public String getSdpId() {
        return sdpId;
    }

    public void setSdpId(String sdpId) {
        this.sdpId = sdpId;
    }

    public String getSchemeId() {
        return schemeId;
    }

    public void setSchemeId(String schemeId) {
        this.schemeId = schemeId;
    }

    public String getLoadType() {
        return loadType;
    }

    public void setLoadType(String loadType) {
        this.loadType = loadType;
    }

    public Date getTv() {
        return tv;
    }

    public void setTv(Date tv) {
        this.tv = tv;
    }

    public Date getUpdateTv() {
        return updateTv;
    }

    public void setUpdateTv(Date updateTv) {
        this.updateTv = updateTv;
    }
}
