package clouesp.hes.common.DataEntity.Data;

import java.io.Serializable;

import javax.persistence.Column;

public class MdmDataUpdateLogPK implements Serializable{
	private static final long serialVersionUID = 9169033457034125589L;
	@Column(name = "sdp_id", nullable = false, columnDefinition = "varchar(32)")
	private String sdpId;
	@Column(name = "data_type")
	private Integer dataType;
	@Column(name = "scheme_type")
	private Integer schemeType;
	
	public String getSdpId() {
		return sdpId;
	}

	public void setSdpId(String sdpId) {
		this.sdpId = sdpId;
	}

	public Integer getDataType() {
		return dataType;
	}

	public void setDataType(Integer dataType) {
		this.dataType = dataType;
	}

	public Integer getSchemeType() {
		return schemeType;
	}

	public void setSchemeType(Integer schemeType) {
		this.schemeType = schemeType;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((sdpId == null) ? 0 : sdpId.hashCode());
		result = prime * result
				+ ((dataType == null) ? 0 : dataType.hashCode());
		result = prime * result
				+ ((schemeType == null) ? 0 : schemeType.hashCode());
		return result;
	}
	
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		MdmDataUpdateLogPK other = (MdmDataUpdateLogPK) obj;
		if (sdpId == null) {
			if (other.sdpId != null)
				return false;
		} else if (!sdpId.equals(other.sdpId))
			return false;
		if (dataType == null) {
			if (other.dataType != null)
				return false;
		} else if (!dataType.equals(other.dataType))
			return false;
		if (schemeType == null) {
			if (other.schemeType != null)
				return false;
		} else if (!schemeType.equals(other.schemeType))
			return false;		
		return true;
	}					
}
