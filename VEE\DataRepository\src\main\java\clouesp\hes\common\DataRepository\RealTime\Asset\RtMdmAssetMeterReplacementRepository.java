package clouesp.hes.common.DataRepository.RealTime.Asset;

import clouesp.hes.common.DataEntity.Asset.MdmAssetMeterReplacement;
import clouesp.hes.common.DataEntity.Asset.MdmAssetMeterReplacementPK;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface RtMdmAssetMeterReplacementRepository extends JpaRepository<MdmAssetMeterReplacement, MdmAssetMeterReplacementPK> {

    @Query(value = "select * from mdm_asset_meter_replacement "
            + "where sdp_id = :sdpId "
            + "and tv >= :startTv "
            + "and tv < :endTv "
            + "and operation_type in (:operationTypeList) "
            + "and processing_type in (:processingTypeList) "
            + "order by tv desc",
            nativeQuery = true)
    List<MdmAssetMeterReplacement> getMeterReplacement(
            @Param("sdpId") String sdpId,
            @Param("startTv") String startTv,
            @Param("endTv") String endTv,
            @Param("operationTypeList") List<Integer> operationTypeList,
            @Param("processingTypeList") List<Integer> processingTypeList);
}
