package clouesp.hes.core.DataCalc.service.Data;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.List;

import javax.annotation.Resource;

import clouesp.hes.common.CommonUtils.StringUtils;
import clouesp.hes.common.DataEntity.Data.DataEnergyPK;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import clouesp.hes.common.DataEntity.Data.DataEnergy;

@Slf4j
@Service("rtDataAccessService")
public class RtDataAccessService {

	@Resource(name = "realtimeJdbcTemplate")
	private JdbcTemplate jdbcTemplate;

	private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	private boolean putInStorage(String sql, Object[][] params) {
		Connection con = null;
		PreparedStatement pst = null;

		try {
			con = jdbcTemplate.getDataSource().getConnection();
			pst = con.prepareStatement(sql);
			int col = 0;
			for (Object[] param : params) {
				col = 0;
				for (Object obj : param) {

					pst.setObject(col + 1, obj);
					col++ ;
				}
				pst.addBatch();
			}
			con.setAutoCommit(false);
			pst.executeBatch();
			con.commit();
		}
		catch (Exception e) {
			if(e.getMessage() != null
					&& !e.getMessage().startsWith("Unique index")) {
				log.info(sql);
				log.error("DataCalc Error : " , e);
				e.printStackTrace();
			}
			return false;
		} finally {
			try {
				if (pst != null) {
					pst.close();
				}
			} catch (Exception e) {

				return false;
			}
			try {
				if (con != null) {
					con.close();
				}
			} catch (Exception e) {

				return false;
			}
		}

		return true;
	}

	public boolean batchSave(String tableName, List<DataEnergy> dataEnergys) throws Exception{

		int rowCount = 	dataEnergys.size();
		if(rowCount == 0) {
			return true;
		}

		Class clsPK = DataEnergyPK.class;
		Field[] fieldPKs = clsPK.getDeclaredFields();

		Class cls = DataEnergy.class;
		Field[] fields = cls.getDeclaredFields();

		int columnCount = fieldPKs.length + fields.length - 2;

		StringBuffer sql = new StringBuffer();
		StringBuffer val = new StringBuffer();

		sql.append("insert into ");
		sql.append(tableName);
		sql.append("( ");

		val.append("values (");

		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			String name = field.getName();
			name = StringUtils.humpToLine(name);
			sql.append(name);
			sql.append(", ");

			val.append("?");
			val.append(", ");
		}
		int index = 0;
		for (Field field : fields) {
			String name = field.getName();
			if("dataEnergyPK".equalsIgnoreCase(name)) {
				continue;
			}
			if(!name.startsWith("R")) {
				name = StringUtils.humpToLine(name);
			} else {
				name = name.toLowerCase();
			}

			sql.append(name);
			val.append("?");
			if(index < fields.length - 2) {
				sql.append(", ");
				val.append(", ");
			}
			index++;
		}
		sql.append(") ");
		val.append(") ");

		sql.append(val);

		int row = 0;
		int col = 0;
		Object[][] params = new Object[rowCount][columnCount];
		for (DataEnergy dataEnergy : dataEnergys) {
			col = 0;
			for (Field field : fieldPKs) {
				if(Modifier.isFinal(field.getModifiers())) {
					continue;
				}
				field.setAccessible(true);
				Object obj = field.get(dataEnergy.getDataEnergyPK());
				if(obj != null && "java.util.Date".equals(field.getType().getCanonicalName())){
					obj = Timestamp.valueOf(sdf.format(obj));
				}

				params[row][col++] = obj;
			}
			for (Field field : fields) {
				String name = field.getName();
				if("dataEnergyPK".equalsIgnoreCase(name)) {
					continue;
				}
				field.setAccessible(true);
				Object obj = field.get(dataEnergy);

				if(obj != null && "java.util.Date".equals(field.getType().getCanonicalName())){
					obj = Timestamp.valueOf(sdf.format(obj));
				}

				params[row][col++] = obj;
			}
			row++ ;
		}
		String strSql = sql.toString();
		return putInStorage(strSql, params);
	}

	public boolean batchUpdateSave(String tableName, List<DataEnergy> dataEnergys) throws Exception{
		int rowCount = 	dataEnergys.size();
		if(rowCount == 0) {
			return true;
		}
		Class clsPK = DataEnergyPK.class;
		Field[] fieldPKs = clsPK.getDeclaredFields();

		Class cls = DataEnergy.class;
		Field[] fields = cls.getDeclaredFields();

		int columnCount = fieldPKs.length + fields.length - 2;

		StringBuffer sql = new StringBuffer();

		sql.append("replace into ");
		sql.append(tableName);

		sql.append(" (");
		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			String name = field.getName();
			name = StringUtils.humpToLine(name);
			sql.append(name);
			sql.append(", ");
		}
		int index = 0;
		for (Field field : fields) {
			String name = field.getName();

			if("dataEnergyPK".equalsIgnoreCase(name)) {
				continue;
			}
			if(!name.startsWith("R")) {
				name = StringUtils.humpToLine(name);
			} else {
				name = name.toLowerCase();
			}
			sql.append(name);
			if(index < fields.length - 2) {
				sql.append(", ");

			}
			index++;
		}
		sql.append(") values (");

		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			sql.append("?");
			sql.append(", ");
		}
		index = 0;
		for (Field field : fields) {
			String name = field.getName();
			if("dataEnergyPK".equalsIgnoreCase(name)) {
				continue;
			}
			sql.append("?");
			if(index < fields.length - 2) {
				sql.append(", ");
			}
			else {
				sql.append(")");
			}

			index++;
		}

		int row = 0;
		int col = 0;
		Object[][] params = new Object[rowCount][columnCount];
		for (DataEnergy dataEnergy : dataEnergys) {
			col = 0;

			for (Field field : fieldPKs) {
				if (Modifier.isFinal(field.getModifiers())) {
					continue;
				}
				field.setAccessible(true);
				Object obj = field.get(dataEnergy.getDataEnergyPK());
				if (obj != null && "java.util.Date".equals(field.getType().getCanonicalName())) {
					obj = Timestamp.valueOf(sdf.format(obj));
				}
				params[row][col++] = obj;
			}

			for (Field field : fields) {
				String name = field.getName();
				if ("dataEnergyPK".equalsIgnoreCase(name)) {
					continue;
				}
				field.setAccessible(true);
				Object obj = field.get(dataEnergy);
				if (obj != null && "java.util.Date".equals(field.getType().getCanonicalName())) {
					obj = Timestamp.valueOf(sdf.format(obj));
				}
				params[row][col++] = obj;
			}

			row++;
		}

		String strSql = sql.toString();
		return putInStorage(strSql, params);
	}

}
