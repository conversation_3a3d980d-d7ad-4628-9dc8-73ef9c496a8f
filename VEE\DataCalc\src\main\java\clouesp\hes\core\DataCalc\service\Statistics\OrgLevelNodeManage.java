package clouesp.hes.core.DataCalc.service.Statistics;


import jline.internal.Log;

import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class OrgLevelNodeManage {

	private Map<String, OrgLevelNode> mapNodeList = new ConcurrentHashMap<String, OrgLevelNode>();
	private Map<String, OrgLevelNode> mapRootSysOrgStatistics = new ConcurrentHashMap<String, OrgLevelNode>();

	public Map<String, OrgLevelNode> getMapNodeList() {
		return mapNodeList;
	}

	public OrgNodeInfo NewOrgNodeInfo() {
		return new OrgNodeInfo();
	}

	public class OrgNodeInfo {
		private String orgId;
		private String parentOrgId;
		private int dbStatisticsCount = 0; // 数据库中的统计中
		private int CalcStatisticsCount = 0; // 根据层次关系计算的值 ，如果是叶子节点就是数据库中的值

		public String getOrgId() {
			return orgId;
		}

		public void setOrgId(String orgId) {
			this.orgId = orgId;
		}

		public String getParentOrgId() {
			return parentOrgId;
		}

		public void setParentOrgId(String parentOrgId) {
			this.parentOrgId = parentOrgId;
		}

		public int getDbStatisticsCount() {
			return dbStatisticsCount;
		}

		public void setDbStatisticsCount(int dbStatisticsCount) {
			this.dbStatisticsCount = dbStatisticsCount;
		}

		public int getCalcStatisticsCount() {
			return CalcStatisticsCount;
		}

		public void setCalcStatisticsCount(int calcStatisticsCount) {
			CalcStatisticsCount = calcStatisticsCount;
		}
	}


	public class OrgLevelNode {
		private OrgNodeInfo orgNodeInfo = new OrgNodeInfo();
		private Map<String, OrgLevelNode> mapSubSysOrgStatistics = new ConcurrentHashMap<String, OrgLevelNode>();

		public OrgLevelNode findNodeByOrgId(String orgId) {
			if (orgNodeInfo == null || orgNodeInfo.orgId == null)
				return null;
			if (orgNodeInfo.orgId.equals(orgId))
				return this;
			for (Map.Entry<String, OrgLevelNode> entry : mapSubSysOrgStatistics.entrySet()) {
				OrgLevelNode retNode = entry.getValue().findNodeByOrgId(orgId);
				if (retNode != null)
					return retNode;
			}
			return null;
		}

		public void Statistics() {

			int count = 0;
			if (mapSubSysOrgStatistics.isEmpty()) {
				orgNodeInfo.setCalcStatisticsCount(orgNodeInfo.getDbStatisticsCount());
			} else {
				for (Map.Entry<String, OrgLevelNode> entry : mapSubSysOrgStatistics.entrySet()) {
					OrgLevelNode orgLevelNode = entry.getValue();
					orgLevelNode.Statistics();
					count += orgLevelNode.getOrgNodeInfo().getCalcStatisticsCount();
				}
				orgNodeInfo.setCalcStatisticsCount(count + orgNodeInfo.getDbStatisticsCount());

			}
		}

		public OrgLevelNode AddSubNode(OrgNodeInfo addOrgNodeInfo) {

			if (addOrgNodeInfo.getParentOrgId() == null || !addOrgNodeInfo.getParentOrgId().equals(orgNodeInfo.orgId)) {
				Log.info("Node OrgId( " + orgNodeInfo.orgId + " ) != addOrgParentId( "
						+ addOrgNodeInfo.getParentOrgId() + " ) in AddSubNode(...)");
				return null;
			}
			if (mapSubSysOrgStatistics.containsKey(addOrgNodeInfo.getOrgId())) {
				//Log.info(OrgId( " + addOrgNodeInfo.getOrgId() + " ) already exist in AddSubNode(...)");
				return null;
			}

			OrgLevelNode orgLevelNode = new OrgLevelNode();
			orgLevelNode.getOrgNodeInfo().setOrgId(addOrgNodeInfo.getOrgId());
			orgLevelNode.getOrgNodeInfo().setParentOrgId(addOrgNodeInfo.getParentOrgId());
			orgLevelNode.getOrgNodeInfo().setDbStatisticsCount(addOrgNodeInfo.getDbStatisticsCount());

			mapSubSysOrgStatistics.put(addOrgNodeInfo.getOrgId(), orgLevelNode);
			return orgLevelNode;
		}

		public OrgNodeInfo getOrgNodeInfo() {
			return orgNodeInfo;
		}

		public void setOrgNodeInfo(OrgNodeInfo orgNodeInfo) {
			this.orgNodeInfo = orgNodeInfo;
		}

		public Map<String, OrgLevelNode> getMapSubSysOrgStatistics() {
			return mapSubSysOrgStatistics;
		}

		public void setMapSubSysOrgStatistics(Map<String, OrgLevelNode> mapSubSysOrgStatistics) {
			this.mapSubSysOrgStatistics = mapSubSysOrgStatistics;
		}

	}

	public OrgLevelNodeManage() {

	}

	OrgLevelNode FindNodeByOrgId(String orgId) {

		OrgLevelNode orgLevelNode = null;

		for (Map.Entry<String, OrgLevelNode> entry : mapRootSysOrgStatistics.entrySet()) {

			if (entry.getValue() == null)
				continue;
			orgLevelNode = entry.getValue().findNodeByOrgId(orgId);
			if (orgLevelNode != null)
				return orgLevelNode;
		}
		return null;

	}

	private void ReorganizeNode(OrgLevelNode curOrgNode) {
		// 向下整理

		Iterator<Map.Entry<String, OrgLevelNode>> it = mapRootSysOrgStatistics.entrySet().iterator();
		while (it.hasNext()) {
			Map.Entry<String, OrgLevelNode> entry = it.next();
			if (entry.getValue() == null)
				continue;
			OrgLevelNode tmpNode = entry.getValue();
			if (tmpNode.getOrgNodeInfo().getParentOrgId() != null
					&& tmpNode.getOrgNodeInfo().getParentOrgId().equals(curOrgNode.getOrgNodeInfo().orgId)) {
				if (curOrgNode.getMapSubSysOrgStatistics().containsKey(tmpNode.getOrgNodeInfo().getOrgId())) {
					Log.info("OrgId( " + tmpNode.getOrgNodeInfo().getOrgId()
							+ " ) already exist in ReorganizeNode(...)");
				} else {
					curOrgNode.getMapSubSysOrgStatistics().put(tmpNode.getOrgNodeInfo().getOrgId(), tmpNode);
				}
				it.remove();
			}
		}

		// 向上整理
		do {
			OrgLevelNode parentNode = FindNodeByOrgId(curOrgNode.getOrgNodeInfo().getParentOrgId());
			if (parentNode != null && mapRootSysOrgStatistics.containsKey(curOrgNode.getOrgNodeInfo().getOrgId())) {
				parentNode.getMapSubSysOrgStatistics().put(curOrgNode.getOrgNodeInfo().getOrgId(), curOrgNode);
				mapRootSysOrgStatistics.remove(curOrgNode.getOrgNodeInfo().getOrgId());
			}

			curOrgNode = FindNodeByOrgId(curOrgNode.getOrgNodeInfo().getParentOrgId());
		} while (curOrgNode != null);

	}

	public void StatisticsAllNode() {
		for (Map.Entry<String, OrgLevelNode> entry : mapRootSysOrgStatistics.entrySet()) {
			if (entry.getValue() == null)
				continue;
			entry.getValue().Statistics();
		}
	}

	public void AddNode(OrgNodeInfo addOrgNodeInfo) {

		if (FindNodeByOrgId(addOrgNodeInfo.getOrgId()) != null) {
			//Log.info(OrgId( " + addOrgNodeInfo.getOrgId() + " ) already exist in AddNode(...)");
			return;
		}

		OrgLevelNode parentNode = FindNodeByOrgId(addOrgNodeInfo.getParentOrgId());
		if (parentNode != null) {
			OrgLevelNode addNode = parentNode.AddSubNode(addOrgNodeInfo);
			if (addNode != null) {
				if (!mapNodeList.containsKey(addNode.getOrgNodeInfo().getOrgId())) {
					mapNodeList.put(addNode.getOrgNodeInfo().getOrgId(), addNode);
				}
				ReorganizeNode(addNode);
			}
		} else {

			OrgLevelNode orgLevelNode = new OrgLevelNode();
			orgLevelNode.getOrgNodeInfo().setOrgId(addOrgNodeInfo.getOrgId());
			orgLevelNode.getOrgNodeInfo().setParentOrgId(addOrgNodeInfo.getParentOrgId());
			orgLevelNode.getOrgNodeInfo().setDbStatisticsCount(addOrgNodeInfo.getDbStatisticsCount());

			mapRootSysOrgStatistics.put(addOrgNodeInfo.getOrgId(), orgLevelNode);
			if (!mapNodeList.containsKey(orgLevelNode.getOrgNodeInfo().getOrgId())) {
				mapNodeList.put(orgLevelNode.getOrgNodeInfo().getOrgId(), orgLevelNode);
			}
			ReorganizeNode(orgLevelNode);
		}
	}

}
