/*
 * �ļ���Logger.java
 * ��Ȩ��Copyright by Power7000 Team
 * ������
 * �޸��ˣ�linan
 * �޸�ʱ�䣺2017��10��19��
 * ���ٵ��ţ�
 * �޸ĵ��ţ�
 * �޸����ݣ�
 */

package clouesp.hes.common.logger.logger;

public class Logger {

    private LoggerAppenderType loggerAppenderType = LoggerAppenderType.Lucene;
    private String indexPath;
    
    ILoggerAppender appender = null;
    
    private boolean exit = false;
    private Thread persistenceThread;
    private Persistence persistence;    
    
    private static Logger instance = null;
   

    private Logger() {
        // System.setProperty("Log4jContextSelector",
        // "org.apache.logging.log4j.core.async.AsyncLoggerContextSelector");
    }

    public static Logger getInstance() {
        if (instance == null) instance = new Logger();
        return instance;
    }

    public void setLevel(LoggerLevel level) {
        LoggerAppenderFactory.getInstance().setLevel(level);
    }

    public void setAppenderType(LoggerAppenderType type) {
        loggerAppenderType = type;
    }

    public void start() {
        exit = false;       
      
        LoggerAppenderFactory.getInstance().start();
        appender = LoggerAppenderFactory.getInstance().getAppender(
            loggerAppenderType);   
        
        persistence = new Persistence();
        persistenceThread = new Thread(persistence);
        persistenceThread.start();       
    }
    
    public void join(){
        if(persistenceThread != null) try {
            persistenceThread.join();
        }
        catch (InterruptedException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }   

    public void stop() {
        if(appender != null)
            appender.directAppendLogger();        
        
        LoggerAppenderFactory.getInstance().stop();
        exit = true;
    }
    
    public String getIndexPath(){
        return this.indexPath;
    }
    
    public void setIndexPath(String indexPath){
        this.indexPath = indexPath;
    }   

    public void writeLogInfo(LoggerLevel level, String serviceId, String deviceId,
                             String infoType, String info) {
        if (appender != null) appender.writeLogInfo(level, serviceId, deviceId, infoType, info);
    }
    
    private class Persistence implements Runnable {
        private void putPersistence(){
            if(appender != null)
                appender.appendLogger();
        }
        
        @Override
        public void run() {
            while (!exit)
                try {
                    putPersistence();
                    Thread.sleep(1000);
                }
                catch (InterruptedException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
        }
    }   
}
