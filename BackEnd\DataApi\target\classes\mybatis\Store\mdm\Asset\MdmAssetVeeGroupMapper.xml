<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="MdmAssetVeeGroupMapper">
    <select id="getVeeGroupList"  resultType="MdmAssetVeeGroup" parameterType="PagingRequest" >
        select
            *
        from
            mdm_asset_vee_group
        <where>
            1=1
            <if test="entity">
                <if test="entity.name != null and  entity.name != ''">
                    and name like concat(concat('%', #{entity.name}),'%')
                </if>
                <if test="entity.type != null">
                    and type =  #{entity.type}
                </if>
                <if test="entity.objectType != null">
                    and object_type =  #{entity.objectType}
                </if>
            </if>
        </where>
        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>
    </select>

</mapper>