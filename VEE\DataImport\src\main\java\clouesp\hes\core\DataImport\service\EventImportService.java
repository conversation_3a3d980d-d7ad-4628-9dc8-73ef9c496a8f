package clouesp.hes.core.DataImport.service;

import clouesp.hes.common.DataEntity.Asset.MdmAssetHes;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetHesRepository;
import clouesp.hes.common.logger.logger.Logger;
import clouesp.hes.common.logger.logger.LoggerLevel;
import clouesp.hes.core.DataImport.config.ServerConfig;
import clouesp.hes.core.DataImport.handler.EventFileHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileFilter;
import java.text.SimpleDateFormat;
import java.util.*;
@Slf4j
@Service("eventImportService")
public class EventImportService {
	@Value("${ftp.localDir}")
	private String localDir;
	
	@Value("${ftp.manualDir}")
	private String manualDir;
	private boolean executing = false;	

	@Autowired
	private RtMdmAssetHesRepository rtMdmAssetHesRepository;
	
	@Autowired
	private EventFileHandler eventFileHandler;
	
	@Autowired
    private ServerConfig serverConfig;	
	
	private SimpleDateFormat sdflog = new SimpleDateFormat("MM-dd HH:mm:ss");

    private List<String> getFilePaths(String path, int index){
        File file = new File(path);
        FileFilter ff = new FileFilter() {
            public boolean accept(File file) {
            	if (!file.getName().startsWith("EVENT_"))
            		return false;
                return file.getName().endsWith(".csv");
            }
        };
        File[] tempList = file.listFiles(ff);
        
		Arrays.sort(tempList, new Comparator<File>() {
			public int compare(File f1, File f2) {

				long diff = f1.lastModified() - f2.lastModified();
				if (diff > 0)
					return 1;
				else if (diff == 0)
					return 0;
				else
					return -1;

			}

			public boolean equals(Object obj) {
				return true;
			}

		});
       
        List<String> names = new ArrayList<String>();
        
        for (int i = 0; i < tempList.length; i++){
            if (tempList[i].isFile()){
                names.add(tempList[i].getPath());
            }
        }
        
        return names;
    }  
    
	public void execute() {		
		if (executing) {
			return;
		}

		try {
			executing = true;
			String logInfo = "Start import event";
			log.info(logInfo);
			Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "System", logInfo);

			List<String> localFilePaths = getFilePaths(localDir, 0);
			for (String filePath : localFilePaths) {
				File srcFile = new File(filePath);
				if (!srcFile.exists()) {
					continue;
				}
				if (System.currentTimeMillis() - srcFile.lastModified() < 1000 * 300) {
					continue;
				}
				eventFileHandler.handleCsvFiles(filePath);
			}

			localFilePaths = getFilePaths(manualDir, 1);
			for (String filePath : localFilePaths) {
				File srcFile = new File(filePath);
				if (!srcFile.exists()) {
					continue;
				}
				if (System.currentTimeMillis() - srcFile.lastModified() < 1000 * 600) {
					continue;
				}

				eventFileHandler.handleCsvFiles(filePath);
			}

			//check all assetHes
			List<String> localDirList = new ArrayList<>();		
			List<MdmAssetHes> mdmAssetHess = rtMdmAssetHesRepository.findAll();
			for (MdmAssetHes mdmAssetHes : mdmAssetHess) {
				localDirList.add(mdmAssetHes.getLocalFileDir());
				Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "System", "Event Import: HesID = " + mdmAssetHes.getHesId() + " , Data import Dir = " + mdmAssetHes.getLocalFileDir());
			}
			for (String dirPath : localDirList) {
				List<String> localFilePathTmps = getFilePaths(dirPath, 0);
				for (String filePath : localFilePathTmps) {
					File srcFile = new File(filePath);
					if (!srcFile.exists()) {
						continue;
					}
					if (System.currentTimeMillis() - srcFile.lastModified() < 1000 * 300) {
						continue;
					}
					eventFileHandler.handleCsvFiles(filePath);
				}
			}
			executing = false;

			logInfo = "End import event";
			log.info(logInfo);
			Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "System", logInfo);
		}catch (Exception e){
			log.error("DataImport Error : " , e);
		}finally {
			executing = false;
		}
	} 
	

	
}
