package clouesp.hes.common.DataEntity.System;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name="SYS_ORG")
@Data
public class SysOrg  {
    @Id
    @Column(name = "ID", nullable = false,columnDefinition = "varchar(32)")
     private String id;
    @Column(name = "NAME",columnDefinition = "varchar(128)")
    private  String name ;
    @Column(name = "PARENT_ORG_TID",columnDefinition = "varchar(32)")
    private String parentOrgTid ;

}
