package clouesp.hes.common.DataRepository.Persistence.Statistics;

import clouesp.hes.common.DataEntity.Statistics.ObjectStatistics;
import clouesp.hes.common.DataEntity.Statistics.ObjectStatisticsPK;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface ObjectStatisticsRepository  extends JpaRepository<ObjectStatistics, ObjectStatisticsPK> {
  //    @Query(value ="select dict_id as Object_id , org_id , count(*) as Object_count from (select b.org_id , c.dict_id , a.* from mdm_data_VEE_EVENT a, MDM_ASSET_SERVICE_POINT b ,MDM_DICT_DETAIL c  where a.data_type = 109 and a.object_id = b.id   and a.object_type = 7 and c.inner_value = a.event_id  and   a.tv >= trunc(sysdate) -7  and a.tv < trunc(sysdate) + 1 )  group by dict_id,org_id",nativeQuery = true)
    @Query(value ="select dict_id as Object_id , org_id , count(*) as Object_count from (select b.org_id , c.dict_id , a.* from mdm_data_VEE_EVENT a, MDM_ASSET_SERVICE_POINT b ,MDM_DICT_DETAIL c  where a.data_type = 109 and a.object_id = b.id   and a.object_type = 7 and c.inner_value = a.event_id  and   a.tv >= trunc(sysdate) -1  and a.tv < trunc(sysdate)   )  group by dict_id,org_id",nativeQuery = true)
      List<ObjectStatistics> findOracleYesterdayDeviceEventCount();

    @Query(value ="select dict_id as Object_id , org_id , count(*) as Object_count from (select b.org_id , c.dict_id , a.* from mdm_data_VEE_EVENT a, MDM_ASSET_SERVICE_POINT b ,MDM_DICT_DETAIL c  where a.data_type = 109 and a.object_id = b.id   and a.object_type = 7 and c.inner_value = a.event_id  and   a.tv >=  DATE(NOW() - INTERVAL 1 DAY)   and a.tv < DATE(NOW())   ) c group by dict_id,org_id",nativeQuery = true)
    List<ObjectStatistics> findMysqlYesterdayDeviceEventCount();

 //     @Query(value ="select  inner_value as Object_id , org_id , count(*) as Object_count from (select b.org_id , c.inner_value , a.* from mdm_data_VEE_EVENT a, MDM_ASSET_SERVICE_POINT b ,MDM_DICT_DETAIL c  where a.data_type <> 109 and a.object_id = b.id   and a.object_type = 7 and c.inner_value = a.event_id  and   a.tv >= trunc(sysdate) -7  and a.tv < trunc(sysdate) + 1 )  group by inner_value,org_id",nativeQuery = true)
      @Query(value ="select inner_value as Object_id , org_id , count(*) as Object_count from (select b.org_id , c.inner_value , a.* from mdm_data_VEE_EVENT a, MDM_ASSET_SERVICE_POINT b ,MDM_DICT_DETAIL c  where a.data_type <> 109 and a.object_id = b.id   and a.object_type = 7 and c.inner_value = a.event_id  and   a.tv >= trunc(sysdate) -1  and a.tv < trunc(sysdate)   )  group by inner_value,org_id",nativeQuery = true)
      List<ObjectStatistics> findOracleYesterdayVeeEventCount();

    @Query(value ="select inner_value as Object_id , org_id , count(*) as Object_count from (select b.org_id , c.inner_value , a.* from mdm_data_VEE_EVENT a, MDM_ASSET_SERVICE_POINT b ,MDM_DICT_DETAIL c  where a.data_type <> 109 and a.object_id = b.id   and a.object_type = 7 and c.inner_value = a.event_id  and   a.tv >=  DATE(NOW() - INTERVAL 1 DAY)   and a.tv < DATE(NOW())  ) c group by inner_value,org_id",nativeQuery = true)
    List<ObjectStatistics> findMysqlYesterdayVeeEventCount();

 //     @Query(value =" select data_source as Object_id , org_id , count(*) as Object_count from (select b.org_id ,  a.* from mdm_data_reg_dayly a, MDM_ASSET_SERVICE_POINT b  where  a.sdp_id = b.id     and data_source is not null and   a.tv >= trunc(sysdate) -7  and a.tv < trunc(sysdate) + 1 )  group by data_source,org_id",nativeQuery = true)
     @Query(value ="select data_source as Object_id , org_id , count(*) as Object_count from (select b.org_id ,  a.* from mdm_data_reg_dayly a, MDM_ASSET_SERVICE_POINT b  where  a.sdp_id = b.id and data_source is not null and   a.tv >= trunc(sysdate) -1  and a.tv < trunc(sysdate) )  group by data_source,org_id",nativeQuery = true)
      List<ObjectStatistics> findOracleYesterdayVeeDataCount();

    @Query(value ="select data_source as Object_id , org_id , count(*) as Object_count from (select b.org_id ,  a.* from mdm_data_reg_dayly a, MDM_ASSET_SERVICE_POINT b  where  a.sdp_id = b.id and data_source is not null and   a.tv >=  DATE(NOW() - INTERVAL 1 DAY)   and a.tv < DATE(NOW()) ) c group by data_source,org_id",nativeQuery = true)
    List<ObjectStatistics> findMysqlYesterdayVeeDataCount();

 //     @Query(value =" select inner_value as Object_id , org_id , count(*) as Object_count from (select b.org_id , c.inner_value , a.* from mdm_data_VEE_EVENT a, MDM_ASSET_CALC_OBJ b ,MDM_DICT_DETAIL c  where  a.object_id = b.id   and c.dict_id  = '1010' and c.inner_value = a.event_id  and   a.tv >= trunc(sysdate) -7  and a.tv < trunc(sysdate) + 1 )  group by inner_value,org_id",nativeQuery = true)
      @Query(value ="select inner_value as Object_id , org_id , count(*) as Object_count from (select b.org_id , c.inner_value , a.* from mdm_data_VEE_EVENT a, MDM_ASSET_CALC_OBJ b ,MDM_DICT_DETAIL c  where  a.object_id = b.id   and c.dict_id  = '1010' and c.inner_value = a.event_id  and   a.tv >= trunc(sysdate) -1  and a.tv < trunc(sysdate)  )  group by inner_value,org_id",nativeQuery = true)
      List<ObjectStatistics> findOracleYesterdayLossDataCount();
    @Query(value ="select inner_value as Object_id , org_id , count(*) as Object_count from (select b.org_id , c.inner_value , a.* from mdm_data_VEE_EVENT a, MDM_ASSET_CALC_OBJ b ,MDM_DICT_DETAIL c  where  a.object_id = b.id   and c.dict_id  = '1010' and c.inner_value = a.event_id  and   a.tv >=  DATE(NOW() - INTERVAL 1 DAY)  and a.tv < DATE(NOW()) ) c group by inner_value,org_id",nativeQuery = true)
    List<ObjectStatistics> findMysqlYesterdayLossDataCount();


      @Query(value ="select inner_value as Object_id , org_id , count(*) as Object_count from (select b.org_id , c.inner_value , a.* from mdm_data_VEE_EVENT a, MDM_ASSET_CALC_OBJ b ,MDM_DICT_DETAIL c  where a.SCHEME_TYPE = 3  and a.object_id = b.id   and c.dict_id  = '1003' and c.inner_value = a.event_id  and   a.tv >=trunc(add_months(sysdate,-1),'mm') and a.tv < trunc(sysdate,'mm') )  group by inner_value,org_id",nativeQuery = true)
      List<ObjectStatistics> findOracleLastMonthsBillingCount();
    @Query(value ="select inner_value as Object_id , org_id , count(*) as Object_count from (select b.org_id , c.inner_value , a.* from mdm_data_VEE_EVENT a, MDM_ASSET_CALC_OBJ b ,MDM_DICT_DETAIL c  where a.SCHEME_TYPE = 3  and a.object_id = b.id   and c.dict_id  = '1003' and c.inner_value = a.event_id  and   a.tv >=DATE(date_add(curdate()-day(curdate())+1,interval -1 month)) and a.tv < DATE(date_add(curdate(), interval - day(curdate()) + 1 day)) ) c group by inner_value,org_id",nativeQuery = true)
    List<ObjectStatistics> findMysqlLastMonthsBillingCount();




}
