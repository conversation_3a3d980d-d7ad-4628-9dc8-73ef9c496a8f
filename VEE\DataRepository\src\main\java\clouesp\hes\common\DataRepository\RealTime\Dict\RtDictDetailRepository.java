package clouesp.hes.common.DataRepository.RealTime.Dict;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;

import clouesp.hes.common.DataEntity.Dict.DictDetail;
import clouesp.hes.common.DataEntity.Dict.DictDetailPK;

public interface RtDictDetailRepository extends JpaRepository<DictDetail, DictDetailPK>{
	List<DictDetail> findByDictId(String dictId);
	DictDetail findByDictIdAndInnerValue(String dictId, Integer innerValue);
}
