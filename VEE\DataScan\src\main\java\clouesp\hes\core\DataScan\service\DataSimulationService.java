package clouesp.hes.core.DataScan.service;

import clouesp.hes.common.DataEntity.Asset.MdmAssetServicePoint;
import clouesp.hes.common.DataEntity.Data.DataRegDaily;
import clouesp.hes.common.DataEntity.Data.DataRegMinutely;
import clouesp.hes.common.DataEntity.Data.DataRegMonthly;
import clouesp.hes.common.DataEntity.Data.DataRegPK;
import clouesp.hes.common.DataRepository.Persistence.Data.DataRegDailyRepository;
import clouesp.hes.common.DataRepository.Persistence.Data.DataRegMinutelyRepository;
import clouesp.hes.common.DataRepository.Persistence.Data.DataRegMonthlyRepository;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetServicePointRepository;
import clouesp.hes.common.DataRepository.RealTime.Data.RtDataRegDailyRepository;
import clouesp.hes.common.DataRepository.RealTime.Data.RtDataRegMinutelyRepository;
import clouesp.hes.common.DataRepository.RealTime.Data.RtDataRegMonthlyRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.*;

@Service("dataSimulationService")
public class DataSimulationService {
    @Autowired
    private RtMdmAssetServicePointRepository rtMdmAssetServicePointRepository;

    @Autowired
    private RtDataRegMinutelyRepository rtDataRegMinutelyRepository;

    @Autowired
    private DataRegMinutelyRepository dataRegMinutelyRepository;

    @Autowired
    private RtDataRegDailyRepository rtDataRegDailyRepository;

    @Autowired
    private DataRegDailyRepository dataRegDailyRepository;

    @Autowired
    private RtDataRegMonthlyRepository rtDataRegMonthlyRepository;

    @Autowired
    private DataRegMonthlyRepository dataRegMonthlyRepository;

//    @Scheduled(fixedDelay = 3600000)
    public void dispatchMinutely() {
        List<MdmAssetServicePoint> servicePointList = rtMdmAssetServicePointRepository.findAll();
        if(servicePointList.size() == 0) {
            return;
        }

        List<DataRegMinutely> dataList = new ArrayList<>();
        Date curTime = new Date();
        Random random = new Random();

        for(MdmAssetServicePoint servicePoint : servicePointList) {
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.DAY_OF_MONTH, -21);
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);

            String sdpId = servicePoint.getId();
            DataRegPK lastPk = new DataRegPK();
            lastPk.setSdpId(sdpId);
            lastPk.setTv(cal.getTime());

            DataRegMinutely lastData = dataRegMinutelyRepository.findById(lastPk).orElse(null);
            if(lastData == null) {
                lastData = new DataRegMinutely();
                lastData.setDataRegPK(lastPk);
                lastData.setTimeType("MINUTE");
                lastData.setUpdateTv(curTime);
                lastData.setR0P1A1(0d);
                lastData.setR0P3(0d);
                lastData.setR0P4(0d);
                lastData.setDataSource(0);
                lastData.setDataVersion(0);
                rtDataRegMinutelyRepository.save(lastData);
                dataRegMinutelyRepository.save(lastData);
            }

            cal.add(Calendar.MINUTE, 15);

            while(cal.getTime().getTime() < curTime.getTime()) {
                DataRegMinutely data = new DataRegMinutely();
                DataRegPK pk = data.getDataRegPK();
                pk.setSdpId(sdpId);
                pk.setTv(cal.getTime());
                data.setTimeType("MINUTE");
                data.setUpdateTv(curTime);
                double addValue = ((double) Math.round((random.nextDouble() * 50d) * 1000)) / 1000;
                data.setR0P1A1(lastData.getR0P1A1() + addValue);
                addValue = ((double)(-2 + random.nextInt(4))) / 10000;
                data.setR0P3(lastData.getR0P3() + addValue);
                if(data.getR0P3() < 0) {
                    data.setR0P3(0d);
                }
                data.setR0P4(lastData.getR0P4() + ((double)(-9 + random.nextInt(18))) / 10000);
                if(data.getR0P4() < 0) {
                    data.setR0P4(0d);
                }


                data.setDataSource(0);
                data.setDataVersion(0);
                dataList.add(data);
                cal.add(Calendar.MINUTE, 15);
                lastData = data;
            }
        }
        if(dataList.size() > 0) {
            rtDataRegMinutelyRepository.saveAll(dataList);
            dataRegMinutelyRepository.saveAll(dataList);
        }
    }

//    @Scheduled(fixedDelay = 3600000 * 24)
    public void dispatchDaily() {
        List<MdmAssetServicePoint> servicePointList = rtMdmAssetServicePointRepository.findAll();
        if(servicePointList.size() == 0) {
            return;
        }

        List<DataRegDaily> dataList = new ArrayList<>();
        Date curTime = new Date();
        Random random = new Random();

        for(MdmAssetServicePoint servicePoint : servicePointList) {
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.DAY_OF_MONTH, -21);
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);

            String sdpId = servicePoint.getId();
            DataRegPK lastPk = new DataRegPK();
            lastPk.setSdpId(sdpId);
            lastPk.setTv(cal.getTime());

            DataRegDaily lastData = dataRegDailyRepository.findById(lastPk).orElse(null);
            if(lastData == null) {
                lastData = new DataRegDaily();
                lastData.setDataRegPK(lastPk);
                lastData.setTimeType("DAY");
                lastData.setUpdateTv(curTime);

                lastData.setR0P1(0d);
                lastData.setR0P2(0d);
                lastData.setR0P3(0d);
                lastData.setR0P4(0d);

                lastData.setR0P1A1(0d);
                lastData.setR0P1A3(0d);

                lastData.setR1P1A1(0d);
                lastData.setR2P1A1(0d);
                lastData.setR3P1A1(0d);
                lastData.setR4P1A1(0d);

                lastData.setDataSource(0);
                lastData.setDataVersion(0);
                rtDataRegDailyRepository.save(lastData);
                dataRegDailyRepository.save(lastData);
            }

            cal.add(Calendar.DAY_OF_MONTH, 1);

            while(cal.getTime().getTime() < curTime.getTime()) {
                DataRegDaily data = new DataRegDaily();
                DataRegPK pk = data.getDataRegPK();
                pk.setSdpId(sdpId);
                pk.setTv(cal.getTime());
                data.setTimeType("DAY");
                data.setUpdateTv(curTime);

                double addValue = ((double) Math.round((random.nextDouble() * 50d) * 1000)) / 1000;
                data.setR0P1(lastData.getR0P1() + addValue);

                addValue = ((double) Math.round((random.nextDouble() * 50d) * 1000)) / 1000;
                data.setR0P2(lastData.getR0P2() + addValue);

                addValue = ((double) Math.round((random.nextDouble() * 50d) * 1000)) / 1000;
                data.setR0P3(lastData.getR0P3() + addValue);

                addValue = ((double) Math.round((random.nextDouble() * 50d) * 1000)) / 1000;
                data.setR0P4(lastData.getR0P4() + addValue);

                addValue = ((double) Math.round((random.nextDouble() * 50d) * 1000)) / 1000;
                data.setR0P1A1(lastData.getR0P1A1() + addValue);

                addValue = ((double) Math.round((random.nextDouble() * 50d) * 1000)) / 1000;
                data.setR0P1A3(lastData.getR0P1A3() + addValue);

                addValue = ((double) Math.round((random.nextDouble() * 50d) * 1000)) / 1000;
                data.setR1P1A1(lastData.getR1P1A1() + addValue);

                addValue = ((double) Math.round((random.nextDouble() * 50d) * 1000)) / 1000;
                data.setR2P1A1(lastData.getR2P1A1() + addValue);

                addValue = ((double) Math.round((random.nextDouble() * 50d) * 1000)) / 1000;
                data.setR3P1A1(lastData.getR3P1A1() + addValue);

                addValue = ((double) Math.round((random.nextDouble() * 50d) * 1000)) / 1000;
                data.setR4P1A1(lastData.getR4P1A1() + addValue);


                data.setDataSource(0);
                data.setDataVersion(0);
                dataList.add(data);
                cal.add(Calendar.DAY_OF_MONTH, 1);
                lastData = data;
            }
        }
        if(dataList.size() > 0) {
            rtDataRegDailyRepository.saveAll(dataList);
            dataRegDailyRepository.saveAll(dataList);
        }
    }

//    @Scheduled(fixedDelay = 3600000 * 24)
    public void dispatchMonthly() {
        List<MdmAssetServicePoint> servicePointList = rtMdmAssetServicePointRepository.findAll();
        if(servicePointList.size() == 0) {
            return;
        }

        List<DataRegMonthly> dataList = new ArrayList<>();
        Date curTime = new Date();
        Random random = new Random();

        for(MdmAssetServicePoint servicePoint : servicePointList) {
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.MONTH, -1);
            cal.set(Calendar.DAY_OF_MONTH, 1);
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);

            String sdpId = servicePoint.getId();
            DataRegPK lastPk = new DataRegPK();
            lastPk.setSdpId(sdpId);
            lastPk.setTv(cal.getTime());

            DataRegMonthly lastData = dataRegMonthlyRepository.findById(lastPk).orElse(null);
            if(lastData == null) {
                lastData = new DataRegMonthly();
                lastData.setDataRegPK(lastPk);
                lastData.setTimeType("MONTH");
                lastData.setUpdateTv(curTime);

                lastData.setR0P1(0d);
                lastData.setR0P2(0d);
                lastData.setR0P3(0d);
                lastData.setR0P4(0d);

                lastData.setR0P1A1(0d);
                lastData.setR0P1A3(0d);

                lastData.setR1P1A1(0d);
                lastData.setR2P1A1(0d);
                lastData.setR3P1A1(0d);
                lastData.setR4P1A1(0d);

                lastData.setDataSource(0);
                lastData.setDataVersion(0);
                rtDataRegMonthlyRepository.save(lastData);
                dataRegMonthlyRepository.save(lastData);
            }

            cal.add(Calendar.MONTH, 1);

            while(cal.getTime().getTime() < curTime.getTime()) {
                DataRegMonthly data = new DataRegMonthly();
                DataRegPK pk = data.getDataRegPK();
                pk.setSdpId(sdpId);
                pk.setTv(cal.getTime());
                data.setTimeType("DAY");
                data.setUpdateTv(curTime);

                double addValue = ((double) Math.round((random.nextDouble() * 50d) * 1000)) / 1000;
                data.setR0P1(lastData.getR0P1() + addValue);

                addValue = ((double) Math.round((random.nextDouble() * 50d) * 1000)) / 1000;
                data.setR0P2(lastData.getR0P2() + addValue);

                addValue = ((double) Math.round((random.nextDouble() * 50d) * 1000)) / 1000;
                data.setR0P3(lastData.getR0P3() + addValue);

                addValue = ((double) Math.round((random.nextDouble() * 50d) * 1000)) / 1000;
                data.setR0P4(lastData.getR0P4() + addValue);

                addValue = ((double) Math.round((random.nextDouble() * 50d) * 1000)) / 1000;
                data.setR0P1A1(lastData.getR0P1A1() + addValue);

                addValue = ((double) Math.round((random.nextDouble() * 50d) * 1000)) / 1000;
                data.setR0P1A3(lastData.getR0P1A3() + addValue);

                addValue = ((double) Math.round((random.nextDouble() * 50d) * 1000)) / 1000;
                data.setR1P1A1(lastData.getR1P1A1() + addValue);

                addValue = ((double) Math.round((random.nextDouble() * 50d) * 1000)) / 1000;
                data.setR2P1A1(lastData.getR2P1A1() + addValue);

                addValue = ((double) Math.round((random.nextDouble() * 50d) * 1000)) / 1000;
                data.setR3P1A1(lastData.getR3P1A1() + addValue);

                addValue = ((double) Math.round((random.nextDouble() * 50d) * 1000)) / 1000;
                data.setR4P1A1(lastData.getR4P1A1() + addValue);


                data.setDataSource(0);
                data.setDataVersion(0);
                dataList.add(data);
                cal.add(Calendar.MONTH, 1);
                lastData = data;
            }
        }
        if(dataList.size() > 0) {
            rtDataRegMonthlyRepository.saveAll(dataList);
            dataRegMonthlyRepository.saveAll(dataList);
        }
    }
}
