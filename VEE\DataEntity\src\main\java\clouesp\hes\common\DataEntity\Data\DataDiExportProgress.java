package clouesp.hes.common.DataEntity.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name= "mdm_data_di_export_progress")
public class DataDiExportProgress {
	@Id
	@Column(name = "dataitem_id", nullable = false, columnDefinition = "varchar(64)")
	private String dataitemId;
	
	@Column(name = "tv")
	private Date tv;
	
	@Column(name = "export_tv")
	private Date exportTv;
	
	@Column(name = "export_result", columnDefinition = "varchar(20)")
	private String exportResult;
	
	@Column(name = "export_reason", columnDefinition = "varchar(512)")
	private String exportReason;

	public String getDataitemId() {
		return dataitemId;
	}

	public void setDataitemId(String dataitemId) {
		this.dataitemId = dataitemId;
	}

	public Date getTv() {
		return tv;
	}

	public void setTv(Date tv) {
		this.tv = tv;
	}

	public Date getExportTv() {
		return exportTv;
	}

	public void setExportTv(Date exportTv) {
		this.exportTv = exportTv;
	}

	public String getExportResult() {
		return exportResult;
	}

	public void setExportResult(String exportResult) {
		this.exportResult = exportResult;
	}

	public String getExportReason() {
		return exportReason;
	}

	public void setExportReason(String exportReason) {
		this.exportReason = exportReason;
	}
	
}
