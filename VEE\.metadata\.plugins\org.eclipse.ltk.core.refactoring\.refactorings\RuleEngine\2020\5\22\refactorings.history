<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring comment="Rename resource &apos;RuleEngine/src/main/java/com&apos; to &apos;clouesp&apos;" description="Rename resource &apos;com&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/main/java/com" name="clouesp" stamp="1590646319549" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;RuleEngine/src/main/java/clouesp/photon&apos; to &apos;hes&apos;" description="Rename resource &apos;photon&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/main/java/clouesp/photon" name="hes" stamp="1590646330329" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;RuleEngine/src/main/java/clouesp/hes/common/ruleengine&apos; to &apos;RuleEngine&apos;" description="Rename resource &apos;ruleengine&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/main/java/clouesp/hes/common/ruleengine" name="RuleEngine" stamp="1590646346245" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;RuleEngine/src/test/java/com&apos; to &apos;clouesp&apos;" description="Rename resource &apos;com&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/java/com" name="clouesp" stamp="1590647116985" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;RuleEngine/src/test/java/clouesp/photon&apos; to &apos;hes&apos;" description="Rename resource &apos;photon&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/java/clouesp/photon" name="hes" stamp="1590647134088" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;RuleEngine/src/test/java/clouesp/hes/common/ruleengine&apos; to &apos;RuleEngine&apos;" description="Rename resource &apos;ruleengine&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/java/clouesp/hes/common/ruleengine" name="RuleEngine" stamp="1590647148477" updateReferences="true"/>
</session>