package clouesp.hes.core.DataImport.service.Data;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import clouesp.hes.common.CommonUtils.StringUtils;
import clouesp.hes.common.DataEntity.Data.*;
import clouesp.hes.core.DataImport.config.ServerConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import clouesp.hes.core.DataImport.dao.Persistence.DaoSupport;

@Slf4j
@Service("dataAccessService")
public class DataAccessService {
	@Resource(name = "daoSupport")
	private DaoSupport dao;

	@Resource(name = "persistenceJdbcTemplate")
	private JdbcTemplate jdbcTemplate;

	@Autowired
	private ServerConfig serverConfig;

	private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	private boolean putInStorage(String sql, Object[][] params) {
		Connection con = null;
		PreparedStatement pst = null;

		try {
			con = jdbcTemplate.getDataSource().getConnection();
			pst = con.prepareStatement(sql);
			int col = 0;
			for (Object[] param : params) {
				col = 0;
				for (Object obj : param) {

					pst.setObject(col + 1, obj);
					col++ ;
				}
				pst.addBatch();
			}
			con.setAutoCommit(false);
			pst.executeBatch();
			con.commit();
		}
		catch (Exception e) {
			if(e.getMessage() != null &&
					!e.getMessage().startsWith("ORA-00001") &&
					!e.getMessage().startsWith("Duplicate entry")) {
				log.info(sql);
				log.error("DataImport Error : " , e);
				e.printStackTrace();
			}
			return false;
		} finally {
			try {
				if (pst != null) {
					pst.close();
				}
			} catch (Exception e) {

				return false;
			}
			try {
				if (con != null) {
					con.close();
				}
			} catch (Exception e) {

				return false;
			}
		}

		return true;
	}

	public boolean batchSaveReg(String tableName, List<DataReg> dataRegs) throws Exception{

		int rowCount = 	dataRegs.size();
		if(rowCount == 0) {
			return true;
		}

		Class clsPK = DataRegPK.class;
		Field[] fieldPKs = clsPK.getDeclaredFields();

		Class cls = DataReg.class;
		Field[] fields = cls.getDeclaredFields();

		int columnCount = fieldPKs.length + fields.length - 3;

		StringBuffer sql = new StringBuffer();
		StringBuffer val = new StringBuffer();

		sql.append("insert into ");
		sql.append(tableName);
		sql.append("( ");

		val.append("values (");

		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			String name = field.getName();
			name = StringUtils.humpToLine(name);
			sql.append(name);
			sql.append(", ");

			val.append("?");
			val.append(", ");
		}
		int index = 0;
		for (Field field : fields) {
			String name = field.getName();
			if("dataRegPK".equalsIgnoreCase(name) || "timeType".equalsIgnoreCase(name)) {
				continue;
			}
			if(!name.startsWith("R")) {
				name = StringUtils.humpToLine(name);
			} else {
				name = name.toLowerCase();
			}

			sql.append(name);
			val.append("?");
			if(index < fields.length - 3) {
				sql.append(", ");
				val.append(", ");
			}
			index++;
		}
		sql.append(") ");
		val.append(") ");

		sql.append(val);

		int row = 0;
		int col = 0;
		Object[][] params = new Object[rowCount][columnCount];
		for (DataReg dataReg : dataRegs) {
			col = 0;
			for (Field field : fieldPKs) {
				if(Modifier.isFinal(field.getModifiers())) {
					continue;
				}
				field.setAccessible(true);
				Object obj = field.get(dataReg.getDataRegPK());
				if(obj != null && "java.util.Date".equals(field.getType().getCanonicalName())){
					obj = Timestamp.valueOf(sdf.format(obj));
				}


				params[row][col++] = obj;
			}
			for (Field field : fields) {
				String name = field.getName();
				if("dataRegPK".equalsIgnoreCase(name) || "timeType".equalsIgnoreCase(name)) {
					continue;
				}
				field.setAccessible(true);
				Object obj = field.get(dataReg);

				if(obj != null && "java.util.Date".equals(field.getType().getCanonicalName())){
					obj = Timestamp.valueOf(sdf.format(obj));
				}

				params[row][col++] = obj;
			}
			row++ ;
		}
		String strSql = sql.toString();
		return putInStorage(strSql, params);
	}

	public boolean batchUpdateSaveRegOracle(String tableName, List<DataReg> dataRegs) throws Exception{
		int rowCount = 	dataRegs.size();
		if(rowCount == 0) {
			return true;
		}
		Class clsPK = DataRegPK.class;
		Field[] fieldPKs = clsPK.getDeclaredFields();

		Class cls = DataReg.class;
		Field[] fields = cls.getDeclaredFields();

		int columnCount = fieldPKs.length + fields.length - 3;

		StringBuffer sql = new StringBuffer();
		StringBuffer notSql = new StringBuffer();
		StringBuffer notVal = new StringBuffer();

		sql.append("merge into ");
		sql.append(tableName + " T1 ");

		sql.append("using(select ");
		int index = 0;
		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			String name = field.getName();
			name = StringUtils.humpToLine(name);

			sql.append("? as ");
			sql.append(name);
			if(index < fieldPKs.length - 2) {
				sql.append(", ");
			}
			index++;
		}
		sql.append(" from dual) T2 ");

		sql.append("on (");

		index = 0;
		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			String name = field.getName();
			name = StringUtils.humpToLine(name);

			sql.append("T1.");
			sql.append(name);
			sql.append(" = ");
			sql.append("T2.");
			sql.append(name);
			if(index < fieldPKs.length - 2) {
				sql.append(" and ");
			}

			index++;
		}
		sql.append(") ");
		sql.append("when matched then update set ");


		notSql.append("when not matched then ");
		notSql.append("insert (");
		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			String name = field.getName();
			name = StringUtils.humpToLine(name);

			notSql.append(name);
			notSql.append(", ");
		}
		index = 0;
		for (Field field : fields) {
			String name = field.getName();

			if("dataRegPK".equalsIgnoreCase(name) || "timeType".equalsIgnoreCase(name)) {
				continue;
			}
			if(!name.startsWith("R")) {
				name = StringUtils.humpToLine(name);
			} else {
				name = name.toLowerCase();
			}

			sql.append(name);
			sql.append(" = ?");

			notSql.append(name);

			if(index < fields.length - 3) {
				sql.append(", ");
				notSql.append(", ");
			}
			index++;
		}
		sql.append(" ");
		notSql.append(") values (");

		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			notVal.append("?");
			notVal.append(", ");
		}
		index = 0;
		for (Field field : fields) {
			String name = field.getName();
			if("dataRegPK".equalsIgnoreCase(name) || "timeType".equalsIgnoreCase(name)) {
				continue;
			}
			notVal.append("?");
			if(index < fields.length - 3) {
				notVal.append(", ");
			}
			else {
				notVal.append(")");
			}

			index++;
		}
		sql.append(notSql).append(notVal);

		int row = 0;
		int col = 0;
		Object[][] params = new Object[rowCount][columnCount * 2];
		for (DataReg dataReg : dataRegs) {
			col = 0;
			for (int r = 0; r < 2; r++) {
				for (Field field : fieldPKs) {
					if(Modifier.isFinal(field.getModifiers())) {
						continue;
					}
					field.setAccessible(true);
					Object obj = field.get(dataReg.getDataRegPK());
					if(obj != null && "java.util.Date".equals(field.getType().getCanonicalName())){
						obj = Timestamp.valueOf(sdf.format(obj));
					}
					params[row][col++] = obj;
				}

				for (Field field : fields) {
					String name = field.getName();
					if("dataRegPK".equalsIgnoreCase(name) || "timeType".equalsIgnoreCase(name)) {
						continue;
					}
					field.setAccessible(true);
					Object obj = field.get(dataReg);
					if(obj != null && "java.util.Date".equals(field.getType().getCanonicalName())){
						obj = Timestamp.valueOf(sdf.format(obj));
					}
					params[row][col++] = obj;
				}
			}
			row++ ;
		}

		String strSql = sql.toString();
		return putInStorage(strSql, params);
	}

	private boolean batchUpdateSaveRegMysql(String tableName, List<DataReg> dataRegs) throws Exception{
		int rowCount = 	dataRegs.size();
		if(rowCount == 0) {
			return true;
		}
		Class clsPK = DataRegPK.class;
		Field[] fieldPKs = clsPK.getDeclaredFields();

		Class cls = DataReg.class;
		Field[] fields = cls.getDeclaredFields();

		int columnCount = fieldPKs.length + fields.length - 3;

		StringBuffer sql = new StringBuffer();


		sql.append("replace into ");
		sql.append(tableName + "(");

		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			String name = field.getName();

			if(!name.startsWith("R")) {
				name = StringUtils.humpToLine(name);
			} else {
				name = name.toLowerCase();
			}


			sql.append(name);
			sql.append(", ");
		}


		int index = 0;

		index = 0;
		for (Field field : fields) {
			String name = field.getName();
			if("dataRegPK".equalsIgnoreCase(name) || "timeType".equalsIgnoreCase(name)) {
				continue;
			}

			if(!name.startsWith("R")) {
				name = StringUtils.humpToLine(name);
			} else {
				name = name.toLowerCase();
			}

			sql.append(name);
			if(index < fields.length - 3) {
				sql.append(", ");

			}
			index++;
		}
		sql.append(" ");
		sql.append(") values (");

		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			sql.append("?");
			sql.append(", ");
		}

		index = 0;
		for (Field field : fields) {
			String name = field.getName();
			if("dataRegPK".equalsIgnoreCase(name) || "timeType".equalsIgnoreCase(name)) {
				continue;
			}
			sql.append("?");
			if(index < fields.length - 3) {
				sql.append(", ");
			}
			else {
				sql.append(")");
			}
			index++;
		}

		int row = 0;
		int col = 0;
		Object[][] params = new Object[rowCount][columnCount];
		for (DataReg dataReg : dataRegs) {
			col = 0;
			for (Field field : fieldPKs) {
				if(Modifier.isFinal(field.getModifiers())) {
					continue;
				}
				field.setAccessible(true);
				Object obj = field.get(dataReg.getDataRegPK());
				if(obj != null && "java.util.Date".equals(field.getType().getCanonicalName())){
					obj = Timestamp.valueOf(sdf.format(obj));
				}
				params[row][col++] = obj;
			}
			for (Field field : fields) {
				String name = field.getName();
				if("dataRegPK".equalsIgnoreCase(name) || "timeType".equalsIgnoreCase(name)) {
					continue;
				}
				field.setAccessible(true);
				Object obj = field.get(dataReg);
				if (obj != null && "java.util.Date".equals(field.getType().getCanonicalName())) {
					obj = Timestamp.valueOf(sdf.format(obj));
				}
				params[row][col++] = obj;
			}
			row++;
		}

		String strSql = sql.toString();
		return putInStorage(strSql, params);
	}

	public boolean batchUpdateSaveReg(String tableName, List<DataReg> dataRegs) throws Exception{

		if("Oracle".equalsIgnoreCase(serverConfig.getDatabaseType())) {
			return batchUpdateSaveRegOracle(tableName, dataRegs);
		} else if ("MySQL".equalsIgnoreCase(serverConfig.getDatabaseType())) {
			return batchUpdateSaveRegMysql(tableName, dataRegs);
		}


		return false;
	}

	public boolean batchSaveInsts(String tableName, List<DataInstMinutely> dataInsts) throws Exception{

		int rowCount = 	dataInsts.size();
		if(rowCount == 0) {
			return true;
		}

		Class clsPK = DataInstPK.class;
		Field[] fieldPKs = clsPK.getDeclaredFields();

		Class cls = DataInstMinutely.class;
		Field[] fields = cls.getDeclaredFields();

		int columnCount = fieldPKs.length + fields.length - 2;

		StringBuffer sql = new StringBuffer();
		StringBuffer val = new StringBuffer();

		sql.append("insert into ");
		sql.append(tableName);
		sql.append("( ");

		val.append("values (");

		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			String name = field.getName();
			name = StringUtils.humpToLine(name);

			sql.append(name);
			sql.append(", ");

			val.append("?");
			val.append(", ");
		}
		int index = 0;
		for (Field field : fields) {

			String name = field.getName();
			if("dataInstPK".equalsIgnoreCase(name)) {
				continue;
			}

			if(name.endsWith("A") || name.endsWith("B") || name.endsWith("C") || name.endsWith("0")) {
				name = name.substring(0, name.length() - 1).toLowerCase() + "_" + name.substring(name.length() - 1).toLowerCase();

			} else if (name.length() > 4) {
				name = StringUtils.humpToLine(name);
			}

			sql.append(name);
			val.append("?");
			if(index < fields.length - 2) {
				sql.append(", ");
				val.append(", ");
			}
			index++;
		}
		sql.append(") ");
		val.append(") ");

		sql.append(val);

		int row = 0;
		int col = 0;
		Object[][] params = new Object[rowCount][columnCount];
		for (DataInstMinutely dataInst : dataInsts) {
			col = 0;
			for (Field field : fieldPKs) {
				if(Modifier.isFinal(field.getModifiers())) {
					continue;
				}
				field.setAccessible(true);
				Object obj = field.get(dataInst.getDataInstPK());
				if(obj != null && "java.util.Date".equals(field.getType().getCanonicalName())){
					obj = Timestamp.valueOf(sdf.format(obj));
				}
				params[row][col++] = obj;
			}
			for (Field field : fields) {
				String name = field.getName();
				if("dataInstPK".equalsIgnoreCase(name)) {
					continue;
				}
				field.setAccessible(true);
				Object obj = field.get(dataInst);
				if(obj != null && "java.util.Date".equals(field.getType().getCanonicalName())){
					obj = Timestamp.valueOf(sdf.format(obj));
				}
				params[row][col++] = obj;
			}
			row++ ;
		}
		String strSql = sql.toString();
		return putInStorage(strSql, params);
	}

	private boolean batchUpdateSaveInstsOracle(String tableName, List<DataInstMinutely> dataInsts) throws Exception{
		int rowCount = 	dataInsts.size();
		if(rowCount == 0) {
			return true;
		}
		Class clsPK = DataInstPK.class;
		Field[] fieldPKs = clsPK.getDeclaredFields();

		Class cls = DataInstMinutely.class;
		Field[] fields = cls.getDeclaredFields();

		int columnCount = fieldPKs.length + fields.length - 2;

		StringBuffer sql = new StringBuffer();
		StringBuffer notSql = new StringBuffer();
		StringBuffer notVal = new StringBuffer();

		sql.append("merge into ");
		sql.append(tableName + " T1 ");

		sql.append("using(select ");
		int index = 0;
		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			String name = field.getName();
			name = StringUtils.humpToLine(name);

			sql.append("? as ");
			sql.append(name);
			if(index < fieldPKs.length - 2) {
				sql.append(", ");
			}
			index++;
		}
		sql.append(" from dual) T2 ");

		sql.append("on (");

		index = 0;
		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			String name = field.getName();
			name = StringUtils.humpToLine(name);

			sql.append("T1.");
			sql.append(name);
			sql.append(" = ");
			sql.append("T2.");
			sql.append(name);
			if(index < fieldPKs.length - 2) {
				sql.append(" and ");
			}

			index++;
		}
		sql.append(") ");
		sql.append("when matched then update set ");

		notSql.append("when not matched then ");
		notSql.append("insert (");
		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			String name = field.getName();
			name = StringUtils.humpToLine(name);
			notSql.append(name);
			notSql.append(", ");
		}
		index = 0;
		for (Field field : fields) {
			String name = field.getName();
			if("dataInstPK".equalsIgnoreCase(name)) {
				continue;
			}
			if(name.endsWith("A") || name.endsWith("B") || name.endsWith("C") || name.endsWith("0")) {
				name = name.substring(0, name.length() - 1).toLowerCase() + "_" + name.substring(name.length() - 1).toLowerCase();

			} else if (name.length() > 4) {
				name = StringUtils.humpToLine(name);
			}

			sql.append(name);
			sql.append(" = ?");

			notSql.append(name);

			if(index < fields.length - 2) {
				sql.append(", ");
				notSql.append(", ");
			}
			index++;
		}
		sql.append(" ");
		notSql.append(") values (");

		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			notVal.append("?");
			notVal.append(", ");
		}
		index = 0;
		for (Field field : fields) {
			String name = field.getName();
			if("dataInstPK".equalsIgnoreCase(name)) {
				continue;
			}
			notVal.append("?");
			if(index < fields.length - 2) {
				notVal.append(", ");
			}
			else {
				notVal.append(")");
			}

			index++;
		}
		sql.append(notSql).append(notVal);

		int row = 0;
		int col = 0;
		Object[][] params = new Object[rowCount][columnCount * 2];
		for (DataInstMinutely dataInst : dataInsts) {
			col = 0;
			for (int r = 0; r < 2; r++) {
				for (Field field : fieldPKs) {
					if(Modifier.isFinal(field.getModifiers())) {
						continue;
					}
					field.setAccessible(true);
					Object obj = field.get(dataInst.getDataInstPK());
					if(obj != null && "java.util.Date".equals(field.getType().getCanonicalName())){
						obj = Timestamp.valueOf(sdf.format(obj));
					}
					params[row][col++] = obj;
				}
				for (Field field : fields) {
					String name = field.getName();
					if("dataInstPK".equalsIgnoreCase(name)) {
						continue;
					}
					field.setAccessible(true);
					Object obj = field.get(dataInst);
					if(obj != null && "java.util.Date".equals(field.getType().getCanonicalName())){
						obj = Timestamp.valueOf(sdf.format(obj));
					}
					params[row][col++] = obj;
				}
			}
			row++ ;
		}

		String strSql = sql.toString();
		return putInStorage(strSql, params);
	}

	private boolean batchUpdateSaveInstsMysql(String tableName, List<DataInstMinutely> dataInsts) throws Exception{
		int rowCount = 	dataInsts.size();
		if(rowCount == 0) {
			return true;
		}
		Class clsPK = DataInstPK.class;
		Field[] fieldPKs = clsPK.getDeclaredFields();

		Class cls = DataInstMinutely.class;
		Field[] fields = cls.getDeclaredFields();

		int columnCount = fieldPKs.length + fields.length - 2;

		StringBuffer sql = new StringBuffer();

		sql.append("replace into ");
		sql.append(tableName + "(");
		int index = 0;

		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			String name = field.getName();
			name = StringUtils.humpToLine(name);
			sql.append(name);
			sql.append(", ");
		}
		index = 0;
		for (Field field : fields) {
			String name = field.getName();
			if("dataInstPK".equalsIgnoreCase(name)) {
				continue;
			}
			if(name.endsWith("A") || name.endsWith("B") || name.endsWith("C") || name.endsWith("0")) {
				name = name.substring(0, name.length() - 1).toLowerCase() + "_" + name.substring(name.length() - 1).toLowerCase();

			} else if (name.length() > 4) {
				name = StringUtils.humpToLine(name);
			}
			sql.append(name);
			if(index < fields.length - 2) {
				sql.append(", ");
			}
			index++;
		}
		sql.append(") values(");

		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			sql.append("?");
			sql.append(", ");
		}
		index = 0;
		for (Field field : fields) {
			String name = field.getName();
			if("dataInstPK".equalsIgnoreCase(name)) {
				continue;
			}
			sql.append("?");
			if(index < fields.length - 2) {
				sql.append(", ");
			}
			else {
				sql.append(")");
			}

			index++;
		}

		int row = 0;
		int col = 0;
		Object[][] params = new Object[rowCount][columnCount];
		for (DataInstMinutely dataInst : dataInsts) {
			col = 0;
			for (Field field : fieldPKs) {
				if (Modifier.isFinal(field.getModifiers())) {
					continue;
				}
				field.setAccessible(true);
				Object obj = field.get(dataInst.getDataInstPK());
				if (obj != null && "java.util.Date".equals(field.getType().getCanonicalName())) {
					obj = Timestamp.valueOf(sdf.format(obj));
				}
				params[row][col++] = obj;
			}
			for (Field field : fields) {
				String name = field.getName();
				if ("dataInstPK".equalsIgnoreCase(name)) {
					continue;
				}
				field.setAccessible(true);
				Object obj = field.get(dataInst);
				if (obj != null && "java.util.Date".equals(field.getType().getCanonicalName())) {
					obj = Timestamp.valueOf(sdf.format(obj));
				}
				params[row][col++] = obj;
			}

			row++;
		}

		String strSql = sql.toString();
		return putInStorage(strSql, params);
	}

	public boolean batchUpdateSaveInsts(String tableName, List<DataInstMinutely> dataInsts) throws Exception{
		if("Oracle".equalsIgnoreCase(serverConfig.getDatabaseType())) {
			return batchUpdateSaveInstsOracle(tableName, dataInsts);
		} else if ("MySQL".equalsIgnoreCase(serverConfig.getDatabaseType())) {
			return batchUpdateSaveInstsMysql(tableName, dataInsts);
		}
		return false;
	}

	public void batchSaveLogOracle(List<MdmDataUpdateLog> dataLogs) throws Exception {
		int rowCount = 	dataLogs.size();
		if(rowCount == 0) {
			return;
		}
		Class clsPK = MdmDataUpdateLogPK.class;
		Field[] fieldPKs = clsPK.getDeclaredFields();

		Class cls = MdmDataUpdateLog.class;
		Field[] fields = cls.getDeclaredFields();

		int columnCount = fieldPKs.length + fields.length - 2;

		StringBuffer sql = new StringBuffer();
		StringBuffer notSql = new StringBuffer();
		StringBuffer notVal = new StringBuffer();

		sql.append("merge into mdm_data_update_log T1 ");
		sql.append("using(select ");
		int index = 0;
		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			String name = field.getName();
			name = StringUtils.humpToLine(name);

			sql.append("? as ");
			sql.append(name);
			if(index < fieldPKs.length - 2) {
				sql.append(", ");
			}
			index++;
		}
		sql.append(" from dual) T2 ");

		sql.append("on (");

		index = 0;
		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			String name = field.getName();
			name = StringUtils.humpToLine(name);

			sql.append("T1.");
			sql.append(name);
			sql.append(" = ");
			sql.append("T2.");
			sql.append(name);
			if(index < fieldPKs.length - 2) {
				sql.append(" and ");
			}

			index++;
		}
		sql.append(") ");
		sql.append("when matched then update set ");

		notSql.append("when not matched then ");
		notSql.append("insert (");
		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			String name = field.getName();
			name = StringUtils.humpToLine(name);
			notSql.append(name);
			notSql.append(", ");
		}
		index = 0;
		for (Field field : fields) {
			String name = field.getName();
			if("mdmDataUpdateLogPK".equalsIgnoreCase(name)) {
				continue;
			}
			name = StringUtils.humpToLine(name);
			sql.append(name);
			sql.append(" = ?");

			notSql.append(name);

			if(index < fields.length - 2) {
				sql.append(", ");
				notSql.append(", ");
			}
			index++;
		}
		sql.append(" ");
		notSql.append(") values (");

		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			notVal.append("?");
			notVal.append(", ");
		}
		index = 0;
		for (Field field : fields) {
			String name = field.getName();
			if("mdmDataUpdateLogPK".equalsIgnoreCase(name)) {
				continue;
			}
			notVal.append("?");
			if(index < fields.length - 2) {
				notVal.append(", ");
			}
			else {
				notVal.append(")");
			}

			index++;
		}
		sql.append(notSql).append(notVal);

		int row = 0;
		int col = 0;
		Object[][] params = new Object[rowCount][columnCount * 2];
		for (MdmDataUpdateLog dataLog : dataLogs) {
			col = 0;
			for (int r = 0; r < 2; r++) {
				for (Field field : fieldPKs) {
					if(Modifier.isFinal(field.getModifiers())) {
						continue;
					}
					field.setAccessible(true);
					Object obj = field.get(dataLog.getMdmDataUpdateLogPK());
					if(obj != null && "java.util.Date".equals(field.getType().getCanonicalName())){
						obj = Timestamp.valueOf(sdf.format(obj));
					}
					params[row][col++] = obj;
				}
				for (Field field : fields) {
					String name = field.getName();
					if("mdmDataUpdateLogPK".equalsIgnoreCase(name)) {
						continue;
					}
					field.setAccessible(true);
					Object obj = field.get(dataLog);
					if(obj != null && "java.util.Date".equals(field.getType().getCanonicalName())){
						obj = Timestamp.valueOf(sdf.format(obj));
					}
					params[row][col++] = obj;
				}
			}
			row++ ;
		}

		String strSql = sql.toString();
		putInStorage(strSql, params);


	}

	public void batchSaveLogMysql(List<MdmDataUpdateLog> dataLogs) throws Exception {
		int rowCount = 	dataLogs.size();
		if(rowCount == 0) {
			return;
		}
		Class clsPK = MdmDataUpdateLogPK.class;
		Field[] fieldPKs = clsPK.getDeclaredFields();

		Class cls = MdmDataUpdateLog.class;
		Field[] fields = cls.getDeclaredFields();

		int columnCount = fieldPKs.length + fields.length - 2;

		StringBuffer sql = new StringBuffer();

		sql.append("replace into mdm_data_update_log (");

		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			String name = field.getName();
			name = StringUtils.humpToLine(name);
			sql.append(name);
			sql.append(", ");
		}
		int index = 0;
		index = 0;
		for (Field field : fields) {
			String name = field.getName();
			if ("MdmDataUpdateLogPK".equalsIgnoreCase(name)) {
				continue;
			}
			name = StringUtils.humpToLine(name);
			sql.append(name);
			if (index < fields.length - 2) {
				sql.append(", ");
			}
			index++;
		}
		sql.append(") values(");

		for (Field field : fieldPKs) {
			if(Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			sql.append("?");
			sql.append(", ");
		}
		index = 0;
		for (Field field : fields) {
			String name = field.getName();
			if("MdmDataUpdateLogPK".equalsIgnoreCase(name)) {
				continue;
			}
			sql.append("?");
			if(index < fields.length - 2) {
				sql.append(", ");
			}
			else {
				sql.append(")");
			}

			index++;
		}

		int row = 0;
		int col = 0;
		Object[][] params = new Object[rowCount][columnCount];
		for (MdmDataUpdateLog dataLog : dataLogs) {
			col = 0;
			for (Field field : fieldPKs) {
				if (Modifier.isFinal(field.getModifiers())) {
					continue;
				}
				field.setAccessible(true);
				Object obj = field.get(dataLog.getMdmDataUpdateLogPK());
				if (obj != null && "java.util.Date".equals(field.getType().getCanonicalName())) {
					obj = Timestamp.valueOf(sdf.format(obj));
				}
				params[row][col++] = obj;
			}
			for (Field field : fields) {
				String name = field.getName();
				if ("MdmDataUpdateLogPK".equalsIgnoreCase(name)) {
					continue;
				}
				field.setAccessible(true);
				Object obj = field.get(dataLog);
				if (obj != null && "java.util.Date".equals(field.getType().getCanonicalName())) {
					obj = Timestamp.valueOf(sdf.format(obj));
				}
				params[row][col++] = obj;
			}

			row++;
		}

		String strSql = sql.toString();
		putInStorage(strSql, params);

	}

	public void batchSaveLog(List<MdmDataUpdateLog> dataLogs) throws Exception {
		if("Oracle".equalsIgnoreCase(serverConfig.getDatabaseType())) {
			batchSaveLogOracle(dataLogs);
		} else if ("MySQL".equalsIgnoreCase(serverConfig.getDatabaseType())) {
			batchSaveLogMysql(dataLogs);
		}

	}
}
