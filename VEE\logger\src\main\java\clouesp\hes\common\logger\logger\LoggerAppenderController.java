/*
 * 文件名：LoggerCfg.java
 * 版权：Copyright by Power7000 Team
 * 描述：
 * 修改人：jybai
 * 修改时间：2017年10月23日
 * 跟踪单号：
 * 修改单号：
 * 修改内容：
 */

package clouesp.hes.common.logger.logger;


import java.io.Serializable;
import java.util.Map;
import java.util.Vector;

import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.core.Appender;
import org.apache.logging.log4j.core.Layout;
import org.apache.logging.log4j.core.LoggerContext;
import org.apache.logging.log4j.core.appender.ConsoleAppender;
import org.apache.logging.log4j.core.appender.FileAppender;
import org.apache.logging.log4j.core.config.AppenderRef;
import org.apache.logging.log4j.core.config.Configuration;
import org.apache.logging.log4j.core.config.LoggerConfig;
import org.apache.logging.log4j.core.layout.PatternLayout;


class LoggerAppenderController {

    private Appender appender = null;

    private Logger logger = null;

    private LoggerContext loggerContext = null;

    private Configuration configuration = null;

    private Layout<? extends Serializable> layout = null;

    private LoggerConfig loggerConfig = null;

    public LoggerAppenderController() {

    }

    public Logger getLogger(String key) {

        logger = LogManager.getLogger(key);
        loggerContext = (LoggerContext)LogManager.getContext(false);
        configuration = loggerContext.getConfiguration();

        layout = PatternLayout.createDefaultLayout(configuration);
        //layout = PatternLayout.createLayout("%msg", null, configuration, null, null, true, false,
        //    null, null);

        AppenderRef ref = AppenderRef.createAppenderRef(key, null, null);
        AppenderRef[] refs = new AppenderRef[] {ref};
        
        loggerConfig = LoggerConfig.createLogger(false, Level.ALL, key, "true", refs, null,
            configuration, null);

        configuration.addLogger(key, loggerConfig);

        loggerContext.updateLoggers();

        return logger;
    }
    
    public Appender getAppender(String key){
        Map<String, Appender> appenders = loggerConfig.getAppenders();
        if (!appenders.containsKey(key)) return null;
        return appenders.get(key);
    }

    public void addConsoleAppender(String key) {
        ConsoleAppender consoleAppender = ConsoleAppender.createDefaultAppenderForLayout(layout);
        addAppender(consoleAppender);
    }

    public void addLuceneAppender(String key) {
        LuceneAppender luceneAppender = LuceneAppender.createAppender(key,
            loggerConfig.getFilter(), layout);
        addAppender(luceneAppender);
    }

    public void addFileAppender(String key, String fileName) {
        @SuppressWarnings("deprecation")
        FileAppender fileAppdender = FileAppender.createAppender(fileName, "true", "false",
            key, null, "true", "true", null, layout, null, null, null, configuration);
        addAppender(fileAppdender);
    }

    public void removeAppender(String key) {
        if (loggerConfig.getAppenders().containsKey(key)) {
            loggerConfig.getAppenders().get(key).stop();
            loggerConfig.removeAppender(key);
        }
    }

    public void removeAllAppender() {
        Map<String, Appender> appenders = loggerConfig.getAppenders();
        Vector<String> appenderNameset = new Vector<String>();
        for (Map.Entry<String, Appender> entry : appenders.entrySet()) {
            appenderNameset.add(entry.getKey());
            entry.getValue().stop();
        }
        for (int i = 0; i < appenderNameset.size(); i++ )
            loggerConfig.removeAppender(appenderNameset.get(i));

    }

    /** Override the logging level of a given logger, return the previous level */
    public void setLevel(LoggerLevel level) {
        loggerConfig.setLevel(LoggerUtils.getInstance().LoggerLevel2Level(level));
        loggerContext.updateLoggers(configuration);
    }

    public void start(String key) {
        Map<String, Appender> appenders = loggerConfig.getAppenders();
        if (appenders.containsKey(key)) appenders.get(key).start();
    }

    public void stop(String key) {
        Map<String, Appender> appenders = loggerConfig.getAppenders();
        if (appenders.containsKey(key)) appenders.get(key).stop();
    }

    private void addAppender(Appender appender) {
        configuration.addAppender(appender);
        loggerConfig.addAppender(appender, loggerConfig.getLevel(), loggerConfig.getFilter());
        this.appender = appender;
    }

    public Appender getappender() {
        return this.appender;
    }   
}
