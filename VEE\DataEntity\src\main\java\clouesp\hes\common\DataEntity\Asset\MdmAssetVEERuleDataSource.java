package clouesp.hes.common.DataEntity.Asset;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.IdClass;
import javax.persistence.Table;

@Entity
@Table(name= "mdm_asset_vee_rule_datasource")
@IdClass(MdmAssetVEERuleDataSourcePK.class)
public class MdmAssetVEERuleDataSource  {
	@Id
	@Column(name = "rule_id", nullable = false, columnDefinition = "varchar(32)")
	private String ruleId;
	
	@Id
	@Column(name = "data_type")
	private Integer dataType;
	
	@Column(name = "start_cycle")
	private Integer startCycle;
	
	@Column(name = "cycle_count")
	private Integer cycleCount;
	
	@Column(name = "cycle_type")
	private Integer cycleType;
	
	@Column(name = "data_code", columnDefinition = "varchar(128)")
	private String dataCode;
	
	@Id
	@Column(name = "scheme_type", columnDefinition = "varchar(32)")
	private Integer schemeType;
	
	@Column(name = "key_flag")
	private Integer keyFlag;
	
	@Id
	@Column(name = "source_event_type", columnDefinition = "varchar(32)")
	private String sourceEventType;	

	public String getRuleId() {
		return ruleId;
	}

	public void setRuleId(String ruleId) {
		this.ruleId = ruleId;
	}

	public Integer getDataType() {
		return dataType;
	}

	public void setDataType(Integer dataType) {
		this.dataType = dataType;
	}

	public Integer getStartCycle() {
		return startCycle;
	}

	public void setStartCycle(Integer startCycle) {
		this.startCycle = startCycle;
	}

	public Integer getCycleCount() {
		return cycleCount;
	}

	public void setCycleCount(Integer cycleCount) {
		this.cycleCount = cycleCount;
	}

	public Integer getCycleType() {
		return cycleType;
	}

	public void setCycleType(Integer cycleType) {
		this.cycleType = cycleType;
	}

	public String getDataCode() {
		return dataCode;
	}

	public void setDataCode(String dataCode) {
		this.dataCode = dataCode;
	}

	public Integer getSchemeType() {
		return schemeType;
	}

	public void setSchemeType(Integer schemeType) {
		this.schemeType = schemeType;
	}

	public Integer getKeyFlag() {
		return keyFlag;
	}

	public void setKeyFlag(Integer keyFlag) {
		this.keyFlag = keyFlag;
	}

	public String getSourceEventType() {
		return sourceEventType;
	}

	public void setSourceEventType(String sourceEventType) {
		this.sourceEventType = sourceEventType;
	}
	
}
