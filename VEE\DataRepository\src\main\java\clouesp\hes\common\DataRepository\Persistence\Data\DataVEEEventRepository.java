package clouesp.hes.common.DataRepository.Persistence.Data;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import clouesp.hes.common.DataEntity.Data.DataVEEEvent;

public interface DataVEEEventRepository extends JpaRepository<DataVEEEvent, String>{
	@Query(value = "select * from (select * from mdm_data_vee_event where "
			+ "object_id = :objectId "
			+ "and event_id = :eventId "
			+ "and tv < to_date(:tv,'yyyy-mm-dd hh24:mi:ss') "
			+ "order by tv desc) where rownum <= 1 "
			, nativeQuery = true)
	DataVEEEvent oracleFindEvent(
			@Param("objectId") String objectId,
			@Param("eventId") String eventId,
			@Param("tv") String tv
			);

	@Query(value = "select * from (select * from mdm_data_vee_event where "
			+ "object_id = :objectId "
			+ "and event_id = :eventId "
			+ "and tv <  :tv  "
			+ "order by tv desc) a limit 1 "
			, nativeQuery = true)
	DataVEEEvent mysqlFindEvent(
			@Param("objectId") String objectId,
			@Param("eventId") String eventId,
			@Param("tv") String tv
	);

}
