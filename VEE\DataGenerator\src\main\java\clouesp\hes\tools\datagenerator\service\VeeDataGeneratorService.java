package clouesp.hes.tools.datagenerator.service;

import clouesp.hes.common.DataEntity.Asset.*;
import clouesp.hes.common.DataEntity.Data.DataReg;
import clouesp.hes.common.DataEntity.Dict.DictDetail;
import clouesp.hes.common.DataEntity.Dict.DictVeeEvent;
import clouesp.hes.common.DataRepository.Persistence.Asset.*;

import clouesp.hes.common.DataRepository.Persistence.Data.DataRegDailyRepository;
import clouesp.hes.common.DataRepository.Persistence.Data.DataRegMinutelyRepository;
import clouesp.hes.common.DataRepository.Persistence.Data.DataRegMonthlyRepository;
import clouesp.hes.common.DataRepository.Persistence.Dict.DictDetailRepository;
import clouesp.hes.common.DataRepository.Persistence.Dict.DictVeeEventRepository;
import clouesp.hes.common.DataRepository.RealTime.Dict.RtDictDetailRepository;
import jline.internal.Log;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


@Service("veeDataGeneratorService")
public class VeeDataGeneratorService {

    @Autowired
    private MdmAssetVEERuleDataSourceRepository mdmAssetVEERuleDataSourceRepository;
    @Autowired
    private MdmAssetVEERuleParamRepository mdmAssetVEERuleParamRepository;
    @Autowired
    private MdmAssetVEERuleRepository mdmAssetVEERuleRepository;
    @Autowired
    private AssetMeterRepository assetMeterRepository;
    @Autowired
    private MdmAssetServicePointRepository mdmAssetServicePointRepository;
    @Autowired
    private MdmAssetCalcObjRepository mdmAssetCalcObjRepository;
    @Autowired
    private MdmAssetVeeGroupRepository mdmAssetVeeGroupRepository;
    @Autowired
    private DictVeeEventRepository dictVeeEventRepository;

    @Autowired
    private DataRegMinutelyRepository dataRegMinutelyRepository;

    @Autowired
    private DataRegDailyRepository dataRegDailyRepository;

    @Autowired
    private DataRegMonthlyRepository dataRegMonthlyRepository;

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private MdmAssetCalcObjMapRepository mdmAssetCalcObjMapRepository;
    @Autowired
    private DictDetailRepository dictDetailRepository;




    //由于测试时，可能只配置一个sdp，生成数据时，针对所有事件导致混乱，所以一条一条规则生成
    public String GenerateVeeEventData(String veeGroupName,String eventId, String startTv, int errCount) {

        String rnt = "0";

        //开始根据事件id生成事件对应规则的数据
        Log.info("-------开始生成VEE数据-------");
        Log.info("veeGroupName:" + veeGroupName);
        Log.info("eventId:" + eventId);
        Log.info("startTv:" + startTv);
        Log.info("errCount:" + errCount);



        try
        {

            //查找当前分组信息
            List<MdmAssetVeeGroup> mdmAssetVeeGroups = mdmAssetVeeGroupRepository.findAllByName(veeGroupName);

            if(mdmAssetVeeGroups == null || mdmAssetVeeGroups.size() == 0)
            {
                Log.info("-------未找到对应的VEE分组信息-------");

                rnt = "未找到对应的VEE分组信息";

                return rnt;
            }

            //开始查找当前eventid是否存在
            Optional<DictVeeEvent> optional = dictVeeEventRepository.findById(eventId);
            if(!optional.isPresent())
            {
                Log.info("-------未找到对应的VEE事件信息-------");

                rnt = "未找到对应的VEE事件信息";

                return rnt;
            }

            for (MdmAssetVeeGroup mdmAssetVeeGroup:mdmAssetVeeGroups
                 ) {
                //找到当前分组下对应的eventid下的所有规则
                String veeGrpId = mdmAssetVeeGroup.getId();

                List<MdmAssetVEERule> rules = mdmAssetVEERuleRepository.findAllByMgIdAndEventId(veeGrpId,eventId);

                if(rules == null
                        || rules.size() ==0)
                {
                    Log.info("-------未找根据vee组id和事件id找到对应配置【veeGrpId:"+veeGrpId+",eventId:"+eventId+"】-------");

                    continue;
                }


                //开始循环当前组及事件id对应的配置，理论上一个组中只配置一个事件规则
                for (MdmAssetVEERule rule: rules
                ) {

                    List<MdmAssetVEERuleDataSource> veeRuleDataSources = mdmAssetVEERuleDataSourceRepository.findAllByRuleId(rule.getId());

                    if(veeRuleDataSources == null || veeRuleDataSources.size()==0)
                    {
                        Log.info("-------未找到对应规则【ruleId:"+rule.getId()+"】-------");

                        continue;
                    }

                    //根据组别，查找是sdp还是计算对象
                    if(mdmAssetVeeGroup.getObjectType()==7) //sdp
                    {

                        //获取所有当前组被使用的sdp数据
                        List<MdmAssetServicePoint> sdpList = mdmAssetServicePointRepository.findAllByVeeValidationGroupId(mdmAssetVeeGroup.getId());

                        Map<MdmAssetServicePoint,String> errPointMap = this.buildSdpDataReg(rule,sdpList, veeRuleDataSources, startTv,errCount,eventId);

                        //构造错误信息
                        for (Map.Entry<MdmAssetServicePoint,String> entry: errPointMap.entrySet())
                        {

                            String sn = entry.getKey().getSn();

                            rnt = rnt + "【"+sn+":"+entry.getValue()+"】";
                        }

                    }
                    else if(mdmAssetVeeGroup.getObjectType()==8) //计算对象
                    {


                        //获取使用了当前线损校验的计算对象

                        List<MdmAssetCalcObj> mdmAssetCalcObjList = mdmAssetCalcObjRepository.findAllByVeeValidationGroupId(veeGrpId);

                        List<MdmAssetVEERuleParam> params = mdmAssetVEERuleParamRepository.findAllByRuleId(rule.getId());


                        //线损
                        Map<MdmAssetCalcObj,String> errPointMap = this.buildCalObjData_New(rule,mdmAssetCalcObjList,veeRuleDataSources,startTv,errCount,params);

                        //构造错误信息
                        for (Map.Entry<MdmAssetCalcObj,String> entry: errPointMap.entrySet())
                        {

                            String name = entry.getKey().getName();

                            rnt = rnt + "【"+name+":"+entry.getValue()+"】";
                        }

                    }

                }
                //获取当前分组的objectType类别，如果是sdp，获取所有sdp，如果是计算对象，获取所有计算对象

            }






        }
       catch (Exception ex)
        {
            Log.info(ex.getMessage());

            ex.printStackTrace();

            rnt = "-------未知异常-------";
        }


        return rnt;
    }

    private List<MdmAssetServicePoint> getCalObjSdpList(MdmAssetCalcObj calcObj)
    {
        //获取所有用来做校验的sdp列表，递归获取
        List<MdmAssetServicePoint> spdList = new ArrayList<>();

        String calId = calcObj.getId();
        List<MdmAssetCalcObjMap>  mdmAssetCalcObjMaps = mdmAssetCalcObjMapRepository.findAllById(calId);

        if(mdmAssetCalcObjMaps != null && mdmAssetCalcObjMaps.size() > 0)
        {

            for (MdmAssetCalcObjMap calObjMap: mdmAssetCalcObjMaps
            ) {
                //spd
                if(calObjMap.getMeteringType() == 1)
                {
                    MdmAssetServicePoint sdp = mdmAssetServicePointRepository.findById(calObjMap.getMeteringId()).get();

                    //出入标志
                    sdp.setLineLossInOut(calObjMap.getType());
                    //取值字段，1,2,3,4
                    spdList.add(sdp);
                }
                else
                {
                    //计算对象
                    MdmAssetCalcObj calcObj1 = mdmAssetCalcObjRepository.findById(calObjMap.getMeteringId()).get();
                    this.getSubCalObjSdpList(calcObj1,spdList);

                }

            }
        }

        return spdList;
    }
    private void getSubCalObjSdpList(MdmAssetCalcObj calcObj,List<MdmAssetServicePoint> spdList)
    {
        //获取当前计量点的关联
        String calId = calcObj.getId();

        List<MdmAssetCalcObjMap>  mdmAssetCalcObjMaps = mdmAssetCalcObjMapRepository.findAllById(calId);


        if(mdmAssetCalcObjMaps != null && mdmAssetCalcObjMaps.size() > 0)
        {

            for (MdmAssetCalcObjMap calObjMap: mdmAssetCalcObjMaps
                 ) {
                //spd
                if(calObjMap.getMeteringType() == 1)
                {
                    MdmAssetServicePoint sdp = mdmAssetServicePointRepository.findById(calObjMap.getMeteringId()).get();

                    //出入标志
                    sdp.setLineLossInOut(calObjMap.getType());
                    //取值字段，1,2,3,4
                    spdList.add(sdp);
                }
                else
                {
                    //计算对象
                    MdmAssetCalcObj calcObj1 = mdmAssetCalcObjRepository.findById(calObjMap.getMeteringId()).get();
                    this.getSubCalObjSdpList(calcObj1,spdList);

                }

            }
        }
    }
    //把错误数据的位置返回回去
    private Map<MdmAssetServicePoint,String> buildSdpDataReg(MdmAssetVEERule rule,
                                 List<MdmAssetServicePoint> sdpList,
                                 List<MdmAssetVEERuleDataSource> dataSourceList,
                                 String sTv,
                                 int errPlanCount,
                                 String eventId
                                 ) throws Exception {
        Map<MdmAssetServicePoint,String> rnt = new LinkedHashMap<>();

        for (MdmAssetServicePoint sdpPoint:sdpList
        ) {

            String errDate = "";

            DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            LocalDateTime lstv = LocalDateTime.parse(sTv.replace(" ","T"));

            Instant instant = lstv.atZone(ZoneId.systemDefault()).toInstant();

            Date startTv = Date.from(instant);


            //删除sdp对应的所有数据
            mdmAssetServicePointRepository.deleteMdmDataRegMinutely(sdpPoint.getId(),startTv);
            mdmAssetServicePointRepository.deleteMdmDataRegDayly(sdpPoint.getId(),startTv);
            mdmAssetServicePointRepository.deleteMdmDataRegMonthly(sdpPoint.getId(),startTv);

            Map<String,Double> dataMinMap = new LinkedHashMap<>();
            Map<String,Double> dataDayMap = new LinkedHashMap<>();
            Map<String,Double> dataMonthMap = new LinkedHashMap<>();
            //生成正常数据,先生成60分钟的，日的数据为当天60分钟0点数据，月为1号0点数据

            //分钟数据
            LocalDateTime tmpEndTv = LocalDateTime.now();

            String tmpTv = sTv.replace(" ","T");
            LocalDateTime lStartTv = LocalDateTime.parse(tmpTv);
            String lastTv = "";

            //创建数据
            Map<String,Map<String,Double>> allCrtDataReg = this.createSdpDataReg(sdpPoint,sTv);

            dataMinMap = allCrtDataReg.get("minutely");

            dataDayMap = allCrtDataReg.get("dayly");

            dataMonthMap = allCrtDataReg.get("monthly");


            //开始构造错误数据

            //单数据源
            if(rule.getMultDatasource() == 0)
            {
                //每个数据源构造指定的错误数据
                for (MdmAssetVEERuleDataSource dataSource: dataSourceList
                ) {

                    //参数项
                    List<MdmAssetVEERuleParam> ruleParams = mdmAssetVEERuleParamRepository.findAllByRuleId(dataSource.getRuleId());

                    //每个数据源最早可以构建的时间点
                    LocalDateTime firstErrTime = LocalDateTime.parse(sTv.replace(" ","T"));
                    int step = Math.abs(dataSource.getStartCycle());
                    int schemeType = dataSource.getSchemeType();
                    int startIndex = 0;
                    int endIndex = 0;
                    ArrayList<String> keys = new ArrayList<>();
                    List<DataReg> dataRegList = new ArrayList<>();
                    Map<String, Double> currDataReg = new LinkedHashMap<>();

                    if(schemeType ==1)
                    {
                        firstErrTime = firstErrTime.plusMinutes(step*60);
                        keys = new ArrayList<>(dataMinMap.keySet()) ;
                        startIndex = keys.indexOf(firstErrTime.format(timeFormatter));
                        if(startIndex == -1)
                        {
                            //没有获得最小的取数开始位置
                            continue;
                        }
                        endIndex = dataMinMap.size()-1;

                        currDataReg = dataMinMap;




                    }
                    else if(schemeType ==2)
                    {
                        firstErrTime = firstErrTime.plusDays(step);
                        keys = new ArrayList<>(dataDayMap.keySet()) ;
                        startIndex = keys.indexOf(firstErrTime.format(timeFormatter));
                        if(startIndex == -1)
                        {
                            //没有获得最小的取数开始位置
                            continue;
                        }
                        endIndex = dataDayMap.size()-1;

                        currDataReg = dataDayMap;
                    }
                    else if(schemeType ==3)
                    {
                        firstErrTime = firstErrTime.plusMonths(step);
                        keys = new ArrayList<>(dataMonthMap.keySet()) ;
                        startIndex = keys.indexOf(firstErrTime.format(timeFormatter));
                        if(startIndex == -1)
                        {
                            //没有获得最小的取数开始位置
                            continue;
                        }
                        endIndex = dataMonthMap.size()-1;

                        currDataReg = dataMonthMap;
                    }

                    ArrayList<String> errIndexList = new ArrayList<String>();
                    int tmpErrCount = 1;
                    int errCount = errPlanCount;
                    //下一个异常值的位置，最好不要跟当前值太近，避免取用造出来的错误值
                    Integer mustSetp = dataSource.getCycleCount();

                    //不够生成那么多的错误
                    if((endIndex-startIndex+1) < errCount)
                    {
                        errCount = endIndex-startIndex+1;
                        //continue;
                    }
                    while (1==1)
                    {
                        if(tmpErrCount > errCount)
                        {
                            break;
                        }
                        int tmpIndex = this.randomInteger(startIndex,endIndex);
                        String key  = keys.get(tmpIndex);

                        
                        if(errIndexList.indexOf(key) >= 0)//存在
                        {

                            continue;
                        }
                        else
                        {
                            errIndexList.add(key);
                            tmpErrCount++;
                            /*
                            //往前回溯指定点
                            int tmpStartIndex = tmpIndex-mustSetp;


                            //是否与其他的错误列表中的值间隔指定距离
                            boolean isok = true;
                            for (String o:errIndexList
                            ) {

                                int pe = keys.indexOf(o);
                                int ps = pe - Math.abs(mustSetp);

                                if(tmpStartIndex >= ps && tmpStartIndex <= pe)
                                {
                                    isok = false;
                                    break;
                                }

                            }

                            if(isok)
                            {
                                errIndexList.add(key);
                                tmpErrCount++;
                            }
                            else
                            {
                                continue;
                            }

                             */

                        }

                    }


                    //判断各种异常类型
                    switch (eventId)
                    {
                        //缺数
                        case "1001001":
                        {

                            dataRegList = this.generateMissData(currDataReg,
                                    errIndexList,
                                    sdpPoint.getId());
                            break;

                        }
                        //总分不等
                        case "1001008":
                        {
                            dataRegList = this.generatTotalUnequalTariff(currDataReg,
                                    errIndexList,
                                    sdpPoint.getId());
                            break;
                        }
                        //突然变大
                        case "1001004":{

                            dataRegList = this.generatAbnormalBigger(currDataReg,
                                    errIndexList,
                                    sdpPoint.getId(),
                                    dataSource,
                                    ruleParams
                                    );
                            break;
                        }
                        //突然变小
                        case "1001005":
                        {
                            dataRegList = this.generatAbnormalSmaller(currDataReg,
                                    errIndexList,
                                    sdpPoint.getId(),
                                    dataSource,
                                    ruleParams
                            );
                            break;
                        }
                        //一直是0
                        case "1001007":
                        {
                            dataRegList = this.generatAlwaysZero(currDataReg,
                                    errIndexList,
                                    sdpPoint.getId(),
                                    dataSource,
                                    ruleParams
                            );
                            break;
                        }
                        //0消耗
                        case "1001006":
                        {
                            dataRegList = this.generatZeroConsumption(currDataReg,
                                    errIndexList,
                                    sdpPoint.getId(),
                                    dataSource,
                                    ruleParams
                            );
                            break;
                        }
                    }

                    //保存数据
                    if(schemeType ==1)
                    {
                        //List<DataRegMinutely> dataRegMinutelyList = DataUtils.convertDataReg(dataRegList, DataRegMinutely.class);

                        dataAccessService.batchSave("MDM_DATA_REG_MINUTELY",dataRegList);
                        //dataRegMinutelyRepository.saveAll(dataRegMinutelyList);
                    }
                    else if(schemeType ==2)
                    {
                        dataAccessService.batchSave("MDM_DATA_REG_DAYLY",dataRegList);
                        //List<DataRegDaily> dataRegDailyList = DataUtils.convertDataReg(dataRegList, DataRegDaily.class);

                        //dataRegDailyRepository.saveAll(dataRegDailyList);
                    }
                    else if(schemeType ==3)
                    {
                        dataAccessService.batchSave("MDM_DATA_REG_MONTHLY",dataRegList);
                        //List<DataRegMonthly> dataRegMonthlyList = DataUtils.convertDataReg(dataRegList, DataRegMonthly.class);

                        //dataRegMonthlyRepository.saveAll(dataRegMonthlyList);
                    }


                    //错误信息
                    String tmp = "";
                    for (String errKey:errIndexList
                         ) {
                        tmp = tmp + errKey + ",";
                    }

                    if(schemeType == 1)
                    {


                        errDate = errDate + "分钟("+tmp.substring(0,tmp.length()-2)+")";
                    }
                    else if(schemeType == 2)
                    {
                        errDate = errDate + "日("+tmp.substring(0,tmp.length()-2)+")";
                    }
                    else if(schemeType == 3)
                    {
                        errDate = errDate + "月("+tmp.substring(0,tmp.length()-2)+")";
                    }

                    rnt.put(sdpPoint,errDate);


                }
            }
            else //多数据源
            {


                Map<String, Map<String,Double>> sourceAllDataMap = new HashMap<>();
                sourceAllDataMap.put("minutely",dataMinMap);
                sourceAllDataMap.put("dayly",dataDayMap);
                sourceAllDataMap.put("monthly",dataMonthMap);

                Map<String,List<DataReg>> allDataReg = new HashMap<>();

                List<String> errList = new ArrayList<>();
                //曲线数据不等于日冻结数据
                //曲线数据不等于月冻结数据
                //日冻结不等于月冻结
                //三种判断方式一致
                switch (eventId){

                    case "1001009":
                    case "1001010":
                    case "1001011": {
                        allDataReg = this.generatMultDataSourceData(sourceAllDataMap,
                                errPlanCount,
                                sdpPoint.getId(),
                                dataSourceList,
                                errList
                                );
                        break;
                    }
                }

                String tmpErrDate = "";
                //错误信息
                for (String s:errList
                     ) {
                    tmpErrDate = tmpErrDate + s + ",";

                }

                rnt.put(sdpPoint,tmpErrDate.substring(0,tmpErrDate.length()-1));



                for (Map.Entry<String, List<DataReg>> entry :allDataReg.entrySet()
                     ) {

                    List<DataReg> dataRegList = entry.getValue();

                    if(entry.getKey().equals("minutely"))
                    {
                        dataAccessService.batchSave("MDM_DATA_REG_MINUTELY",dataRegList);
                    }
                    else if(entry.getKey().equals("dayly"))
                    {
                        dataAccessService.batchSave("MDM_DATA_REG_DAYLY",dataRegList);
                    }
                    else if(entry.getKey().equals("monthly"))
                    {
                        dataAccessService.batchSave("MDM_DATA_REG_MONTHLY",dataRegList);
                    }


                }

            }


        }

        return rnt;
    }


    //创建指定sdp的数据,只构造60分钟数据，做测试用，其他更小周期不管
    private Map<String,Map<String,Double>> createSdpDataReg(MdmAssetServicePoint sdpPoint,
    String sTv
    )
    {
        Map<String,Map<String,Double>> result = new HashMap<>();

        Map<String,Double> dataMinMap = new LinkedHashMap<>();
        Map<String,Double> dataDayMap = new LinkedHashMap<>();
        Map<String,Double> dataMonthMap = new LinkedHashMap<>();

        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime lstv = LocalDateTime.parse(sTv.replace(" ","T"));

        Instant instant = lstv.atZone(ZoneId.systemDefault()).toInstant();

        Date startTv = Date.from(instant);


        //生成正常数据,先生成60分钟的，日的数据为当天60分钟0点数据，月为1号0点数据

        //分钟数据
        LocalDateTime tmpEndTv = LocalDateTime.now();

        String tmpTv = sTv.replace(" ","T");
        LocalDateTime lStartTv = LocalDateTime.parse(tmpTv);
        String lastTv = "";
        //开始的
        LocalDateTime tmpStartTv = LocalDateTime.parse(tmpTv);

        while (1==1)
        {


            if(tmpStartTv.isAfter(tmpEndTv))
            {

                break;
            }

            double curData = 0;

            if(dataMinMap.size() == 0)
            {
                //生成第一个值
                curData = this.randomStartNumber();

                dataMinMap.put(tmpStartTv.format(timeFormatter), curData);


            }
            else
            {
                curData = this.randomStepNumber();

                //取得最后一个值加上这个值
                double lastData = dataMinMap.get(lastTv);

                curData = lastData + curData;

                DecimalFormat df = new DecimalFormat("#.00");
                String formattedNumber = df.format(curData);
                curData = Double.parseDouble(formattedNumber);


                dataMinMap.put(tmpStartTv.format(timeFormatter), curData);
            }
            lastTv = tmpStartTv.format(timeFormatter);

            //时间累计加60分钟
            tmpStartTv = tmpStartTv.plusMinutes(60);

        }

        //构造日数据
        String tmpymd = lStartTv.getYear() + "-" + String.format("%02d",(lStartTv.getMonth().ordinal()+1)) + "-"+ String.format("%02d",lStartTv.getDayOfMonth());
        LocalDateTime tmpDayStartTv = LocalDateTime.parse(tmpymd + "T00:00:00");

        while (1==1)
        {
            if(tmpDayStartTv.isAfter(tmpEndTv))
            {
                break;
            }

            double tmpdata = dataMinMap.get(tmpDayStartTv.format(timeFormatter));

            dataDayMap.put(tmpDayStartTv.format(timeFormatter), tmpdata);

            tmpDayStartTv = tmpDayStartTv.plusDays(1);

        }

        //构造月数据
        tmpymd = lStartTv.getYear() + "-" + String.format("%02d",(lStartTv.getMonth().ordinal()+1))  + "-01T00:00:00";
        LocalDateTime tmpMonthStartTv = LocalDateTime.parse(tmpymd );

        while (1==1)
        {
            if(tmpMonthStartTv.isAfter(tmpEndTv))
            {
                break;
            }
            if(dataMinMap.keySet().contains(tmpMonthStartTv.format(timeFormatter)))
            {
                double tmpdata = dataMinMap.get(tmpMonthStartTv.format(timeFormatter));

                dataMonthMap.put(tmpMonthStartTv.format(timeFormatter), tmpdata);
            }


            tmpMonthStartTv = tmpMonthStartTv.plusMonths(1);
        }


        result.put("minutely", dataMinMap);
        result.put("dayly", dataDayMap);
        result.put("monthly", dataMonthMap);

        return result;

    }

    private Map<MdmAssetCalcObj,String> buildCalObjData_New(MdmAssetVEERule rule,
                                                        List<MdmAssetCalcObj> calcObjList,
                                                        List<MdmAssetVEERuleDataSource> dataSourceList,
                                                        String sTv,
                                                        Integer errCount,
                                                        List<MdmAssetVEERuleParam> params
    ) throws Exception
    {
        Map<MdmAssetCalcObj,String> rnt = new LinkedHashMap<>();

        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime lstv = LocalDateTime.parse(sTv.replace(" ","T"));

        Instant instant = lstv.atZone(ZoneId.systemDefault()).toInstant();

        Date startTv = Date.from(instant);

        //参数值,默认调小输出
        double pValue = 0;
        for (MdmAssetVEERuleParam p: params
        ) {

            if(p.getParamKey().equals("paramValue2") && p.getParamValue() != null)
            {
                pValue = p.getParamValue();

            }

        }



        //循环每个计算对象
        for (MdmAssetCalcObj calObj:calcObjList
        )
        {
            List<MdmAssetServicePoint> inPoints = new ArrayList<>();

            List<MdmAssetServicePoint> outPoints = new ArrayList<>();

            //获取所有用来做校验的sdp列表，递归获取
            List<MdmAssetServicePoint> spdList = this.getCalObjSdpList(calObj);

            if(spdList.size() == 0)
            {
                continue;
            }

            //删除历史sdp数据
            for (MdmAssetServicePoint sdpPoint:spdList
            ) {
                mdmAssetServicePointRepository.deleteMdmDataRegMinutely(sdpPoint.getId(),startTv);
                mdmAssetServicePointRepository.deleteMdmDataRegDayly(sdpPoint.getId(),startTv);
                mdmAssetServicePointRepository.deleteMdmDataRegMonthly(sdpPoint.getId(),startTv);

                if(sdpPoint.getLineLossInOut() == 1)
                {
                    inPoints.add(sdpPoint);
                }
                else if(sdpPoint.getLineLossInOut() == 2)
                {
                    outPoints.add(sdpPoint);
                }




            }


            //生成一条数据
            Double inTotal = 0d;
            Double outTotal = 0d;
            Map<MdmAssetServicePoint, Double> oneInData = new HashMap<>();
            Map<MdmAssetServicePoint, Double> oneOutData = new HashMap<>();

            for (MdmAssetServicePoint point: inPoints
                 ) {

                Double tmpData = this.randomStartNumber();

                oneInData.put(point,tmpData);

                inTotal = inTotal + tmpData;

            }

            //所有的输出比输入小一点，再平均一下
            outTotal = inTotal - 200;


            while (true)
            {
                //是否满足不超过线损

                if((inTotal-outTotal)/inTotal * 100 >= pValue)
                {
                    outTotal = outTotal + 10;
                }
                else
                {
                    break;
                }
            }

            Double perOutTmpData = outTotal/outPoints.size();

            //精确2位
            DecimalFormat df = new DecimalFormat("#.00");
            String formattedNumber = df.format(perOutTmpData);
            perOutTmpData = Double.parseDouble(formattedNumber);

            //构造出数据
            for (MdmAssetServicePoint point:outPoints
                 ) {

                oneOutData.put(point,perOutTmpData);
            }

            Map<String,Map<MdmAssetServicePoint, Double>> dataRegMinuteMap_in = new LinkedHashMap<>();
            Map<String,Map<MdmAssetServicePoint, Double>> dataRegMinuteMap_out = new LinkedHashMap<>();
            //List<DataReg> dataRegMinList = new ArrayList<>();
            Map<String,Map<MdmAssetServicePoint, Double>> dataRegDayMap_in = new LinkedHashMap<>();
            Map<String,Map<MdmAssetServicePoint, Double>> dataRegDayMap_out = new LinkedHashMap<>();
            //List<DataReg> dataRegMthList = new ArrayList<>();
            Map<String,Map<MdmAssetServicePoint, Double>> dataRegMonthMap_in = new LinkedHashMap<>();
            Map<String,Map<MdmAssetServicePoint, Double>> dataRegMonthMap_out = new LinkedHashMap<>();

            //构造60分钟数据
            LocalDateTime tmpEndTv = LocalDateTime.now();


            String tmpTv = sTv.replace(" ","T");
            LocalDateTime lStartTv = LocalDateTime.parse(tmpTv);
            String lastTv = "";
            //开始的
            LocalDateTime tmpStartTv = LocalDateTime.parse(tmpTv);

            //Map<MdmAssetServicePoint, Double> tmponeInData = oneInData;
            //Map<MdmAssetServicePoint, Double> tmponeOutData = oneOutData;
            while (1==1)
            {


                if(tmpStartTv.isAfter(tmpEndTv))
                {
                    break;
                }
                Map<MdmAssetServicePoint, Double> copiedInMap = new HashMap<>();
                copiedInMap.putAll(oneInData);

                Map<MdmAssetServicePoint, Double> copiedOutMap = new HashMap<>();
                copiedOutMap.putAll(oneOutData);

                //保存日期，sdpllist值
                dataRegMinuteMap_in.put(tmpStartTv.format(timeFormatter), copiedInMap);
                dataRegMinuteMap_out.put(tmpStartTv.format(timeFormatter), copiedOutMap);
                //随机步长倍数
                Double stepRandom = 0.0001;

                //输入端共增加电量

                Double inSum =0d;



                //针对in里面所有的sdp构造数据

                for (Map.Entry<MdmAssetServicePoint,Double> oneSpdDay: oneInData.entrySet()
                ) {

                    double value = oneSpdDay.getValue();


                    oneSpdDay.setValue(value + value * stepRandom);

                }



                //针对out里面所有的sdp构造数据
                for (Map.Entry<MdmAssetServicePoint,Double> oneSpdDay: oneOutData.entrySet()
                ) {

                    //总增加电量平分到所有用于计算的输出节点
                    double value = oneSpdDay.getValue();

                    oneSpdDay.setValue(value + value * stepRandom);

                }
                //时间累计加60分钟
                tmpStartTv = tmpStartTv.plusMinutes(60);

            }

            //日数据
            String tmpymd = lStartTv.getYear() + "-" + String.format("%02d",(lStartTv.getMonth().ordinal()+1)) + "-"+ String.format("%02d",lStartTv.getDayOfMonth());
            LocalDateTime tmpDayStartTv = LocalDateTime.parse(tmpymd + "T00:00:00");

            Map<String, Double> lossRateMap =new LinkedHashMap<>();
            int testIndex = 0;

            String lastDayKey = "";
            while (1==1)
            {

                if(tmpDayStartTv.isAfter(tmpEndTv))
                {
                    break;
                }

                //取当前时间的所有sdp值
                String key = tmpDayStartTv.format(timeFormatter);
                Map<MdmAssetServicePoint,Double> oneDaySpd = dataRegMinuteMap_in.get(key);



                dataRegDayMap_in.put(key, oneDaySpd);

                Map<MdmAssetServicePoint,Double> oneDaySpd1 = dataRegMinuteMap_out.get(key);
                dataRegDayMap_out.put(key, oneDaySpd1);


                /***************************************************************/
                //昨天的值
                Map<MdmAssetServicePoint,Double> lastOneDaySdp = null;
                Map<MdmAssetServicePoint,Double> lastOneDaySdp1 = null;

                //test每天线损,用电量值计算，暂时不管ctpt
                Double inSum = 0d;

                Double outSum = 0d;
                ArrayList<MdmAssetServicePoint> sdpKeys = new ArrayList<>(oneDaySpd.keySet());
                ArrayList<MdmAssetServicePoint> sdpKeys1 = new ArrayList<>(oneDaySpd1.keySet());
                if(testIndex != 0)
                {
                    lastOneDaySdp = dataRegMinuteMap_in.get(lastDayKey);
                    lastOneDaySdp1 = dataRegMinuteMap_out.get(lastDayKey);
                    for (MdmAssetServicePoint p: sdpKeys
                    ) {

                        //今天-昨天

                        inSum = inSum + (oneDaySpd.get(p)-lastOneDaySdp.get(p));




                    }
                    for (MdmAssetServicePoint p: sdpKeys1
                    ) {

                        //今天-昨天

                        outSum = outSum + (oneDaySpd1.get(p)-lastOneDaySdp1.get(p));




                    }

                    Double lossRate = ((inSum-outSum)/inSum)*100;
                    lossRateMap.put(lastDayKey,lossRate);

                }


                /*******************************************************/

                //加一天
                lastDayKey = tmpDayStartTv.format(timeFormatter);

                testIndex++;

                tmpDayStartTv = tmpDayStartTv.plusDays(1);

            }

            //月数据
            tmpymd = lStartTv.getYear() + "-" + String.format("%02d",(lStartTv.getMonth().ordinal()+1))  + "-01T00:00:00";
            LocalDateTime tmpMonthStartTv = LocalDateTime.parse(tmpymd );

            while (1==1)
            {

                if(tmpMonthStartTv.isAfter(tmpEndTv))
                {
                    break;
                }

                //取当前时间的所有sdp值
                String key = tmpMonthStartTv.format(timeFormatter);
                Map<MdmAssetServicePoint,Double> oneDaySpd = dataRegMinuteMap_in.get(key);

                if(oneDaySpd != null)
                {
                    dataRegMonthMap_in.put(key, oneDaySpd);

                    Map<MdmAssetServicePoint,Double> oneDaySpd1 = dataRegMinuteMap_out.get(key);
                    dataRegMonthMap_out.put(key, oneDaySpd1);
                }


                //加一月
                tmpMonthStartTv = tmpMonthStartTv.plusMonths(1);

            }

            //看当前计算对象配置需要计算60分钟、日、月

            String schemeId = calObj.getSchemeIds();

            String [] schemeIds = schemeId.split(",");

            for (String sid: schemeIds
            ) {

                String errDate = "";

                if(sid.equals(""))
                {
                    continue;
                }

                Map<String,Map<MdmAssetServicePoint,Double>> dataRegMap_in = new HashMap<>();
                Map<String,Map<MdmAssetServicePoint,Double>>  dataRegMap_out = new HashMap<>();
                if(sid.equals("6"))//60分钟
                {
                    dataRegMap_in = dataRegMinuteMap_in;
                    dataRegMap_out = dataRegMinuteMap_out;


                }
                else if(sid.equals("7"))//日
                {
                    dataRegMap_in = dataRegDayMap_in;
                    dataRegMap_out = dataRegDayMap_out;
                }
                else if(sid.equals("8"))//月
                {
                    dataRegMap_in = dataRegMonthMap_in;
                    dataRegMap_out = dataRegMonthMap_out;
                }

                //随机生成几个点
                List<Integer> errIndexList = new ArrayList<>();

                int startIndex = 0;

                ArrayList<String> keys = new ArrayList<>(dataRegMap_in.keySet()) ;
                int endIndex = keys.size()-2;

                //不够数
                if(keys.size() <= errCount)
                {
                    errCount = keys.size();
                }
                while (1==1)
                {
                    if(errIndexList.size() == errCount)
                    {

                        break;
                    }

                    int tmpIndex = this.randomInteger(startIndex, endIndex);

                    if(errIndexList.indexOf(tmpIndex) >=0)
                    {
                        continue;
                    }
                    else
                    {
                        errIndexList.add(tmpIndex);
                    }
                }



                for (Integer currIndex:errIndexList
                ) {

                    //日期
                    String key = keys.get(currIndex);

                    //明天
                    String nextKey = keys.get(currIndex + 1);

                    //今天线损等于明天0点-今天0点的值



                    errDate = errDate + key + ",";

                    //sdp值,入和出
                    Map<MdmAssetServicePoint, Double> inSdpData = dataRegMap_in.get(key);

                    Map<MdmAssetServicePoint, Double> outSdpData = dataRegMap_out.get(key);

                    Map<MdmAssetServicePoint, Double> inSdpData_next = dataRegMap_in.get(nextKey);

                    Map<MdmAssetServicePoint, Double> outSdpData_next = dataRegMap_out.get(nextKey);

                    //随便调整一个出值，使损耗变大

                    double inSum = 0;

                    double outSum = 0;

                    ArrayList<MdmAssetServicePoint> inKeys = new ArrayList<>(inSdpData.keySet());
                    ArrayList<MdmAssetServicePoint> outKeys = new ArrayList<>(outSdpData.keySet());

                    //总入电能量
                    for (MdmAssetServicePoint p:inKeys
                    ) {

                        double pt = 1d;

                        Double ptValue = getScaleValue("1101", p.getPt());
                        if(ptValue != null) {
                            pt = ptValue.doubleValue();
                        }

                        double ct = 1d;
                        Double ctValue = getScaleValue("1102", p.getPt());
                        if(ctValue != null) {
                            ct = ctValue.doubleValue();
                        }

                        //不考虑换表
                        inSum = inSum + inSdpData_next.get(p)*ct*pt-inSdpData.get(p)*ct*pt;

                    }

                    while (1==1)
                    {
                        outSum = 0;
                        for (MdmAssetServicePoint p:outKeys
                        ) {

                            double pt = 1d;

                            Double ptValue = getScaleValue("1101", p.getPt());
                            if(ptValue != null) {
                                pt = ptValue.doubleValue();
                            }

                            double ct = 1d;
                            Double ctValue = getScaleValue("1102", p.getPt());
                            if(ctValue != null) {
                                ct = ctValue.doubleValue();
                            }

                            outSum = outSum + outSdpData_next.get(p)*ct*pt-outSdpData.get(p)*ct*pt;

                        }

                        double lossRate = ((inSum-outSum)/inSum)*100;

                        //保留两位小数
                        DecimalFormat df1 = new DecimalFormat("#.00");
                        String formattedNumber1 = df1.format(lossRate);
                        lossRate = Double.parseDouble(formattedNumber1);

                        if(lossRate > pValue)
                        {
                            break;
                        }

                        //否则整体调小输出值
                        double setp = 0.01;
                        for (Map.Entry<MdmAssetServicePoint, Double> entry:outSdpData.entrySet()
                        )
                        {
                            Double tmpvalue = entry.getValue();

                            tmpvalue = tmpvalue+setp;

                            entry.setValue(tmpvalue);
                        }

                    }


                }

                //构造错误信息
                errDate =  errDate.substring(0,errDate.length()-2);
                String lastErrData = rnt.get(calObj);

                if(lastErrData == null)
                {
                    lastErrData = "";
                }
                if(sid.equals("6"))
                {
                    errDate ="分钟(" + errDate + ")";
                }
                else if(sid.equals("7"))
                {
                    errDate = "日(" + errDate + ")";
                }
                else if(sid.equals("8"))
                {
                    errDate = "月(" + errDate + ")";
                }

                lastErrData = lastErrData + errDate;

                rnt.put(calObj,lastErrData );



            }

            //开始保存数据
            this.saveLossRateData(dataRegMinuteMap_in,
                    dataRegMinuteMap_out,
                    dataRegDayMap_in,
                    dataRegDayMap_out,
                    dataRegMonthMap_in,
                    dataRegMonthMap_out);


        }



            return rnt;
    }
    private Double getScaleValue(String dictId, int innerValue) {
        try {
            DictDetail dictDetail = dictDetailRepository.findByDictIdAndInnerValue(dictId, innerValue);
            if(dictDetail == null) {
                return null;
            }
            String name =  dictDetail.getGuiDisplayName();
            if (name != null) {
                String[] splitName = name.split("/");
                if (splitName.length == 2) {
                    return Double.parseDouble(splitName[0]) / Double.parseDouble(splitName[1]);
                }
            }
        } catch (Exception e) {

        }
        return null;
    }


    private Map<MdmAssetCalcObj,String> buildCalObjData(MdmAssetVEERule rule,
                                 List<MdmAssetCalcObj> calcObjList,
                                 List<MdmAssetVEERuleDataSource> dataSourceList,
                                 String sTv,
                                 Integer errCount,
                                 List<MdmAssetVEERuleParam> params
                                 ) throws Exception {

        Map<MdmAssetCalcObj,String> rnt = new LinkedHashMap<>();

        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime lstv = LocalDateTime.parse(sTv.replace(" ","T"));

        Instant instant = lstv.atZone(ZoneId.systemDefault()).toInstant();

        Date startTv = Date.from(instant);

        //参数值,默认调小输出
        double pValue = 0;
        for (MdmAssetVEERuleParam p: params
        ) {

            if(p.getParamKey().equals("paramValue2") && p.getParamValue() != null)
            {
                pValue = p.getParamValue();

            }

        }

        //循环每个计算对象
        for (MdmAssetCalcObj calObj:calcObjList
        ) {

            //获取所有用来做校验的sdp列表，递归获取
            List<MdmAssetServicePoint> spdList = this.getCalObjSdpList(calObj);


            //删除历史sdp数据
            for (MdmAssetServicePoint sdpPoint:spdList
                 ) {
                mdmAssetServicePointRepository.deleteMdmDataRegMinutely(sdpPoint.getId(),startTv);
                mdmAssetServicePointRepository.deleteMdmDataRegDayly(sdpPoint.getId(),startTv);
                mdmAssetServicePointRepository.deleteMdmDataRegMonthly(sdpPoint.getId(),startTv);


            }


            //一个初始值

            //key是sdp的id，值是生成的值
            Map<MdmAssetServicePoint, Double> oneInData = new HashMap<>();
            //生成in数据，in数据可以随意构造（当前计算对象输入端的所有sdp，其中有子输入）
            Double leftSum = 0d;
            leftSum = this.createCalObjInData(calObj,oneInData);
            //用这个值限制所有输出的值，应当所有输出和要小于这个输入总值

            Map<MdmAssetServicePoint, Double> oneOutData = new HashMap<>();
            Double rightSum = 0d;
            //生成out数据,根据输入端所有sdp合计值，限制生成输出端所有sdp值，可以平均分配到所有输出端去
            rightSum = this.createCalObjOutData(calObj,oneOutData,leftSum);


            //List<DataReg> dataRegSecList = new ArrayList<>();
            //可以是日期，数据是当天所有sdp的数据
            Map<String,Map<MdmAssetServicePoint, Double>> dataRegMinuteMap_in = new LinkedHashMap<>();
            Map<String,Map<MdmAssetServicePoint, Double>> dataRegMinuteMap_out = new LinkedHashMap<>();
            //List<DataReg> dataRegMinList = new ArrayList<>();
            Map<String,Map<MdmAssetServicePoint, Double>> dataRegDayMap_in = new LinkedHashMap<>();
            Map<String,Map<MdmAssetServicePoint, Double>> dataRegDayMap_out = new LinkedHashMap<>();
            //List<DataReg> dataRegMthList = new ArrayList<>();
            Map<String,Map<MdmAssetServicePoint, Double>> dataRegMonthMap_in = new LinkedHashMap<>();
            Map<String,Map<MdmAssetServicePoint, Double>> dataRegMonthMap_out = new LinkedHashMap<>();




            //用生成的数据作为起始数据，以后的数据是上一个周期数据加上一个随机步长

            //60分钟数据
            LocalDateTime tmpEndTv = LocalDateTime.now();


            String tmpTv = sTv.replace(" ","T");
            LocalDateTime lStartTv = LocalDateTime.parse(tmpTv);
            String lastTv = "";
            //开始的
            LocalDateTime tmpStartTv = LocalDateTime.parse(tmpTv);

            //Map<MdmAssetServicePoint, Double> tmponeInData = oneInData;
            //Map<MdmAssetServicePoint, Double> tmponeOutData = oneOutData;
            while (1==1)
            {


                if(tmpStartTv.isAfter(tmpEndTv))
                {
                    break;
                }
                Map<MdmAssetServicePoint, Double> copiedInMap = new HashMap<>();
                copiedInMap.putAll(oneInData);

                Map<MdmAssetServicePoint, Double> copiedOutMap = new HashMap<>();
                copiedOutMap.putAll(oneOutData);

                //保存日期，sdpllist值
                dataRegMinuteMap_in.put(tmpStartTv.format(timeFormatter), copiedInMap);
                dataRegMinuteMap_out.put(tmpStartTv.format(timeFormatter), copiedOutMap);
                //随机步长倍数
                Double stepRandom = 0.0001;

                //输入端共增加电量

                Double inSum =0d;



                //针对in里面所有的sdp构造数据

                for (Map.Entry<MdmAssetServicePoint,Double> oneSpdDay: oneInData.entrySet()
                ) {

                    double value = oneSpdDay.getValue();


                    oneSpdDay.setValue(value + value * stepRandom);

                }



                //针对out里面所有的sdp构造数据
                for (Map.Entry<MdmAssetServicePoint,Double> oneSpdDay: oneOutData.entrySet()
                ) {

                    //总增加电量平分到所有用于计算的输出节点
                    double value = oneSpdDay.getValue();

                    oneSpdDay.setValue(value + value * stepRandom);

                }
                //时间累计加60分钟
                tmpStartTv = tmpStartTv.plusMinutes(60);

            }

            //日数据
            String tmpymd = lStartTv.getYear() + "-" + String.format("%02d",(lStartTv.getMonth().ordinal()+1)) + "-"+ String.format("%02d",lStartTv.getDayOfMonth());
            LocalDateTime tmpDayStartTv = LocalDateTime.parse(tmpymd + "T00:00:00");

            Map<String, Double> lossRateMap =new LinkedHashMap<>();
            int testIndex = 0;

            String lastDayKey = "";
            while (1==1)
            {

                if(tmpDayStartTv.isAfter(tmpEndTv))
                {
                    break;
                }

                //取当前时间的所有sdp值
                String key = tmpDayStartTv.format(timeFormatter);
                Map<MdmAssetServicePoint,Double> oneDaySpd = dataRegMinuteMap_in.get(key);



                dataRegDayMap_in.put(key, oneDaySpd);

                Map<MdmAssetServicePoint,Double> oneDaySpd1 = dataRegMinuteMap_out.get(key);
                dataRegDayMap_out.put(key, oneDaySpd1);


                /***************************************************************/
                //昨天的值
                Map<MdmAssetServicePoint,Double> lastOneDaySdp = null;
                Map<MdmAssetServicePoint,Double> lastOneDaySdp1 = null;

                //test每天线损,用电量值计算，暂时不管ctpt
                Double inSum = 0d;

                Double outSum = 0d;
                ArrayList<MdmAssetServicePoint> sdpKeys = new ArrayList<>(oneDaySpd.keySet());
                ArrayList<MdmAssetServicePoint> sdpKeys1 = new ArrayList<>(oneDaySpd1.keySet());
                if(testIndex != 0)
                {
                    lastOneDaySdp = dataRegMinuteMap_in.get(lastDayKey);
                    lastOneDaySdp1 = dataRegMinuteMap_out.get(lastDayKey);
                    for (MdmAssetServicePoint p: sdpKeys
                         ) {

                        //今天-昨天
                        if(p.isCalcFlag())
                        {
                            inSum = inSum + (oneDaySpd.get(p)-lastOneDaySdp.get(p));
                        }



                    }
                    for (MdmAssetServicePoint p: sdpKeys1
                    ) {

                        //今天-昨天
                        if(p.isCalcFlag())
                        {
                            outSum = outSum + (oneDaySpd1.get(p)-lastOneDaySdp1.get(p));
                        }



                    }

                    Double lossRate = ((inSum-outSum)/inSum)*100;
                    lossRateMap.put(lastDayKey,lossRate);

                }
                /*
                for (Map.Entry<MdmAssetServicePoint, Double> entry:oneDaySpd.entrySet()
                ) {

                    if(entry.getKey().isCalcFlag()) {
                        inSum = inSum + entry.getValue();
                    }
                }


                for (Map.Entry<MdmAssetServicePoint, Double> entry:oneDaySpd1.entrySet()
                ) {

                    if(entry.getKey().isCalcFlag()) {
                        outSum = outSum + entry.getValue();
                    }
                }

                Double lossRate = ((inSum-outSum)/inSum)*100;
                lossRateList.add(lossRate);

                 */



                /*******************************************************/

                //加一天
                lastDayKey = tmpDayStartTv.format(timeFormatter);

                testIndex++;

                tmpDayStartTv = tmpDayStartTv.plusDays(1);

            }

            //月数据
            tmpymd = lStartTv.getYear() + "-" + String.format("%02d",(lStartTv.getMonth().ordinal()+1))  + "-01T00:00:00";
            LocalDateTime tmpMonthStartTv = LocalDateTime.parse(tmpymd );

            while (1==1)
            {

                if(tmpMonthStartTv.isAfter(tmpEndTv))
                {
                    break;
                }

                //取当前时间的所有sdp值
                String key = tmpMonthStartTv.format(timeFormatter);
                Map<MdmAssetServicePoint,Double> oneDaySpd = dataRegMinuteMap_in.get(key);

                if(oneDaySpd != null)
                {
                    dataRegMonthMap_in.put(key, oneDaySpd);

                    Map<MdmAssetServicePoint,Double> oneDaySpd1 = dataRegMinuteMap_out.get(key);
                    dataRegMonthMap_out.put(key, oneDaySpd1);
                }






                //加一月
                tmpMonthStartTv = tmpMonthStartTv.plusMonths(1);

            }





            //看当前计算对象配置需要计算60分钟、日、月

            String schemeId = calObj.getSchemeIds();

            String [] schemeIds = schemeId.split(",");

            for (String sid: schemeIds
                 ) {

                String errDate = "";

                if(sid.equals(""))
                {
                    continue;
                }

                Map<String,Map<MdmAssetServicePoint,Double>> dataRegMap_in = new HashMap<>();
                Map<String,Map<MdmAssetServicePoint,Double>>  dataRegMap_out = new HashMap<>();
                if(sid.equals("6"))//60分钟
                {
                    dataRegMap_in = dataRegMinuteMap_in;
                    dataRegMap_out = dataRegMinuteMap_out;


                }
                else if(sid.equals("7"))//日
                {
                    dataRegMap_in = dataRegDayMap_in;
                    dataRegMap_out = dataRegDayMap_out;
                }
                else if(sid.equals("8"))//月
                {
                    dataRegMap_in = dataRegMonthMap_in;
                    dataRegMap_out = dataRegMonthMap_out;
                }

                //随机生成几个点
                List<Integer> errIndexList = new ArrayList<>();

                int startIndex = 0;

                ArrayList<String> keys = new ArrayList<>(dataRegMap_in.keySet()) ;
                int endIndex = keys.size()-2;

                //不够数
                if(keys.size() <= errCount)
                {
                    errCount = keys.size();
                }
                while (1==1)
                {
                    if(errIndexList.size() == errCount)
                    {

                        break;
                    }

                    int tmpIndex = this.randomInteger(startIndex, endIndex);

                    if(errIndexList.indexOf(tmpIndex) >=0)
                    {
                        continue;
                    }
                    else
                    {
                        errIndexList.add(tmpIndex);
                    }
                }



                for (Integer currIndex:errIndexList
                ) {

                    //日期
                    String key = keys.get(currIndex);

                    //明天
                    String nextKey = keys.get(currIndex + 1);

                    //今天线损等于明天0点-今天0点的值



                    errDate = errDate + key + ",";

                    //sdp值,入和出
                    Map<MdmAssetServicePoint, Double> inSdpData = dataRegMap_in.get(key);

                    Map<MdmAssetServicePoint, Double> outSdpData = dataRegMap_out.get(key);

                    Map<MdmAssetServicePoint, Double> inSdpData_next = dataRegMap_in.get(nextKey);

                    Map<MdmAssetServicePoint, Double> outSdpData_next = dataRegMap_out.get(nextKey);

                    //随便调整一个出值，使损耗变大

                    double inSum = 0;

                    double outSum = 0;

                    ArrayList<MdmAssetServicePoint> inKeys = new ArrayList<>(inSdpData.keySet());
                    ArrayList<MdmAssetServicePoint> outKeys = new ArrayList<>(outSdpData.keySet());

                    //总入电能量
                    for (MdmAssetServicePoint p:inKeys
                         ) {
                        if(p.isCalcFlag())
                        {
                            inSum = inSum + inSdpData_next.get(p)-inSdpData.get(p);
                        }
                    }

                    while (1==1)
                    {
                        outSum = 0;
                        for (MdmAssetServicePoint p:outKeys
                        ) {
                            if(p.isCalcFlag())
                            {
                                outSum = outSum + outSdpData_next.get(p)-outSdpData.get(p);
                            }
                        }

                        double lossRate = ((inSum-outSum)/inSum)*100;

                        //保留两位小数
                        DecimalFormat df = new DecimalFormat("#.00");
                        String formattedNumber = df.format(lossRate);
                        lossRate = Double.parseDouble(formattedNumber);

                        if(lossRate > pValue)
                        {
                            break;
                        }

                        //否则整体调小输出值
                        double setp = this.randomNumber(0.1,0.5);
                        for (Map.Entry<MdmAssetServicePoint, Double> entry:outSdpData.entrySet()
                        )
                        {
                            Double tmpvalue = entry.getValue();

                            tmpvalue = tmpvalue+setp;

                            entry.setValue(tmpvalue);
                        }

                    }


                }

                //构造错误信息
                errDate =  errDate.substring(0,errDate.length()-2);
                String lastErrData = rnt.get(calObj);

                if(lastErrData == null)
                {
                    lastErrData = "";
                }
                if(sid.equals("6"))
                {
                    errDate ="分钟(" + errDate + ")";
                }
                else if(sid.equals("7"))
                {
                    errDate = "日(" + errDate + ")";
                }
                else if(sid.equals("8"))
                {
                    errDate = "月(" + errDate + ")";
                }

                lastErrData = lastErrData + errDate;

                rnt.put(calObj,lastErrData );



            }

            //开始保存数据
            this.saveLossRateData(dataRegMinuteMap_in,
                    dataRegMinuteMap_out,
                    dataRegDayMap_in,
                    dataRegDayMap_out,
                    dataRegMonthMap_in,
                    dataRegMonthMap_out);



        }


        return rnt;
    }

    //分钟、日、月都保存
    private void saveLossRateData(Map<String,Map<MdmAssetServicePoint, Double>> dataRegMinuteMap_in,
                                  Map<String,Map<MdmAssetServicePoint, Double>> dataRegMinuteMap_out,
                                  Map<String,Map<MdmAssetServicePoint, Double>> dataRegDayMap_in,
                                  Map<String,Map<MdmAssetServicePoint, Double>> dataRegDayMap_out,
                                  Map<String,Map<MdmAssetServicePoint, Double>> dataRegMonthMap_in,
                                  Map<String,Map<MdmAssetServicePoint, Double>> dataRegMonthMap_out
                                  ) throws Exception {


        List<DataReg> minRegDataList_in = new ArrayList<>();
        List<DataReg> minRegDataList_out = new ArrayList<>();
        List<DataReg> dayRegDataList_in = new ArrayList<>();
        List<DataReg> dayRegDataList_out = new ArrayList<>();
        List<DataReg> monthRegDataList_in = new ArrayList<>();
        List<DataReg> monthRegDataList_out = new ArrayList<>();

        minRegDataList_in = this.createDataRegList(dataRegMinuteMap_in);
        minRegDataList_out = this.createDataRegList(dataRegMinuteMap_out);

       dayRegDataList_in = this.createDataRegList(dataRegDayMap_in);
       dayRegDataList_out = this.createDataRegList(dataRegDayMap_out);

        monthRegDataList_in = this.createDataRegList(dataRegMonthMap_in);
        monthRegDataList_out = this.createDataRegList(dataRegMonthMap_out);

        dataAccessService.batchSave("MDM_DATA_REG_MINUTELY",minRegDataList_in);
        dataAccessService.batchSave("MDM_DATA_REG_MINUTELY",minRegDataList_out);

        dataAccessService.batchSave("MDM_DATA_REG_DAYLY",dayRegDataList_in);
        dataAccessService.batchSave("MDM_DATA_REG_DAYLY",dayRegDataList_out);

        dataAccessService.batchSave("MDM_DATA_REG_MONTHLY",monthRegDataList_in);
        dataAccessService.batchSave("MDM_DATA_REG_MONTHLY",monthRegDataList_out);





    }

    private List<DataReg> createDataRegList(Map<String,Map<MdmAssetServicePoint, Double>> dataRegMinuteMap) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        List<DataReg> res = new ArrayList<>();

        for (Map.Entry<String,Map<MdmAssetServicePoint,Double>>  entryDay: dataRegMinuteMap.entrySet()
        ) {
            Date tv = simpleDateFormat.parse(entryDay.getKey());
            for (Map.Entry<MdmAssetServicePoint,Double> entrySdp :entryDay.getValue().entrySet()
            ) {
                DataReg oneData = new DataReg();

                String sdpId = entrySdp.getKey().getId();

                double tmpdata = entrySdp.getValue();

                oneData.getDataRegPK().setTv(tv);
                oneData.getDataRegPK().setSdpId(sdpId);
                oneData.setR0P1(tmpdata);

                res.add(oneData);
            }


        }

        return res;
    }

    //至上而下的构造
    private Double createCalObjInData(MdmAssetCalcObj calcObj,Map<MdmAssetServicePoint, Double> mapData)
    {

        Double sum = 0d;
        //获取所有输入对象
        List<MdmAssetCalcObjMap> mdmAssetCalcObjMaps = mdmAssetCalcObjMapRepository.findAllByIdAndType(calcObj.getId(),1);

        //循环输入端对象
        for (MdmAssetCalcObjMap oneItem:mdmAssetCalcObjMaps
             ) {

            //判断是否是sdp，如果是sdp则直接把sdpid作为key，生成值作为值
            if(oneItem.getMeteringType() == 1)
            {
                //随机生成数
                Double tmpData = this.randomStartNumber();

                sum = sum + tmpData;

                MdmAssetServicePoint sdpPoint = mdmAssetServicePointRepository.findById(oneItem.getMeteringId()).get();
                sdpPoint.setCalcFlag(true);

                mapData.put(sdpPoint,tmpData);
            }
            else
            {
                //子计算对象，继续递归
                MdmAssetCalcObj subCalcObj = mdmAssetCalcObjRepository.findById(oneItem.getMeteringId()).get();


                double tmpsum =  this.createSubCalObjInData(subCalcObj,mapData);

                sum = sum + tmpsum;

            }
        }

        return sum;
    }


    private double createSubCalObjInData(MdmAssetCalcObj calcObj, Map<MdmAssetServicePoint,Double> results )
    {
        //查询当前计算对象子对象,包括入、出的sdp

        double sum = 0;

        //查询所有入的，然后整体出的节点等于或者小于入的节点值

        List<MdmAssetCalcObjMap> mdmAssetCalcObjMaps_in = mdmAssetCalcObjMapRepository.findAllByIdAndType(calcObj.getId(),1);

        for (MdmAssetCalcObjMap m:mdmAssetCalcObjMaps_in
             ) {

            if(m.getMeteringType() == 1)//sdp
            {
                Double tmpData = this.randomStartNumber();
                sum = sum + tmpData;

                MdmAssetServicePoint sdpPoint = mdmAssetServicePointRepository.findById(m.getMeteringId()).get();
                sdpPoint.setCalcFlag(true);

                results.put(sdpPoint,tmpData);

            }
            else
            {
                MdmAssetCalcObj subCalcObj = mdmAssetCalcObjRepository.findById(m.getMeteringId()).get();
                Double tmpData = this.createSubCalObjInData(subCalcObj, results);

                sum = sum + tmpData;



            }
        }


        List<MdmAssetCalcObjMap> mdmAssetCalcObjMaps_out = mdmAssetCalcObjMapRepository.findAllByIdAndType(calcObj.getId(),2);

        //平均分配到所有出的点里去，不管是sdp还是计算对象
        //理论上作为子计算对象，只存在一个输出，并连接到父节点
        Double perOutData = (sum-this.randomNumber(1,10))/mdmAssetCalcObjMaps_out.size();

        //精确两位小数
        DecimalFormat df = new DecimalFormat("#.00");
        String formattedNumber = df.format(perOutData);
        perOutData = Double.parseDouble(formattedNumber);



        //出的总值，要小于等于所有入的总值
        for (MdmAssetCalcObjMap m:mdmAssetCalcObjMaps_out
             ) {

            if(m.getMeteringType() == 1)//sdp
            {
                MdmAssetServicePoint sdpPoint = mdmAssetServicePointRepository.findById(m.getMeteringId()).get();

                results.put(sdpPoint,perOutData);

            }
            else//计算对象
            {
                MdmAssetCalcObj subCalcObj = mdmAssetCalcObjRepository.findById(m.getMeteringId()).get();
                this.createSubCalObjInData(subCalcObj, results);

            }
        }


        return sum;


    }


    private Double createCalObjOutData(MdmAssetCalcObj calcObj, Map<MdmAssetServicePoint, Double> results , Double leftSum)
    {
        Double sum = 0d;

        //获取所有输出
        List<MdmAssetCalcObjMap> mdmAssetCalcObjMaps = mdmAssetCalcObjMapRepository.findAllByIdAndType(calcObj.getId(),2);


        if(mdmAssetCalcObjMaps != null && mdmAssetCalcObjMaps.size() > 0)
        {
            sum = leftSum - this.randomNumber(100,200);
            //平均分配输出,比参照值小一点
            Double perOutData = sum/mdmAssetCalcObjMaps.size();

            //精确2位
            DecimalFormat df = new DecimalFormat("#.00");
            String formattedNumber = df.format(perOutData);
            perOutData = Double.parseDouble(formattedNumber);

            for (MdmAssetCalcObjMap oneItem: mdmAssetCalcObjMaps
                 ) {

                if(oneItem.getMeteringType() == 1)//sdp
                {

                    //直接赋值
                    MdmAssetServicePoint sdpPoint = mdmAssetServicePointRepository.findById(oneItem.getMeteringId()).get();
                    sdpPoint.setCalcFlag(true);

                    results.put(sdpPoint, perOutData);
                }
                else if(oneItem.getMeteringType() == 2)
                {
                    //递归计量点
                    MdmAssetCalcObj subCalcObj = mdmAssetCalcObjRepository.findById(oneItem.getMeteringId()).get();
                    this.createSubCalObjOutData(subCalcObj,results,perOutData);

                }
            }
        }

        return sum;
    }
    private void createSubCalObjOutData(MdmAssetCalcObj calcObj, Map<MdmAssetServicePoint, Double> results , Double leftSum)
    {
        //获取当前计量点的所以要输入，平均当前参考值到输入值，理论上只存在一个输入，可以多个输出
        List<MdmAssetCalcObjMap> mdmAssetCalcObjMaps = mdmAssetCalcObjMapRepository.findAllByIdAndType(calcObj.getId(),1);
        //比输入值小一点
        double allOutSum = leftSum-this.randomNumber(0.1,0.5);


        double perInData = leftSum/mdmAssetCalcObjMaps.size();

        for (MdmAssetCalcObjMap oneItem:mdmAssetCalcObjMaps
             ) {

            if(oneItem.getMeteringType() == 1)//sdp
            {
                MdmAssetServicePoint sdpPoint = mdmAssetServicePointRepository.findById(oneItem.getMeteringId()).get();
                results.put(sdpPoint, perInData);
            }
            else
            {
                //递归
                MdmAssetCalcObj subCalcObj = mdmAssetCalcObjRepository.findById(oneItem.getMeteringId()).get();
                this.createSubCalObjOutData(subCalcObj,results,perInData);
            }
        }
        //获取所有输出
        List<MdmAssetCalcObjMap> mdmAssetCalcObjMaps1 = mdmAssetCalcObjMapRepository.findAllByIdAndType(calcObj.getId(),2);

        //平衡输出
        double perOutData = allOutSum/mdmAssetCalcObjMaps1.size();
        for (MdmAssetCalcObjMap oneItem:mdmAssetCalcObjMaps1
        ) {

            if (oneItem.getMeteringType() == 1)//sdp
            {
                MdmAssetServicePoint sdpPoint = mdmAssetServicePointRepository.findById(oneItem.getMeteringId()).get();
                sdpPoint.setCalcFlag(true);
                results.put(sdpPoint, perOutData);
            } else {
                //递归
                MdmAssetCalcObj subCalcObj = mdmAssetCalcObjRepository.findById(oneItem.getMeteringId()).get();
                this.createSubCalObjOutData(subCalcObj, results, perOutData);
            }
        }
    }
    //随机生成一个数
    private double randomStartNumber()
    {
       return this.randomNumber(3000,5000);
    }

    private double randomStepNumber()
    {
        return this.randomNumber(0.01,0.05);
    }

    private double randomNumber(double min, double max)
    {
        double rnt = 0;


        int    scl =  2; // 小数最大位数
        int    pow = (int) Math.pow(10, scl); // 用于提取指定小数位
        double sum =  0; // 用于验证总和

        Map<Double, String> map=new HashMap<>();


        while(1==1){
            double one=0 ;
            one = Math.floor((Math.random() * (max - min) + min) * pow) / pow;

            if(one>min
                    && one<max
                    && one>0){

                DecimalFormat df = new DecimalFormat("#.00");
                String formattedNumber = df.format(one);
                rnt = Double.parseDouble(formattedNumber);

                break;
            }
        }


        return rnt;
    }

    private int randomInteger(int min, int max)
    {
        Random random = new Random();

        return random.nextInt(max - min + 1) + min;
    }


    //把一个数划分成指定份数
    private List<Double> divideTotal(Double source, int divideNum)
    {
        //平均N份，保留2位小数，最后一份是总的减去前面n-1份
        List<Double> reslut = new ArrayList<>();

        double tmp = source/divideNum;
        double tmpsum = 0;

        DecimalFormat df = new DecimalFormat("#.00");


        for(int i=1; i <= divideNum; i++)
        {
            if(i!=divideNum)
            {
                String formattedNumber = df.format(tmp);
                Double curData = Double.parseDouble(formattedNumber);
                tmpsum = tmpsum + curData;
                reslut.add(curData);
            }
            else
            {
                Double curData = source - tmpsum;
                reslut.add(curData);
            }

        }

        return reslut;


    }

    //创建缺数
    private List<DataReg> generateMissData(Map<String, Double> dataRegList, 
                                           List<String> errIndexList,
                                           String sdpId)
    {
        List<DataReg> dataList = new ArrayList<>();

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (Map.Entry<String, Double> entry :dataRegList.entrySet()
             ) {

            try {
                double tmpdata = entry.getValue();

                Date tv = simpleDateFormat.parse(entry.getKey());

                DataReg oneData = new DataReg();

                oneData.getDataRegPK().setTv(tv);
                oneData.getDataRegPK().setSdpId(sdpId);
                if(errIndexList.indexOf(entry.getKey()) >= 0)
                {
                    //oneData.setR0P1(0d);
                    continue;
                }
                else
                {
                    oneData.setR0P1(tmpdata);
                }

                dataList.add(oneData);

            }
            catch (Exception ex)
            {
                Log.error(ex);
            }



        }
        
        return dataList;
    }

    //总分不等，总数据与分tou数据不等
    private List<DataReg> generatTotalUnequalTariff(Map<String, Double> dataRegList,
                                                    List<String> errIndexList,
                                                    String sdpId
    ) throws ParseException {
        List<DataReg> dataList = new ArrayList<>();

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (Map.Entry<String, Double> entry :dataRegList.entrySet()
        ) {

            DataReg oneData = new DataReg();

            Date tv = simpleDateFormat.parse(entry.getKey());
            double tmpdata = entry.getValue();

            oneData.getDataRegPK().setTv(tv);
            oneData.getDataRegPK().setSdpId(sdpId);

            if(errIndexList.indexOf(entry.getKey()) >= 0)
            {
                //oneData.setR0P1(0d);
                //continue;
                oneData.setR0P1(tmpdata);
                //随机分成4份,最后一份做错
                oneData.setR0P1A1(tmpdata);

                List<Double> tariffData = this.divideTotal(tmpdata,4);

                oneData.setR1P1A1(tariffData.get(0));
                oneData.setR2P1A1(tariffData.get(1));
                oneData.setR3P1A1(tariffData.get(2));
                oneData.setR4P1A1(tariffData.get(3) + randomInteger(1,10));



            }
            else
            {
                oneData.setR0P1(tmpdata);

                //构造分项数据
                //随机分成4份
                oneData.setR0P1A1(tmpdata);

                List<Double> tariffData = this.divideTotal(tmpdata,4);

                oneData.setR1P1A1(tariffData.get(0));
                oneData.setR2P1A1(tariffData.get(1));
                oneData.setR3P1A1(tariffData.get(2));
                oneData.setR4P1A1(tariffData.get(3));

            }

            dataList.add(oneData);

        }

        return dataList;
    }


    //突然变大，根据配置的
    private List<DataReg> generatAbnormalBigger(Map<String, Double> dataRegList,
                                                List<String> errIndexList,
                                                String sdpId,
                                                MdmAssetVEERuleDataSource veeRuleDataSource,
                                                List<MdmAssetVEERuleParam> params
                                                ) throws ParseException {
        List<DataReg> dataList = new ArrayList<>();

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        Integer schemaeType = veeRuleDataSource.getSchemeType();
        double pValue = 0;
        for (MdmAssetVEERuleParam p: params
             ) {

            if(p.getParamKey().equals("resultValue") && p.getParamValue() != null)
            {
                pValue = p.getParamValue();

            }

        }

        //循环数据
        for (Map.Entry<String, Double> entry :dataRegList.entrySet()
        )
        {
            DataReg oneData = new DataReg();

            Date tv = simpleDateFormat.parse(entry.getKey());
            double tmpdata = entry.getValue();

            oneData.getDataRegPK().setTv(tv);
            oneData.getDataRegPK().setSdpId(sdpId);
            if(errIndexList.indexOf(entry.getKey()) >= 0)
            {
                //插入异常值
                //当前值-上一周期值 比上一周期值-上上周期值 大指定参数

                double errdata = tmpdata;

                LocalDateTime preTime = LocalDateTime.parse(entry.getKey().replace(" ","T"));
                if(schemaeType == 1)
                {
                    preTime = preTime.plusMinutes(-60);
                }
                else if(schemaeType == 2)
                {
                    preTime = preTime.plusDays(-1);
                }
                else if(schemaeType == 3)
                {
                    preTime = preTime.plusMonths(-1);
                }

                String preTimeKey = preTime.format(timeFormatter);

                //上个周期的值
                double predata =dataRegList.get(preTimeKey);

                //上上个周期的值
                LocalDateTime ppreTime = LocalDateTime.parse(entry.getKey().replace(" ","T"));
                if(schemaeType == 1)
                {
                    ppreTime = ppreTime.plusMinutes(-60*2);
                }
                else if(schemaeType == 2)
                {
                    ppreTime = ppreTime.plusDays(-1*2);
                }
                else if(schemaeType == 3)
                {
                    ppreTime = ppreTime.plusMonths(-1*2);
                }

                String ppreTimeKey = ppreTime.format(timeFormatter);

                double ppredata =dataRegList.get(ppreTimeKey);


                while (1==1)
                {
                    double diff2 = errdata-predata;
                    double diff1 = predata - ppredata;

                    if(diff2 > diff1 * pValue)
                    {

                        //保存数据
                        oneData.setR0P1(errdata);
                        //变更原始值
                        entry.setValue(errdata);
                        break;

                    }
                    else
                    {
                        errdata = errdata + this.randomNumber(0.5,1);

                    }
                }

            }
            else
            {
                //正常插入值
                oneData.setR0P1(tmpdata);
            }
            dataList.add(oneData);
        }
        return dataList;



    }

    //突然变小
    private List<DataReg> generatAbnormalSmaller(Map<String, Double> dataRegList,
                                                List<String> errIndexList,
                                                String sdpId,
                                                 MdmAssetVEERuleDataSource veeRuleDataSource,
                                                List<MdmAssetVEERuleParam> params
    ) throws ParseException {
        List<DataReg> dataList = new ArrayList<>();
        Integer schemaeType = veeRuleDataSource.getSchemeType();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        double pValue = 0;
        for (MdmAssetVEERuleParam p: params
        ) {

            if(p.getParamKey().equals("resultValue") && p.getParamValue() != null)
            {
                pValue = p.getParamValue();

            }

        }

        //循环数据
        for (Map.Entry<String, Double> entry :dataRegList.entrySet()
        )
        {
            DataReg oneData = new DataReg();

            Date tv = simpleDateFormat.parse(entry.getKey());
            double tmpdata = entry.getValue();

            oneData.getDataRegPK().setTv(tv);
            oneData.getDataRegPK().setSdpId(sdpId);
            if(errIndexList.indexOf(entry.getKey()) >= 0)
            {
                //插入异常值
                //当前值-上一周期值 比上一周期值-上上周期值 大指定参数

                double errdata = tmpdata;

                LocalDateTime preTime = LocalDateTime.parse(entry.getKey().replace(" ","T"));
                if(schemaeType == 1)
                {
                    preTime = preTime.plusMinutes(-60);
                }
                else if(schemaeType == 2)
                {
                    preTime = preTime.plusDays(-1);
                }
                else if(schemaeType == 3)
                {
                    preTime = preTime.plusMonths(-1);
                }

                String preTimeKey = preTime.format(timeFormatter);

                //上个周期的值
                double predata =dataRegList.get(preTimeKey);

                //上上个周期的值
                LocalDateTime ppreTime = LocalDateTime.parse(entry.getKey().replace(" ","T"));
                if(schemaeType == 1)
                {
                    ppreTime = ppreTime.plusMinutes(-60*2);
                }
                else if(schemaeType == 2)
                {
                    ppreTime = ppreTime.plusDays(-1*2);
                }
                else if(schemaeType == 3)
                {
                    ppreTime = ppreTime.plusMonths(-1*2);
                }

                String ppreTimeKey = ppreTime.format(timeFormatter);

                double ppredata =dataRegList.get(ppreTimeKey);


                while (1==1)
                {
                    double diff1 = errdata - predata;
                    double diff2 = predata - ppredata;

                    if(diff1 * pValue < diff2)
                    {

                        //保存数据
                        oneData.setR0P1(errdata);
                        //变更原始值
                        entry.setValue(errdata);
                        break;

                    }
                    else
                    {
                        errdata = errdata - this.randomNumber(0.01,0.05);

                    }
                }

            }
            else
            {
                //正常插入值
                oneData.setR0P1(tmpdata);
            }
            dataList.add(oneData);
        }
        return dataList;



    }

    //总是为0
    private List<DataReg> generatAlwaysZero(Map<String, Double> dataRegList,
                                            List<String> errIndexList,
                                            String sdpId,
                                            MdmAssetVEERuleDataSource veeRuleDataSource,
                                            List<MdmAssetVEERuleParam> params) throws ParseException {
        List<DataReg> dataList = new ArrayList<>();

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        double pValue = 0;

        ArrayList<String> keys = new ArrayList<>(dataRegList.keySet());

        for (MdmAssetVEERuleParam p: params
        ) {

            if(p.getParamKey().equals("resultValue") && p.getParamValue() != null)
            {
                pValue = p.getParamValue();

            }

        }

        int cycleCount = veeRuleDataSource.getCycleCount()-1;

        for (Map.Entry<String, Double> entry :dataRegList.entrySet()
        )
        {


            if(errIndexList.indexOf(entry.getKey()) >= 0)
            {

                double tmpData = pValue-this.randomNumber(1,10);

                //修改当前节点及当前节点之前关联节点值
                entry.setValue(tmpData);


                int currIndex = keys.indexOf(entry.getKey());

                //回退指定点
                for(int i=1; i <= cycleCount;i++)
                {
                    int tmpIndex = currIndex - i;

                    String tmpKey = keys.get(tmpIndex);

                    tmpData = pValue-this.randomNumber(1,10);

                    dataRegList.put(tmpKey, tmpData);
                }


            }
            else
            {

            }
        }

        //构造返回数据
        for (Map.Entry<String, Double> entry :dataRegList.entrySet()
        )
        {
            DataReg oneData = new DataReg();

            Date tv = simpleDateFormat.parse(entry.getKey());
            double tmpdata = entry.getValue();

            oneData.getDataRegPK().setTv(tv);
            oneData.getDataRegPK().setSdpId(sdpId);
            oneData.setR0P1(tmpdata);

            dataList.add(oneData);



        }
        return dataList;

    }

    //0消耗
    private List<DataReg> generatZeroConsumption(Map<String, Double> dataRegList,
                                            List<String> errIndexList,
                                            String sdpId,
                                            MdmAssetVEERuleDataSource veeRuleDataSource,
                                            List<MdmAssetVEERuleParam> params) throws ParseException {
        List<DataReg> dataList = new ArrayList<>();

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        double pValue = 0;

        ArrayList<String> keys = new ArrayList<>(dataRegList.keySet());

        for (MdmAssetVEERuleParam p: params
        ) {

            if(p.getParamKey().equals("resultValue") && p.getParamValue() != null)
            {
                pValue = p.getParamValue();

            }

        }

        int cycleCount = veeRuleDataSource.getCycleCount();

        for (Map.Entry<String, Double> entry :dataRegList.entrySet()
        )
        {


            if(errIndexList.indexOf(entry.getKey()) >= 0)
            {



                int currIndex = keys.indexOf(entry.getKey());
                int startIndex = currIndex + veeRuleDataSource.getStartCycle();
                double tmpData = 0;
                //回退指定点
                for(int i=startIndex; i <= currIndex;i++)
                {


                    String tmpKey = keys.get(i);

                    //第一个不改值，后面的用第一个值
                    if(i == startIndex)
                    {
                        tmpData = dataRegList.get(tmpKey);

                        continue;
                    }
                    else
                    {
                        dataRegList.put(tmpKey, tmpData);
                    }


                }


            }
            else
            {

            }
        }

        //构造返回数据
        for (Map.Entry<String, Double> entry :dataRegList.entrySet()
        )
        {
            DataReg oneData = new DataReg();

            Date tv = simpleDateFormat.parse(entry.getKey());
            double tmpdata = entry.getValue();

            oneData.getDataRegPK().setTv(tv);
            oneData.getDataRegPK().setSdpId(sdpId);
            oneData.setR0P1(tmpdata);

            dataList.add(oneData);



        }
        return dataList;

    }

    //曲线数据不等于冻结数据,日冻结与月冻结不等，判断相同
    private Map<String, List<DataReg>> generatMultDataSourceData(
            Map<String,Map<String, Double>> dataRegMapList,
            int errCount,
            String sdpId,
            List<MdmAssetVEERuleDataSource> veeRuleDataSourceList,
            List<String> errList

    ) throws ParseException {
        Map<String, List<DataReg>> result = new HashMap<>();

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        //找到datasource中的keysource
        List<MdmAssetVEERuleDataSource> keySouceList = veeRuleDataSourceList.stream().filter(a->a.getKeyFlag() == 1).collect(Collectors.toList());
        List<MdmAssetVEERuleDataSource> unKeySouceList = veeRuleDataSourceList.stream().filter(a->a.getKeyFlag() == 0).collect(Collectors.toList());
        //*********************暂时不考虑cycleCount只取一个*************************

        //默认考虑是两条source对比，而且是数据类型的
        MdmAssetVEERuleDataSource keySource = keySouceList.get(0);

        MdmAssetVEERuleDataSource unKeySouce = unKeySouceList.get(0);


        Map<String, Double> mainData = new HashMap<>();

        Map<String, Double> subData = new HashMap<>();

        if(keySource.getSchemeType() == 1)
        {
            mainData = dataRegMapList.get("minutely");
        }
        else if(keySource.getSchemeType() == 2)
        {
            mainData = dataRegMapList.get("dayly");
        }
        else if(keySource.getSchemeType() == 3)
        {
            mainData = dataRegMapList.get("monthly");
        }

        if(unKeySouce.getSchemeType() == 1)
        {
            subData = dataRegMapList.get("minutely");
        }
        else if (unKeySouce.getSchemeType() == 2)
        {
            subData = dataRegMapList.get("dayly");
        }
        else if(unKeySouce.getSchemeType() == 3)
        {
            subData = dataRegMapList.get("monthly");
        }

        if(mainData.size() >0
        && subData.size() > 0)
        {

            ArrayList<String> keys = new ArrayList(mainData.keySet()) ;
            //随机生成指定个数的序号
            int maxIndex = mainData.size() -1;

            List<Integer> errIndexList = new ArrayList<>();



            while (1==1)
            {
                if(errIndexList.size() == errCount)
                {
                    break;
                }

                int tmpIndex = this.randomInteger(0,maxIndex);

                if(errIndexList.indexOf(tmpIndex) >= 0)
                {

                    continue;
                }

                errIndexList.add(tmpIndex);
            }

            //暂时只改主数据源的数据，从的不改

            for (Map.Entry<String, Double> entry: mainData.entrySet()
                 ) {

                String key = entry.getKey();

                int index = keys.indexOf(key);

                if(errIndexList.indexOf(index) >=0)
                {

                    //改掉当前值
                    double tmpData = mainData.get(key);

                    //加上一个随机数
                    tmpData = tmpData + this.randomNumber(1,5);

                    //回写
                    mainData.put(key,tmpData);

                    errList.add(key);
                }



            }

            //构造主数据源
            List<DataReg> mainDataRegList = new ArrayList<>();
            for (Map.Entry<String, Double> entry: mainData.entrySet()
                 ) {
                DataReg oneData = new DataReg();

                Date tv = simpleDateFormat.parse(entry.getKey());
                double tmpdata = entry.getValue();

                oneData.getDataRegPK().setTv(tv);
                oneData.getDataRegPK().setSdpId(sdpId);
                oneData.setR0P1(tmpdata);

                mainDataRegList.add(oneData);
            }

            if(keySource.getSchemeType() == 1)
            {
                result.put("minutely",mainDataRegList);
            }
            else if(keySource.getSchemeType() == 2)
            {
                result.put("dayly",mainDataRegList);
            }
            else if(keySource.getSchemeType() == 3)
            {
                result.put("monthly",mainDataRegList);
            }

            //构造从数据源数据
            List<DataReg> subDataRegList = new ArrayList<>();
            for (Map.Entry<String, Double> entry: subData.entrySet()
                 ) {

                DataReg oneData = new DataReg();

                Date tv = simpleDateFormat.parse(entry.getKey());
                double tmpdata = entry.getValue();

                oneData.getDataRegPK().setTv(tv);
                oneData.getDataRegPK().setSdpId(sdpId);
                oneData.setR0P1(tmpdata);

                subDataRegList.add(oneData);
            }

            if(unKeySouce.getSchemeType() == 1)
            {
                result.put("minutely",subDataRegList);
            }
            else if (unKeySouce.getSchemeType() == 2)
            {

                result.put("dayly",subDataRegList);
            }
            else if (unKeySouce.getSchemeType() == 3)
            {
                result.put("monthly",subDataRegList);
            }
        }


        return result;
    }
}
