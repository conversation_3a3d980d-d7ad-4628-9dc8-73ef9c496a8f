import clouesp.hes.common.CommonUtils.ObjectUtils
import clouesp.hes.common.DataEntity.Data.DataReg
import clouesp.hes.common.DataEntity.Data.DataVEEEvent

def regDayMonthDiff(Map<String, Object> ruleInfo) {
    Integer result = 0;
    DataVEEEvent event = null;
    if (ruleInfo == null ||
            !ruleInfo.containsKey("datas") ||
            !ruleInfo.containsKey("params")) {
        return null;
    }

    List<Map<String, Object>> ruleDatas = ruleInfo.get("datas");
    Map<String, Object> ruleParams = ruleInfo.get("params");
    double resultValue = 0;
    if(ruleParams != null && ruleParams.containsKey("resultValue")) {
        resultValue = ruleParams.get("resultValue");
    }
    for(Map<String, Object> ruleData : ruleDatas) {
        String targetClass = ruleData.get("targetClass");
        List<Map<String, Object>> dataDayList = ruleData.get("dataListDay");
        if(dataDayList == null) {
            return null;
        }
        List<Map<String, Object>> dataMonthList = ruleData.get("dataListMonth");
        if(dataMonthList == null) {
            return null;
        }
        if("REG" == targetClass) {
            if(dataDayList.size() > 0 &&
                    dataMonthList.size() > 0) {
                Map<String, Object> dataDay = dataDayList.get(0);
                DataReg dataDayReg = ObjectUtils.convertMapToObject(dataDay, DataReg.class);
                Map<String, Object> dataMonth = dataMonthList.get(0);
                DataReg dataMonthReg = ObjectUtils.convertMapToObject(dataMonth, DataReg.class);
                Double r0p1Day = dataDayReg.getR0P1();
                Double r0p1Month = dataMonthReg.getR0P1();
                if(r0p1Day != null && r0p1Month != null) {
                    if(Math.abs(r0p1Day.doubleValue() - r0p1Month.doubleValue()) > resultValue) {
                        result = 1
                    }
                }
            }
        }
    }

    if(result.intValue() == 1) {
        event = (DataVEEEvent) ruleInfo.get("event");
    }
    return event;
}