package clouesp.hes.core.DataCalc.service.Validation;

import clouesp.hes.common.DataEntity.Asset.MdmAssetServicePoint;
import clouesp.hes.common.DataEntity.Asset.MdmAssetVEERule;
import clouesp.hes.common.DataEntity.Data.DataVEEEvent;
import clouesp.hes.common.DataEntity.Data.DataVEEEventPK;
import clouesp.hes.common.DataEntity.Data.MdmDataUpdateLog;
import clouesp.hes.common.DataEntity.Data.MdmDataUpdateLogPK;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetServicePointRepository;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetVEERuleRepository;
import clouesp.hes.common.DataRepository.RealTime.Data.RtMdmDataUpdateLogRepository;
import org.apache.rocketmq.common.filter.impl.Op;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service("missDataService")
public class MissDataService {
    @Autowired
    private RtMdmAssetServicePointRepository rtMdmAssetServicePointRepository;

    @Autowired
    private RtMdmDataUpdateLogRepository rtMdmDataUpdateLogRepository;

    @Autowired
    private RtMdmAssetVEERuleRepository rtMdmAssetVEERuleRepository;

    public List<DataVEEEvent> handleMissData(
            String sdpId,
            Date tv,
            int dataType,
            int schemeType
    ) {
        List<DataVEEEvent> dataVEEEvents = null;
        MdmDataUpdateLogPK mdmDataUpdateLogPK = new MdmDataUpdateLogPK();
        mdmDataUpdateLogPK.setSdpId(sdpId);
        mdmDataUpdateLogPK.setSchemeType(schemeType);
        mdmDataUpdateLogPK.setDataType(dataType);
        Optional<MdmDataUpdateLog> optionalMdmDataUpdateLog = rtMdmDataUpdateLogRepository.findById(mdmDataUpdateLogPK);
        MdmDataUpdateLog mdmDataUpdateLog = null;
        if (optionalMdmDataUpdateLog.isPresent()) {

            mdmDataUpdateLog = optionalMdmDataUpdateLog.get();
            Date lastTv = mdmDataUpdateLog.getLastDataTime();
            if (lastTv == null) {
                return null;
            }

            if (tv.getTime() - lastTv.getTime() > 0) {
                List<Date> missDataTvs = new ArrayList<Date>();

                Calendar lastCalendar = Calendar.getInstance();
                lastCalendar.setTime(lastTv);
                Integer scheme = 0;
                int unit = 0;
                if (schemeType == 1) {
                    Optional<MdmAssetServicePoint> optionalSdp = rtMdmAssetServicePointRepository.findById(sdpId);
                    if (!optionalSdp.isPresent())
                        return null;
                    MdmAssetServicePoint sdp = optionalSdp.get();
                    if (dataType == 101) {
                        scheme = sdp.getRegisterScheme();
                    } else if (dataType == 102) {
                        scheme = sdp.getIntervalScheme();
                    } else if (dataType == 103) {
                        scheme = sdp.getInstScheme();
                    }
                    if (scheme == null || scheme <= 0) {
                        return null;
                    }

                    unit = Calendar.MINUTE;
                } else if (schemeType == 2) {
                    scheme = 1;
                    unit = Calendar.DAY_OF_MONTH;

                } else if (schemeType == 3) {
                    scheme = 1;
                    unit = Calendar.MONTH;
                }

                if (scheme == 0) {
                    return null;
                }

                while(true) {
                    lastCalendar.add(unit, scheme);
                    if (lastCalendar.getTime().getTime() >= tv.getTime()) {
                        break;
                    }
                    missDataTvs.add(lastCalendar.getTime());
                }

                Integer veeEventClass = 1;
                //查找当前sdp对应的vee规则信息
                Optional<MdmAssetServicePoint>  optional = rtMdmAssetServicePointRepository.findById(sdpId);
                if(optional.isPresent())
                {

                    String validGrpId = optional.get().getVeeValidationGroupId();


                    List<MdmAssetVEERule> mdmAssetVEERuleList = rtMdmAssetVEERuleRepository.findAllByMgIdAndEventId(validGrpId,"1001001");

                    if(mdmAssetVEERuleList != null && mdmAssetVEERuleList.size() > 0)
                    {
                        veeEventClass = mdmAssetVEERuleList.get(0).getClassId();
                    }
                }

                if (missDataTvs.size() > 0) {
                    dataVEEEvents = new ArrayList<>();
                    for (Date missDataTv : missDataTvs) {
                        DataVEEEvent dataVEEEvent = new DataVEEEvent();
                        DataVEEEventPK dataVEEEventPK = dataVEEEvent.getDataVEEEventPK();

                        dataVEEEventPK.setTv(missDataTv);
                        dataVEEEventPK.setEventId("1001001");
                        dataVEEEventPK.setSchemeType(schemeType);
                        dataVEEEventPK.setObjectId(sdpId);

                        dataVEEEvent.setUpdateTv(new Date());
                        dataVEEEvent.setDataSource(3);
                        dataVEEEvent.setObjectType(7);
                        dataVEEEvent.setDataType(dataType);
                        //事件等级

                        dataVEEEvent.setVeeEventClass(veeEventClass);
                        dataVEEEvent.setEstimationStatus(0);
                        dataVEEEvents.add(dataVEEEvent);
                    }
                }
            }
        }

        if (mdmDataUpdateLog == null) {
            mdmDataUpdateLog = new MdmDataUpdateLog();
            mdmDataUpdateLog.setMdmDataUpdateLogPK(mdmDataUpdateLogPK);
        }
        mdmDataUpdateLog.setLastDataTime(tv);
        mdmDataUpdateLog.setLastUpdateTime(new Date());
        rtMdmDataUpdateLogRepository.save(mdmDataUpdateLog);
        return dataVEEEvents;
    }
}
