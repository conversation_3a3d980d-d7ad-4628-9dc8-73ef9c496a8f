package clouesp.hes.core.DataScan.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@Configuration
@EnableSwagger2
public class SwaggerConfig {
	@Bean
	public Docket createRestApi(){
		return new Docket(DocumentationType.SWAGGER_2)
				.groupName("controller")
				.apiInfo(apiInfo())
				.select()
				.apis(RequestHandlerSelectors.basePackage("clouesp.hes.core.DataScan"))//项目包名
				.paths(PathSelectors.any()).build();
	}
	
	private ApiInfo apiInfo(){
		return new ApiInfoBuilder()
				.title("VEE ACCESS")
				.description("VEE ACCESS")
				.termsOfServiceUrl("")
				.version("1.0")
				.build();
	}

}
