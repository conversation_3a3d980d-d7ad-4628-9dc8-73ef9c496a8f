package clouesp.hes.common.DataEntity.System;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name= "sys_dataitem_export")
public class SysDataitemExport {
	@Id
	@Column(name = "dataitem_id", nullable = false, columnDefinition = "varchar(64)")
	private String dataitemId;

	@Column(name = "dataitem_type")
	private Integer dataitemType;
	
	@Column(name = "dst_dataitem_id", columnDefinition = "varchar(64)")
	private String dstDataitemId;
	
	@Column(name = "time_type")
	private Integer timeType;
	
	@Column(name = "export_flag")
	private Integer exportFlag;

	public String getDataitemId() {
		return dataitemId;
	}

	public void setDataitemId(String dataitemId) {
		this.dataitemId = dataitemId;
	}

	public Integer getDataitemType() {
		return dataitemType;
	}

	public void setDataitemType(Integer dataitemType) {
		this.dataitemType = dataitemType;
	}

	public String getDstDataitemId() {
		return dstDataitemId;
	}

	public void setDstDataitemId(String dstDataitemId) {
		this.dstDataitemId = dstDataitemId;
	}

	public Integer getTimeType() {
		return timeType;
	}

	public void setTimeType(Integer timeType) {
		this.timeType = timeType;
	}

	public Integer getExportFlag() {
		return exportFlag;
	}

	public void setExportFlag(Integer exportFlag) {
		this.exportFlag = exportFlag;
	}
}
