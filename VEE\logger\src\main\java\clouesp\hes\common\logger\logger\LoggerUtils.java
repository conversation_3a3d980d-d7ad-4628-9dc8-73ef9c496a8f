/*
 * 文件名：LoggerUtil.java 版权：Copyright by Power7000 Team 描述： 修改人：jybai 修改时间：2017年10月25日 跟踪单号： 修改单号：
 * 修改内容：
 */

package clouesp.hes.common.logger.logger;

import org.apache.logging.log4j.Level;

public class LoggerUtils {

    private static LoggerUtils instance = null;

    private LoggerUtils() {

    }

    public static LoggerUtils getInstance() {
        if (instance == null) instance = new LoggerUtils();
        return instance;
    }

    public String getClassName() {
        StackTraceElement[] stacks = new Throwable().getStackTrace();
        return stacks[1].getClassName();
    }

    public String getMethodName() {
        StackTraceElement[] stacks = new Throwable().getStackTrace();
        return stacks[1].getMethodName();
    }

    public int getLineNumber() {
        StackTraceElement[] stacks = new Throwable().getStackTrace();
        return stacks[1].getLineNumber();
    }

    public String getTraceInfo() {
        StringBuffer sb = new StringBuffer();

        StackTraceElement[] stacks = new Throwable().getStackTrace();

        sb.append("class: ").append(stacks[1].getClassName()).append("; method: ").append(
            stacks[1].getMethodName()).append("; linenumber: ").append(stacks[1].getLineNumber());

        return sb.toString();
    }
    
    public Level LoggerLevel2Level(LoggerLevel level) {
        if (level == LoggerLevel.ALL) return Level.ALL;
        if (level == LoggerLevel.TRACE)
            return Level.TRACE;
        else if (level == LoggerLevel.DEBUG)
            return Level.DEBUG;
        else if (level == LoggerLevel.INFO)
            return Level.INFO;
        else if (level == LoggerLevel.WARN)
            return Level.WARN;
        else if (level == LoggerLevel.ERROR)
            return Level.ERROR;
        else if (level == LoggerLevel.FATAL)
            return Level.FATAL;
        else if (level == LoggerLevel.OFF) return Level.OFF;
        return Level.OFF;
    }
    
    public LoggerLevel Level2LoggerLevel(Level level) {
        if (level == Level.ALL) return LoggerLevel.ALL;
        if (level == Level.TRACE)
            return LoggerLevel.TRACE;
        else if (level == Level.DEBUG)
            return LoggerLevel.DEBUG;
        else if (level == Level.INFO)
            return LoggerLevel.INFO;
        else if (level == Level.WARN)
            return LoggerLevel.WARN;
        else if (level == Level.ERROR)
            return LoggerLevel.ERROR;
        else if (level == Level.FATAL)
            return LoggerLevel.FATAL;
        else if (level == Level.OFF) return LoggerLevel.OFF;
        return LoggerLevel.OFF;
    }   
}
