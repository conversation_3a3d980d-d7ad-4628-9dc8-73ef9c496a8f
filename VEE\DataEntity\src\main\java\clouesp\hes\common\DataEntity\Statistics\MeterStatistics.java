package clouesp.hes.common.DataEntity.Statistics;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name="meter_statistics")
@Data
public class MeterStatistics {
    @Id
    @Column(name="org_id",nullable = false,columnDefinition ="varchar(32)")
    private String orgId ;
    @Column(name = "meter_count",columnDefinition = "INT")
    private int meterCount ;

}
