<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="DataMdMapper">

    <select id="getDataMdList_COUNT_bak" resultType="java.lang.Long"  parameterType="Request">
        select
        count(*)
        from
        ${params.tableName}
        where
        1 = 1
        <if test="params">
            <if test="params.deviceList != null and params.deviceList.size > 0">
                and device_id in
                <foreach collection="params.deviceList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </if>

        <choose>
            <when test="entity.communicatorSn != null and entity.communicatorSn != ''">
                <if test="entity.startTv != null">
                    and tv = #{entity.startTv}
                </if>
            </when>
            <otherwise>
                <if test="entity.sn != null and entity.sn != ''">
                    <if test="entity.startTv != null">
                        and tv &gt;= #{entity.startTv}
                    </if>
                    <if test="entity.endTv != null">
                        and tv &lt; #{entity.endTv}
                    </if>
                </if>
            </otherwise>
        </choose>


    </select>

    <select id="getDataMdList" resultType="java.util.Map"  parameterType="Request">
        select
        am.sn,
        am.name,
        md.tv,
        md.update_tv,
        1 as state,
        <foreach collection="params.colNames" item="colName" index="index" open=" " separator=", " close=" ">
            ${colName}
        </foreach>
        from
        ${params.tableName} md,
        asset_meter am
        where
        am.id = md.device_id
        <if test="params">
            <if test="params.deviceList != null and params.deviceList.size > 0">
                and md.device_id in
                <foreach collection="params.deviceList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </if>

        <if test="entity">
            <choose>
                <when test="entity.communicatorSn != null and entity.communicatorSn != ''">
                    <if test="entity.startTv != null">
                        and md.tv = #{entity.startTv}
                    </if>
                </when>
                <otherwise>
                    <if test="entity.sn != null and entity.sn != ''">
                        <if test="entity.startTv != null">
                            and md.tv &gt;= #{entity.startTv}
                        </if>
                        <if test="entity.endTv != null">
                            and md.tv &lt; #{entity.endTv}
                        </if>
                    </if>
                </otherwise>
            </choose>
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>
    </select>

    <select id="getDataMdList_communicator" resultType="java.util.Map"  parameterType="Request">
        select a.*,b.* from (
        SELECT
        am.sn,
        am.NAME,
        am.id as meterid,
        '${params.times_start}' as tv
        FROM
        asset_meter am
        WHERE
        am.COMMUNICATOR_ID = ( SELECT id FROM asset_communicator WHERE sn = '${params.communicatorSn}' )) a
        left join
        (
        select

        md.device_id,
        md.tv as ts,
        md.update_tv,
        1 as state,
        <foreach collection="params.colNames" item="colName" index="index" open=" " separator=", " close=" ">
            ${colName}
        </foreach>
        from
        ${params.tableName} md

        where
        1 = 1
        <if test="params">
            <if test="params.deviceList != null and params.deviceList.size > 0">
                and md.device_id in
                <foreach collection="params.deviceList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </if>

        <if test="entity">
            <choose>
                <when test="entity.communicatorSn != null and entity.communicatorSn != ''">
                    <if test="entity.startTv != null">
                        and md.tv = #{entity.startTv}
                    </if>
                </when>
                <otherwise>
                    <if test="entity.sn != null and entity.sn != ''">
                        <if test="entity.startTv != null">
                            and md.tv &gt;= #{entity.startTv}
                        </if>
                        <if test="entity.endTv != null">
                            and md.tv &lt; #{entity.endTv}
                        </if>
                    </if>
                </otherwise>
            </choose>
        </if>


        ) b on b.device_id=a.meterid
        <if test="_databaseId == 'oracle'">
            and b.ts = to_date('${params.times_start}','yyyy-MM-dd hh24:mi:ss')
        </if>
        <if test="_databaseId == 'mysql'">

            and b.ts = str_to_date('${params.times_start}','%Y-%m-%d %H:%i:%S')
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <select id="getDataMdList_daily" resultType="java.util.Map"  parameterType="Request">
        select alldata.*,  am.sn,
        am.name from (
        <if test="_databaseId == 'oracle'">
            select
            b.*,a.serizlName,nvl(b.tm,to_date(a.DAY_TIME,'yyyy-mm-dd,hh24:mi:ss')) as tv
            from    (
            SELECT '${params.serizlName}' as  serizlName, TO_CHAR(TO_DATE('${params.start_time_t}', 'YYYY-MM-DD') + ROWNUM - 1, 'YYYY-MM-DD') DAY_TIME
            FROM DUAL
            CONNECT BY ROWNUM  &lt;= (TO_DATE('${params.end_time_t}', 'YYYY-MM-DD')+1) - TO_DATE('${params.start_time_t}', 'YYYY-MM-DD')
            ) a
            left join (
        </if>
        <if test="_databaseId == 'mysql'">
            select
            b.*,a.serizlName,ifnull(b.tm,str_to_date(a.DAY_TIME,'%Y-%m-%d %H:%i:%S')) as tv
            from    (

            SELECT
            ${params.serizlName} as  serizlName,a.Date AS DAY_TIME
            FROM
            (
            SELECT
            curdate( ) - INTERVAL ( a.a + ( 10 * b.a ) + ( 100 * c.a ) ) DAY AS Date
            FROM
            (
            SELECT 0 AS a UNION ALL
            SELECT 1 UNION ALL
            SELECT 2 UNION ALL
            SELECT 3 UNION ALL
            SELECT 4 UNION ALL
            SELECT 5 UNION ALL
            SELECT 6 UNION ALL
            SELECT 7 UNION ALL
            SELECT 8 UNION ALL
            SELECT 9 ) AS a CROSS JOIN (
            SELECT 0 AS a UNION ALL
            SELECT 1 UNION ALL
            SELECT 2 UNION ALL
            SELECT 3 UNION ALL
            SELECT 4 UNION ALL
            SELECT 5 UNION ALL
            SELECT 6 UNION ALL
            SELECT 7 UNION ALL
            SELECT	8 UNION ALL
            SELECT	9 	) AS b	CROSS JOIN (
            SELECT	0 AS a UNION ALL
            SELECT	1 UNION ALL
            SELECT	2 UNION ALL
            SELECT	3 UNION ALL
            SELECT	4 UNION ALL
            SELECT	5 UNION ALL
            SELECT	6 UNION ALL
            SELECT	7 UNION ALL
            SELECT	8 UNION ALL
            SELECT	9 	) AS c 	) a
            WHERE	a.Date BETWEEN '${params.start_time_t}' 	AND '${params.end_time_t}'
            ORDER BY	DAY_TIME ASC

            ) a
            left join (
        </if>
        select
        md.tv as tm,
        <if test="_databaseId == 'oracle'">
            to_char(trunc(md.TV),'YYYY-MM-DD') as ts,
        </if>
        <if test="_databaseId == 'mysql'">
            DATE_FORMAT(md.TV, '%Y-%m-%d') as ts,
        </if>

        md.update_tv,
        1 as state,
        <foreach collection="params.colNames" item="colName" index="index" open=" " separator=", " close=" ">
            ${colName}
        </foreach>
        from
        ${params.tableName} md
        where
        1=1
        <if test="params">
            <if test="params.deviceList != null and params.deviceList.size > 0">
                and md.device_id in
                <foreach collection="params.deviceList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </if>

        <if test="entity">
            <choose>
                <when test="entity.communicatorSn != null and entity.communicatorSn != ''">
                    <if test="entity.startTv != null">
                        and md.tv = #{entity.startTv}
                    </if>
                </when>
                <otherwise>
                    <if test="entity.sn != null and entity.sn != ''">
                        <if test="entity.startTv != null">
                            and md.tv &gt;= #{entity.startTv}
                        </if>
                        <if test="entity.endTv != null">
                            and md.tv &lt; #{entity.endTv}
                        </if>
                    </if>
                </otherwise>
            </choose>
        </if>
        ) b on b.ts = a.DAY_TIME
            ) alldata left join asset_meter am on alldata.serizlName = am.sn

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>
    </select>

    <select id="getDataMdList_minutely" resultType="java.util.Map"  parameterType="Request">
        select alldata.*,  am.sn,
        am.name from (
        <if test="_databaseId == 'oracle'">
            select
            b.*,a.serizlName,nvl(b.tm,to_date(a.DAY_TIME,'yyyy-mm-dd,hh24:mi:ss')) as tv
            from    (

            select '${params.serizlName}' as  serizlName,
            to_char(to_date('${params.times_start_x}','yyyy-mm-dd hh24:mi:ss') + (rownum - 1)/(24*'${params.minuteType}'),'YYYY-MM-DD hh24:mi:ss') as DAY_TIME
            from dual
            connect by rownum &lt;=
            (
            trunc(to_date('${params.times_end_y}','yyyy-mm-dd hh24:mi:ss'), 'mi')
            -
            trunc(to_date('${params.times_start_x}','yyyy-mm-dd hh24:mi:ss'), 'mi')
            )*24*'${params.minuteType}' + 1
            ) a
            left join (
        </if>
        <if test="_databaseId == 'mysql'">
            select
            b.*,a.serizlName,ifnull(b.tm,str_to_date(a.DAY_TIME,'%Y-%m-%d %H:%i:%S')) as tv
            from    (
            select ${params.serizlName} as  serizlName, date_add('${params.times_start_x}', interval row1*'${params.minutes}' MINUTE) DAY_TIME from
            (
            SELECT @row := @row + 1 as row1 FROM
            (select 0 union all select 1 union all select 2 union all select 3 union all select 4 union all select 5 union all select 6 union all select 7 union all select 8 union all select 9) t,
            (select 0 union all select 1 union all select 2 union all select 3 union all select 4 union all select 5 union all select 6 union all select 7 union all select 8 union all select 9 ) t1,
            (select 0 union all select 1 union all select 2 union all select 3 union all select 4 union all select 5 union all select 6 union all select 7 union all select 8 union all select 9 ) t2,
            (SELECT @row:=-1) r
            ) se
            where date_add('${params.times_start_x}', interval row1*'${params.minutes}'  MINUTE) &lt;= '${params.times_end_y}'


            ) a
            left join (

        </if>
        select
        md.tv as tm,
        <if test="_databaseId == 'oracle'">
        to_char(TV,'YYYY-MM-DD hh24:mi:ss') as ts,
        </if>
        <if test="_databaseId == 'mysql'">
        DATE_FORMAT(TV, '%Y-%m-%d %H:%i:%s') as ts,
        </if>
        md.update_tv,
        1 as state,
        <foreach collection="params.colNames" item="colName" index="index" open=" " separator=", " close=" ">
            ${colName}
        </foreach>
        from
        ${params.tableName} md
        where
        1=1
        <if test="params">
            <if test="params.deviceList != null and params.deviceList.size > 0">
                and md.device_id in
                <foreach collection="params.deviceList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </if>

        <if test="entity">
            <choose>
                <when test="entity.communicatorSn != null and entity.communicatorSn != ''">
                    <if test="entity.startTv != null">
                        and md.tv = #{entity.startTv}
                    </if>
                </when>
                <otherwise>
                    <if test="entity.sn != null and entity.sn != ''">
                        <if test="entity.startTv != null">
                            and md.tv &gt;= #{entity.startTv}
                        </if>
                        <if test="entity.endTv != null">
                            and md.tv &lt; #{entity.endTv}
                        </if>
                    </if>
                </otherwise>
            </choose>
        </if>
        ) b on b.ts = a.DAY_TIME
        ) alldata left join asset_meter am on alldata.serizlName = am.sn

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>
    </select>

    <select id="getDataMdList_monthly" resultType="java.util.Map"  parameterType="Request">
        select alldata.*,  am.sn,
        am.name from (
        <if test="_databaseId == 'oracle'">
            select
            b.*,a.serizlName,nvl(b.tm,to_date(a.DAY_TIME,'yyyy-mm-dd,hh24:mi:ss')) as tv
            from    (

            select '${params.serizlName}' as  serizlName, concat(ss.DAY_TIME,'-01') as DAY_TIME from (
            SELECT TO_CHAR(TO_DATE('${params.times_start_x}','YYYY-MM-DD')+ROWNUM-1 ,'YYYY-MM') DAY_TIME
            from dual
            connect by rownum &lt; to_date('${params.times_end_y}', 'yyyy-mm-dd')-to_date('${params.times_start_x}', 'yyyy-mm-dd') +2
            ) ss GROUP BY DAY_TIME ORDER BY DAY_TIME
            ) a
            left join (
        </if>
        <if test="_databaseId == 'mysql'">
            select
            b.*,a.serizlName,ifnull(b.tm,str_to_date(a.DAY_TIME,'%Y-%m-%d %H:%i:%S')) as tv
            from    (
            select *
            from (select ${params.serizlName} as  serizlName,adddate('1970-01-01', interval
            t2.i * 100 + t1.i * 10 + t0.i month) DAY_TIME
            from (select 0 i union select 1 union
            select 2 union select 3 union
            select 4 union select 5 union
            select 6 union select 7 union
            select 8 union select 9) t0,
            (select 0 i union select 1 union
            select 2 union select 3 union
            select 4 union select 5 union
            select 6 union select 7 union
            select 8 union select 9) t1,
            (select 0 i union select 1 union
            select 2 union select 3 union
            select 4 union select 5 union
            select 6 union select 7 union
            select 8 union select 9) t2) a
            where DAY_TIME between subdate(date('${params.times_start_x}'), interval 1 month) and date('${params.times_end_y}')
            ) a
            left join (

        </if>
        select
        md.tv as tm,
        <if test="_databaseId == 'oracle'">
        to_char(trunc(md.TV),'YYYY-MM-DD') as ts,
        </if>
        <if test="_databaseId == 'mysql'">
        DATE_FORMAT(md.TV, '%Y-%m-%d') as ts,
        </if>
        md.update_tv,
        1 as state,
        <foreach collection="params.colNames" item="colName" index="index" open=" " separator=", " close=" ">
            ${colName}
        </foreach>
        from
        ${params.tableName} md
        where
        1=1
        <if test="params">
            <if test="params.deviceList != null and params.deviceList.size > 0">
                and md.device_id in
                <foreach collection="params.deviceList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </if>

        <if test="entity">
            <choose>
                <when test="entity.communicatorSn != null and entity.communicatorSn != ''">
                    <if test="entity.startTv != null">
                        and md.tv = #{entity.startTv}
                    </if>
                </when>
                <otherwise>
                    <if test="entity.sn != null and entity.sn != ''">
                        <if test="entity.startTv != null">
                            and md.tv &gt;= #{entity.startTv}
                        </if>
                        <if test="entity.endTv != null">
                            and md.tv &lt; #{entity.endTv}
                        </if>
                    </if>
                </otherwise>
            </choose>
        </if>
        ) b on b.ts = a.DAY_TIME
        ) alldata left join asset_meter am on alldata.serizlName = am.sn

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>
    </select>




    <select id="getDataMdOrgList" resultType="java.util.Map"  parameterType="Request">
        select
        am.sn,
        am.name,
        md.tv,
        md.update_tv,
        1 as state,
        <foreach collection="params.colNames" item="colName" index="index" open=" " separator=", " close=" ">
            ${colName}
        </foreach>
        from
        ${params.tableName} md,
        asset_meter am
        where
        am.id = md.device_id
        <if test="params">
            <if test="params.deviceList != null and params.deviceList.size > 0">
                and md.device_id in
                <foreach collection="params.deviceList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="params.orgIdList != null and params.orgIdList.size > 0">
                and am.org_id in
                <foreach collection="params.orgIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="params.queryOrgIdList != null and params.queryOrgIdList.size > 0">
                and am.org_id in
                <foreach collection="params.queryOrgIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

        </if>

        <if test="entity">


            <if test="entity.startTv != null">
                and md.tv &gt;= #{entity.startTv}
            </if>
            <if test="entity.endTv != null">
                and md.tv &lt;= #{entity.endTv}
            </if>



        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>
    </select>

    <select id="getDataMdOrgList_COUNT" resultType="java.lang.Long"  parameterType="Request">
        select
        count(0)
        from
        ${params.tableName} md,
        asset_meter am
        where
        am.id = md.device_id
        <if test="params">
            <if test="params.deviceList != null and params.deviceList.size > 0">
                and md.device_id in
                <foreach collection="params.deviceList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="params.orgIdList != null and params.orgIdList.size > 0">
                and am.org_id in
                <foreach collection="params.orgIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="params.queryOrgIdList != null and params.queryOrgIdList.size > 0">
                and am.org_id in
                <foreach collection="params.queryOrgIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

        </if>

        <if test="entity">

            <if test="entity.startTv != null">
                and md.tv &gt;= #{entity.startTv}
            </if>
            <if test="entity.endTv != null">
                and md.tv &lt;= #{entity.endTv}
            </if>

        </if>


    </select>
</mapper>