package clouesp.hes.common.DataMode.Statistics;

public class MdmDataOutageStatistics {
    private  MdmDataOutageStatisticsPK mdmDataOutageStatisticsPK = new MdmDataOutageStatisticsPK();
    private  Integer sdpTotalNum ; //SDP总数，该组织机构SDP总数
    private  Integer totalTime ;   //累计停电时间, 单位:分钟，组织机构下所有停电时间累加
    private  Integer totalNum ;    //累计停电次数，组织机构下所有停电时间累加
    private  Integer avgTime  ;    //平均停电时间，累计停电时间/SDP总数
    private  Integer avgNum ;     //平均停电次数，累计停电次数/SDP总数


    public MdmDataOutageStatisticsPK getMdmDataOutageStatisticsPK() {
        return mdmDataOutageStatisticsPK;
    }

    public void setMdmDataOutageStatisticsPK(MdmDataOutageStatisticsPK mdmDataOutageStatisticsPK) {
        this.mdmDataOutageStatisticsPK = mdmDataOutageStatisticsPK;
    }

    public Integer getSdpTotalNum() {
        return sdpTotalNum;
    }

    public void setSdpTotalNum(Integer sdpTotalNum) {
        this.sdpTotalNum = sdpTotalNum;
    }

    public Integer getTotalTime() {
        return totalTime;
    }

    public void setTotalTime(Integer totalTime) {
        this.totalTime = totalTime;
    }

    public Integer getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Integer totalNum) {
        this.totalNum = totalNum;
    }

    public Integer getAvgTime() {
        return avgTime;
    }

    public void setAvgTime(Integer avgTime) {
        this.avgTime = avgTime;
    }

    public Integer getAvgNum() {
        return avgNum;
    }

    public void setAvgNum(Integer avgNum) {
        this.avgNum = avgNum;
    }

    public String toString(){
       return    mdmDataOutageStatisticsPK.toString() + " , sdpTotalNum = " + (sdpTotalNum == null ? "null" : sdpTotalNum.intValue() )
                + " , totalTime = " + (totalTime == null ? "null" : totalTime.intValue() )
               + " , totalNum = " + (totalNum == null ? "null" : totalNum.intValue() )
               + " , avgTime = " + (avgTime == null ? "null" : avgTime.intValue() )
               + " , avgNum = " + (avgNum == null ? "null" : avgNum.intValue() ) ;
    }
}
