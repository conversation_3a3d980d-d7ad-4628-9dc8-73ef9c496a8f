package clouesp.hes.common.DataEntity.Asset;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import java.text.SimpleDateFormat;
import java.util.Date;

@ApiModel(value="mdmAssetCalcObj",description="计算对象信息")
@Entity
@Table(name= "mdm_asset_calc_obj")
public class MdmAssetCalcObj {
	@ApiModelProperty(value="计算对象ID", position=1)
	@Id
	@Column(name = "id", nullable = false, columnDefinition = "varchar(32)")
	private String id;
	
	@ApiModelProperty(value="字典值1119: 1:损耗对象  2:总加对象", position=2)
	@Column(name = "type")
	private Integer type;
	
	@ApiModelProperty(value="名称", position=3)
	@Column(name = "name", columnDefinition = "varchar(64)")
	private String name;
	
	@ApiModelProperty(value="字典值1118, 3、线路 4 变压器 5 部门", position=4)
	@Column(name = "entity_type")
	private Integer entityType;
	
	@ApiModelProperty(value="实体ID", position=5)
	@Column(name = "entity_id", columnDefinition = "varchar(32)")
	private String entityId;
	
	@ApiModelProperty(value="是否需要结算，0-否,1-是", position=6)
	@Column(name = "calc_enable")
	private Integer calcEnable;
	
	@ApiModelProperty(value="结算方案编码", position=7)
	@Column(name = "scheme_ids", columnDefinition = "varchar(32)")
	private String schemeIds;
	
	@ApiModelProperty(value="总加对象类型，字典值1120，1-售电量等", position=8)
	@Column(name = "sum_obj_type")
	private Integer sumObjType;
	
	@ApiModelProperty(value="VEE验证规则分组ID", position=9)
	@Column(name = "vee_validation_group_id", columnDefinition = "varchar(32)")
	private String veeValidationGroupId;
		
	@ApiModelProperty(value="VEE估算规则分组ID", position=10)
	@Column(name = "vee_estimation_group_id", columnDefinition = "varchar(32)")
	private String veeEstimationGroupId;
	
	@ApiModelProperty(value="VEE计算规则分组ID", position=11)
	@Column(name = "vee_calculation_group_id", columnDefinition = "varchar(32)")
	private String veeCalculationGroupId;
	
	@ApiModelProperty(value="是否从档案字段中获取计算关系,0-否,1-是", position=12)
	@Column(name = "get_asset")
	private Integer getAsset;
	
	@ApiModelProperty(value="计算对象对应的档案字段类型，1-行业字段", position=13)
	@Column(name = "asset_type")
	private Integer assetType;
	
	@ApiModelProperty(value="计算对象对应的档案字段ID，比如：行业ID", position=14)
	@Column(name = "asset_id", columnDefinition = "varchar(32)")
	private String assetId;
	
	@Transient 
	private Date calcStartTv;
	
	@Transient 
	private Date calcEndTv;

	@ApiModelProperty(value="计算任务是否为手动触发，否则为定时自动任务 ", position=17)
	@Transient
	private boolean isManaual = false;

	public boolean isManaual() {
		return isManaual;
	}

	public void setManaual(boolean manaual) {
		isManaual = manaual;
	}

	@Override
	public String toString() {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

		String value = "id = " + id + " , type = " + type + " , name = " + name + " , entityType = " + entityType + " , entityId + " + entityId + " , calcEnable = " + calcEnable +
			" , schemeIds = " + schemeIds 	+ " , sumObjType = " + sumObjType + " , veeValidationGroupId = " + veeValidationGroupId + " , veeEstimationGroupId = " + veeEstimationGroupId +
			" , veeCalculationGroupId = " + veeCalculationGroupId + " , getAsset = " + getAsset + 	" , assetType = " + assetType + " , assetId = " + assetId ;

		if ( calcStartTv != null ) value += " , calcStartTv = " + sdf.format(calcStartTv) ;
		if ( calcEndTv != null ) value += " , calcEndTv = " + sdf.format(calcEndTv) ;
	    value += " , isManaual = " + isManaual ;
		return  value ;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getEntityType() {
		return entityType;
	}

	public void setEntityType(Integer entityType) {
		this.entityType = entityType;
	}

	public String getEntityId() {
		return entityId;
	}

	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}

	public Integer getCalcEnable() {
		return calcEnable;
	}

	public void setCalcEnable(Integer calcEnable) {
		this.calcEnable = calcEnable;
	}

	public String getSchemeIds() {
		return schemeIds;
	}

	public void setSchemeIds(String schemeIds) {
		this.schemeIds = schemeIds;
	}

	public Integer getSumObjType() {
		return sumObjType;
	}

	public void setSumObjType(Integer sumObjType) {
		this.sumObjType = sumObjType;
	}

	public String getVeeValidationGroupId() {
		return veeValidationGroupId;
	}

	public void setVeeValidationGroupId(String veeValidationGroupId) {
		this.veeValidationGroupId = veeValidationGroupId;
	}

	public String getVeeEstimationGroupId() {
		return veeEstimationGroupId;
	}

	public void setVeeEstimationGroupId(String veeEstimationGroupId) {
		this.veeEstimationGroupId = veeEstimationGroupId;
	}

	public String getVeeCalculationGroupId() {
		return veeCalculationGroupId;
	}

	public void setVeeCalculationGroupId(String veeCalculationGroupId) {
		this.veeCalculationGroupId = veeCalculationGroupId;
	}

	public Integer getGetAsset() {
		return getAsset;
	}

	public void setGetAsset(Integer getAsset) {
		this.getAsset = getAsset;
	}

	public Integer getAssetType() {
		return assetType;
	}

	public void setAssetType(Integer assetType) {
		this.assetType = assetType;
	}

	public String getAssetId() {
		return assetId;
	}

	public void setAssetId(String assetId) {
		this.assetId = assetId;
	}

	public Date getCalcStartTv() {
		return calcStartTv;
	}

	public void setCalcStartTv(Date calcStartTv) {
		this.calcStartTv = calcStartTv;
	}

	public Date getCalcEndTv() {
		return calcEndTv;
	}

	public void setCalcEndTv(Date calcEndTv) {
		this.calcEndTv = calcEndTv;
	}	
}
