package clouesp.hes.core.DataImport.config;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;


@Configuration
public class DataSourceConfig {
	
    @Bean(name = "persistenceDS")
    @Primary
    @Qualifier("persistenceDS")
    @ConfigurationProperties(prefix = "spring.persistence.datasource")
    public DataSource persistenceDS() {
        return DataSourceBuilder.create().build();
    }	
    
    @Bean(name="persistenceJdbcTemplate")
    public JdbcTemplate primaryJdbcTemplate (
        @Qualifier("persistenceDS")  DataSource dataSource ) {
        return new JdbcTemplate(dataSource);
    }
	
    @Bean(name = "realtimeDS")
    @Qualifier("realtimeDS")
    @ConfigurationProperties(prefix = "spring.realtime.datasource")
    public DataSource realtimeDS() {
    	return DataSourceBuilder.create().build();
    }

    @Bean(name="realtimeJdbcTemplate")
    public JdbcTemplate realtimeJdbcTemplate (
            @Qualifier("realtimeDS")  DataSource dataSource ) {
        return new JdbcTemplate(dataSource);
    }
}
