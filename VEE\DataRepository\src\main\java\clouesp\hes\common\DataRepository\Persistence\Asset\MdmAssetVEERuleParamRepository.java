package clouesp.hes.common.DataRepository.Persistence.Asset;

import org.springframework.data.jpa.repository.JpaRepository;

import clouesp.hes.common.DataEntity.Asset.MdmAssetVEERuleParam;
import clouesp.hes.common.DataEntity.Asset.MdmAssetVEERuleParamPK;

import java.util.List;

public interface MdmAssetVEERuleParamRepository extends JpaRepository<MdmAssetVEERuleParam, MdmAssetVEERuleParamPK>{

    List<MdmAssetVEERuleParam> findAllByRuleId(String ruleId);
}
