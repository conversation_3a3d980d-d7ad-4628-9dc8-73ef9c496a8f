package clouesp.hes.core.DataCalc.service.Validation;

import clouesp.hes.common.CommonUtils.ObjectUtils;
import clouesp.hes.common.DataEntity.Asset.*;
import clouesp.hes.common.DataEntity.Data.*;
import clouesp.hes.common.DataEntity.Dict.VEEMethod;
import clouesp.hes.common.DataRepository.Persistence.Data.*;
import clouesp.hes.common.DataRepository.RealTime.Asset.*;
import clouesp.hes.common.DataRepository.RealTime.Data.*;
import clouesp.hes.common.DataRepository.RealTime.Dict.RtVEEMethodRepository;
import clouesp.hes.common.logger.logger.Logger;
import clouesp.hes.common.logger.logger.LoggerLevel;
import clouesp.hes.core.DataCalc.config.ServerConfig;
import clouesp.hes.core.DataCalc.service.Storage.EventStorageService;
import groovy.lang.GroovyObject;
import groovy.util.GroovyScriptEngine;
import jline.internal.Log;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("validationService")
public class ValidationService {
	@Autowired
	private RtMdmAssetServicePointRepository rtMdmAssetServicePointRepository;
	@Autowired
	private RtMdmAssetVEERuleRepository rtMdmAssetVEERuleRepository;
	@Autowired
	private RtVEEMethodRepository rtVEEMethodRepository;
	@Autowired
	private RtMdmAssetVEERuleDataSourceRepository rtMdmAssetVEERuleDataSourceRepository;
	@Autowired
	private RtMdmAssetVEERuleParamRepository rtMdmAssetVEERuleParamRepository;
	@Autowired	
	private DataRegDailyRepository dataRegDailyRepository;
	@Autowired
	private RtDataRegDailyRepository rtDataRegDailyRepository;
	@Autowired
	private DataRegMinutelyRepository dataRegMinutelyRepository;
	@Autowired
	private RtDataRegMinutelyRepository rtDataRegMinutelyRepository;
	@Autowired
	private DataRegMonthlyRepository dataRegMonthlyRepository;
	@Autowired
	private RtDataRegMonthlyRepository rtDataRegMonthlyRepository;
	@Autowired
	private DataIntervalDailyRepository dataIntervalDailyRepository;
	@Autowired
	private RtDataIntervalDailyRepository rtDataIntervalDailyRepository;
	@Autowired
	private DataIntervalMinutelyRepository dataIntervalMinutelyRepository;
	@Autowired
	private RtDataIntervalMinutelyRepository rtDataIntervalMinutelyRepository;
	@Autowired
	private DataIntervalMonthlyRepository dataIntervalMonthlyRepository;
	@Autowired
	private RtDataIntervalMonthlyRepository rtDataIntervalMonthlyRepository;	
	@Autowired
	private DataInstMinutelyRepository dataInstMinutelyRepository;
	@Autowired
	private RtDataInstMinutelyRepository rtDataInstMinutelyRepository;

	@Autowired
	private RtMdmAssetCalcObjRepository rtMdmAssetCalcObjRepository;

	@Autowired
	private ServerConfig serverConfig;

	@Resource(name="eventStorageService")
	private EventStorageService eventStorageService;

	private GroovyScriptEngine groovyScriptEngine;

	public void startService() {
		String path = System.getProperty("user.dir") + "/ValidationScript/";
		String[] root = new String[] {path};
		try {
			groovyScriptEngine = new GroovyScriptEngine(root);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	public boolean isSpecialRule(
			String eventId,
			String sdpId,
			int dataType,
			int schemeType,
			String sourceEventType
			) {
		Optional<MdmAssetServicePoint> optionalSdp = rtMdmAssetServicePointRepository.findById(sdpId);
		if (!optionalSdp.isPresent())
			return false;
		MdmAssetServicePoint sdp = optionalSdp.get();
		String mgId = sdp.getVeeValidationGroupId();
		if (mgId == null) {
			return false;
		}				
		List<MdmAssetVEERule> rules = rtMdmAssetVEERuleRepository.findMatching(
				mgId, 
				1, 
				dataType, 
				schemeType,
				sourceEventType == null ? 0 : 1,
				sourceEventType
				);
		if (rules == null || rules.size() == 0) {
			return false;
		}
		for (MdmAssetVEERule rule : rules) {
			if (eventId.equals(rule.getEventId())) {
				return true;
			}
		}
		
		return false;
	}	

	public List<Map<String, Object>> getRuleInfos(
			String sdpId, 
			Date refTv, 
			int dataType, 
			int schemeType,
			String sourceEventType,
			Object data) {
		String logInfo = null;
		List<Map<String, Object>> ruleInfos = new ArrayList<Map<String, Object>>();
		Optional<MdmAssetServicePoint> optionalSdp = rtMdmAssetServicePointRepository.findById(sdpId);
		if (!optionalSdp.isPresent())
			return null;
		MdmAssetServicePoint sdp = optionalSdp.get();
		String mgId = sdp.getVeeValidationGroupId();
		if (mgId == null) {
			logInfo = "VEE validation group id is null[" + sdpId + "]";
			Logger.getInstance().writeLogInfo(LoggerLevel.WARN, serverConfig.getServiceId(), sdpId, "Validation", logInfo); 
			return null;
		}
		
		List<MdmAssetVEERule> rules = rtMdmAssetVEERuleRepository.findMatching(
				mgId, 
				1, 
				dataType, 
				schemeType,
				sourceEventType == null ? 0:1,
				sourceEventType
				);
		
		if (rules == null || rules.size() == 0) {
			logInfo = "No matching rule";
			Logger.getInstance().writeLogInfo(LoggerLevel.WARN, serverConfig.getServiceId(), sdpId, "Validation", logInfo); 			
			return null;
		}

		for (MdmAssetVEERule rule : rules) {
			String methodId = rule.getMethod();
			if(methodId == null || "".equals(methodId)) {
				continue;
			}

			Optional<VEEMethod> optionalMethod = rtVEEMethodRepository.findById(methodId);
			if (!optionalMethod.isPresent()) {
				logInfo = "No find method[" + rule.getName() + "]";
				Logger.getInstance().writeLogInfo(LoggerLevel.WARN, serverConfig.getServiceId(), sdpId, "Validation", logInfo); 			
				continue;
			}
			VEEMethod method = optionalMethod.get();
			String packageId = method.getPackageId();
			if (packageId == null) {
				logInfo = "No find package id[" + method.getName() + "]";
				Logger.getInstance().writeLogInfo(LoggerLevel.WARN, serverConfig.getServiceId(), sdpId, "Validation", logInfo); 							
				continue;
			}
			
			if (!packageId.startsWith("VEE/")) {
				logInfo = "No find vee package id[" + method.getName() + "]";
				Logger.getInstance().writeLogInfo(LoggerLevel.WARN, serverConfig.getServiceId(), sdpId, "Validation", logInfo); 							
				continue;
			}
			
			List<MdmAssetVEERuleDataSource> dataSources = null;
			if (rule.getMultDatasource() == 1) {
				dataSources = rtMdmAssetVEERuleDataSourceRepository.findByRuleId(rule.getId());
			} else {
				dataSources = rtMdmAssetVEERuleDataSourceRepository.findByRuleIdAndSchemeType(rule.getId(), schemeType);
			}
			
			if (dataSources == null || dataSources.size() == 0) {
				logInfo = "No find data source[" + rule.getName() + "]";
				Logger.getInstance().writeLogInfo(LoggerLevel.WARN, serverConfig.getServiceId(), sdpId, "Validation", logInfo); 							
				continue;
			}
			List<MdmAssetVEERuleParam> params = rtMdmAssetVEERuleParamRepository.findByRuleId(rule.getId());

			DataVEEEvent event = new DataVEEEvent();
			DataVEEEventPK dataVEEEventPK = event.getDataVEEEventPK();
			dataVEEEventPK.setTv(refTv);
			dataVEEEventPK.setEventId(rule.getEventId());
			dataVEEEventPK.setSchemeType(schemeType);
			dataVEEEventPK.setObjectId(sdpId);
			event.setObjectType(7);
			if (dataType == 109) {
				dataType =  106;
			}
			event.setDataType(dataType);                                
			event.setDataSource(3);


			event.setVeeEventClass(rule.getClassId());
			event.setEstimationStatus(0);

			Map<String, Object> ruleInfo = getRuleInfo(
					packageId,
					sdp,
					refTv,
					schemeType,
					data,
					event,
					sourceEventType,
					dataSources,
					params);
			if(ruleInfo != null)
				ruleInfos.add(ruleInfo);
			else {
				logInfo = "No find data[" + rule.getName() + "]";
				Logger.getInstance().writeLogInfo(LoggerLevel.WARN, serverConfig.getServiceId(), sdpId, "Validation", logInfo); 							
				continue;
			}
		}
		return ruleInfos;
	}
	
	public List<Map<String, Object>> getCalcObjRuleInfos(
			String calcObjId, 
			Date refTv, 
			int dataType, 
			int schemeType,
			String sourceEventType,
			Object data) {
		List<Map<String, Object>> ruleInfos = new ArrayList<Map<String, Object>>();
		Optional<MdmAssetCalcObj> optionalObj = rtMdmAssetCalcObjRepository.findById(calcObjId);
		if (!optionalObj.isPresent())
			return null;
		MdmAssetCalcObj obj = optionalObj.get();
		String mgId = obj.getVeeValidationGroupId();
		if (mgId == null) {
			return null;
		}		
		List<MdmAssetVEERule> rules = rtMdmAssetVEERuleRepository.findMatching(
				mgId, 
				1, 
				dataType, 
				schemeType,
				sourceEventType == null ? 0:1,
				sourceEventType
				);
		
		if (rules == null || rules.size() == 0)
			return null;

		for (MdmAssetVEERule rule : rules) {
			String methodId = rule.getMethod();
			if(methodId == null || "".equals(methodId)) {
				continue;
			}

			Optional<VEEMethod> optionalMethod = rtVEEMethodRepository.findById(methodId);
			if (!optionalMethod.isPresent())
				continue;
			VEEMethod method = optionalMethod.get();
			String packageId = method.getPackageId();
			if (packageId == null)
				continue;
			
			if (!packageId.startsWith("VEE/")) {
				continue;
			}
			
			List<MdmAssetVEERuleDataSource> dataSources = null;
			if (rule.getMultDatasource() == 1) {
				dataSources = rtMdmAssetVEERuleDataSourceRepository.findByRuleId(rule.getId());
			} else {
				dataSources = rtMdmAssetVEERuleDataSourceRepository.findByRuleIdAndSchemeType(rule.getId(), schemeType);
			}
			if (dataSources == null || dataSources.size() == 0)
				continue;
			List<MdmAssetVEERuleParam> params = rtMdmAssetVEERuleParamRepository.findByRuleId(rule.getId());

			DataVEEEvent event = new DataVEEEvent();
			DataVEEEventPK dataVEEEventPK = event.getDataVEEEventPK();

			dataVEEEventPK.setTv(refTv);
			dataVEEEventPK.setEventId(rule.getEventId());
			dataVEEEventPK.setSchemeType(schemeType);
			dataVEEEventPK.setObjectId(calcObjId);

			event.setObjectType(8);
			event.setDataType(dataType);
			event.setDataSource(3);
			event.setVeeEventClass(rule.getClassId());
			event.setEstimationStatus(0);

			Map<String, Object> ruleInfo = new HashMap<String, Object>();
			List<Map<String, Object>> ruleDatas = new ArrayList<Map<String, Object>>();
			Map<String, Object> ruleParams = new HashMap<String, Object>();
			
			ruleInfo.put("packageId", packageId);	
			ruleInfo.put("event", event);	
			ruleInfo.put("datas", ruleDatas);
			ruleInfo.put("params", ruleParams);
			
			DataCalcObj curCalcObj = new DataCalcObj();
			BeanUtils.copyProperties(data, curCalcObj);
			
			Map<String, Map<String, Object>> mapTargetClass = new HashMap<String, Map<String, Object>>();
						
			for (MdmAssetVEERuleDataSource dataSource : dataSources) {
				String code = dataSource.getDataCode();
				if (code == null)
					continue;
				String[] splitCode = code.split("\\.");
				if (splitCode.length != 2)
					continue;	
				List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
				Map<String, Object> mapData = new HashMap<String, Object>();
				mapData.put("inTotal", curCalcObj.getInTotal());
				mapData.put("outTotal", curCalcObj.getOutTotal());
				mapData.put("calcValue", curCalcObj.getCalcValue());
				mapData.put("targetClass", "LOSS");
				dataList.add(mapData);				
				
				Map<String, Object> ruleData = null;
				if (mapTargetClass.containsKey(splitCode[0])) {
					ruleData = mapTargetClass.get(splitCode[0]);
				}
				if (ruleData == null) {
					ruleData = new HashMap<String, Object>();
					ruleDatas.add(ruleData);		
					ruleData.put("targetClass", splitCode[0]);
					mapTargetClass.put(splitCode[0], ruleData);
				}
				
				ruleData.put(splitCode[1], dataList);
			}
			
			for (MdmAssetVEERuleParam param : params) {
				ruleParams.put(param.getParamKey(), param.getParamValue());
			}	
			
			ruleInfos.add(ruleInfo);
		}
		return ruleInfos;		
	}
	
	private Map<String, Object> getRuleInfo(
			String packageId, 
			MdmAssetServicePoint sdp,
			Date refTv,
			int schemeType,
			Object data,
			DataVEEEvent event,
			String sourceEventType,
			List<MdmAssetVEERuleDataSource> dataSources,
			List<MdmAssetVEERuleParam> params) {
			
		Map<String, Object> ruleInfo = new HashMap<String, Object>();
		List<Map<String, Object>> ruleDatas = new ArrayList<Map<String, Object>>();
		Map<String, Object> ruleParams = new HashMap<String, Object>();

		/********************************************************/
		//数据周期加入进去
		ruleInfo.put("schemeType",schemeType);
		/********************************************************/
		ruleInfo.put("packageId", packageId);	
		ruleInfo.put("event", event);	
		ruleInfo.put("datas", ruleDatas);
		ruleInfo.put("params", ruleParams);
		ruleInfo.put("sourceEventType", sourceEventType);
		Map<String, Map<String, Object>> mapTargetClass = new HashMap<String, Map<String, Object>>();
		
		for (MdmAssetVEERuleDataSource dataSource : dataSources) {
			List<Map<String, Object>> dataList = getDataList(
					dataSource, sdp, refTv, schemeType, data);
			if(dataList == null || dataList.size() == 0) {
				return null;
			}
			String code = dataSource.getDataCode();
			if (code == null)
				continue;
			String[] splitCode = code.split("\\.");
			if (splitCode.length != 2)
				continue;
			Map<String, Object> ruleData = null;
			if (mapTargetClass.containsKey(splitCode[0])) {
				ruleData = mapTargetClass.get(splitCode[0]);
			}
			if (ruleData == null) {
				ruleData = new HashMap<String, Object>();
				ruleDatas.add(ruleData);		
				ruleData.put("targetClass", splitCode[0]);
				mapTargetClass.put(splitCode[0], ruleData);
			}
			
			ruleData.put(splitCode[1], dataList);
		}
		
		for (MdmAssetVEERuleParam param : params) {
			ruleParams.put(param.getParamKey(), param.getParamValue());
		}	
		return ruleInfo;
	}
	
	private List<Map<String, Object>> getDataList(
			MdmAssetVEERuleDataSource dataSource, 
			MdmAssetServicePoint sdp,
			Date refTv,
			int refSchemeType,
			Object refData) {
		String objectId = sdp.getId();
		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
		int schemeType = dataSource.getSchemeType();
		int dataType = dataSource.getDataType();
		
		List<Date> tvs = null;
		int cycleCount = 0;
		if (dataSource.getCycleType() != null) {
			int field = Calendar.YEAR;
			if (dataSource.getCycleType() == 1) {
				field = Calendar.MINUTE;
			} else if (dataSource.getCycleType() == 2) {
				field = Calendar.DAY_OF_MONTH;
			} else if (dataSource.getCycleType() == 3) {
				field = Calendar.MONTH;
			}

			int amount = dataSource.getStartCycle();
			cycleCount = dataSource.getCycleCount();
			int dataCycle = 1;
			if (dataSource.getCycleType() == 1) {
				if (dataType == 101) {
					if (sdp.getRegisterScheme() == null) {
						return null;
					}
					dataCycle = sdp.getRegisterScheme();
				}
				if (dataType == 102) {
					if (sdp.getIntervalScheme() == null) {
						return null;
					}
					dataCycle = sdp.getIntervalScheme();
				} else if (dataType == 103) {
					if (sdp.getInstScheme() == null) {
						return null;
					}					
					dataCycle = sdp.getInstScheme();
				}
			}
			amount = amount * dataCycle;

			Calendar cal = Calendar.getInstance();
			cal.setTime(refTv);
			cal.add(field, amount);
			tvs = new ArrayList<Date>();
			tvs.add(cal.getTime());
			for (int i = 1; i < cycleCount; i++) {
				cal.add(field, 1*dataCycle);
				tvs.add(cal.getTime());
			}
		}
		
		List<String> filters = new ArrayList<String>();
		filters.add("dataRegPK");
		filters.add("dataInstPK");
		filters.add("timeType");
		filters.add("updateTv");

		switch (dataType) {
		case 101:		
			if (schemeType == 1) {
				DataRegMinutely curData = null;
				if (schemeType == refSchemeType) {
					curData = new DataRegMinutely();
					BeanUtils.copyProperties(refData, curData);
					try {
						rtDataRegMinutelyRepository.save(curData);
					} catch (Exception e) {

					}
				}
				List<DataRegMinutely> datas = null;
				datas = rtDataRegMinutelyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
				if (datas.size() < cycleCount) {
					datas = dataRegMinutelyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
					try {
						rtDataRegMinutelyRepository.saveAll(datas);
					} catch(Exception e) {
						
					}
					datas = rtDataRegMinutelyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
				}
			 
				if (datas.size() < cycleCount) {
					return null;
				}
				for (DataRegMinutely data : datas) {
					Map<String, Object> mapData = ObjectUtils.transBean2MapFilter(data, filters);
					mapData.put("targetClass", "REG");
					dataList.add(mapData);
				}
			}			
			else if (dataSource.getSchemeType() == 2) {
				DataRegDaily curData = null;
				if (schemeType == refSchemeType) {
					curData = new DataRegDaily();
					BeanUtils.copyProperties(refData, curData);

					try {
						rtDataRegDailyRepository.save(curData);
					} catch (Exception e) {

					}
				}
				
				List<DataRegDaily> datas = null;

				datas = rtDataRegDailyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
				if (datas.size() < cycleCount) {
					datas = dataRegDailyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
					try {
						rtDataRegDailyRepository.saveAll(datas);
					} catch(Exception e) {
						
					}
					datas = rtDataRegDailyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
				}

				if (datas.size() < cycleCount) {
					return null;
				}
				for (DataRegDaily data : datas) {
					Map<String, Object> mapData = ObjectUtils.transBean2MapFilter(data, filters);
					mapData.put("targetClass", "REG");
					dataList.add(mapData);
				}
			} 
			else if (dataSource.getSchemeType() == 3) {
				DataRegMonthly curData = null;
				if (schemeType == refSchemeType) {
					curData = new DataRegMonthly();
					BeanUtils.copyProperties(refData, curData);

					try {
						rtDataRegMonthlyRepository.save(curData);
					} catch (Exception e) {

					}
				}

				List<DataRegMonthly> datas = null;

				datas = rtDataRegMonthlyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
				if (datas.size() < cycleCount) {
					datas = dataRegMonthlyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
					try {
						rtDataRegMonthlyRepository.saveAll(datas);
					} catch(Exception e) {
						
					}
					datas = rtDataRegMonthlyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
				}

				if (datas.size() < cycleCount) {
					return null;
				}
				for (DataRegMonthly data : datas) {
					Map<String, Object> mapData = ObjectUtils.transBean2MapFilter(data, filters);
					mapData.put("targetClass", "REG");
					dataList.add(mapData);
				}
			}
		break;
		
	case 102:
		if (dataSource.getSchemeType() == 1) {
			DataIntervalMinutely curData = null;
			if (schemeType == refSchemeType) {
				curData = new DataIntervalMinutely();
				BeanUtils.copyProperties(refData, curData);

				try {
					rtDataIntervalMinutelyRepository.save(curData);
				} catch (Exception e) {

				}
			}
			List<DataIntervalMinutely> datas = null;

			datas = rtDataIntervalMinutelyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
			if (datas.size() < cycleCount) {
				datas = dataIntervalMinutelyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
				try {
					rtDataIntervalMinutelyRepository.saveAll(datas);
				} catch(Exception e) {
					
				}
				datas = rtDataIntervalMinutelyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
			}

			if (datas.size() < cycleCount) {
				return null;
			}
			for (DataIntervalMinutely data : datas) {
				Map<String, Object> mapData = ObjectUtils.transBean2MapFilter(data, filters);
				mapData.put("targetClass", "INTERVAL");
				dataList.add(mapData);
			}
		} 
		else if (dataSource.getSchemeType() == 2) {
			DataIntervalDaily curData = null;
			if (schemeType == refSchemeType) {
				curData = new DataIntervalDaily();
				BeanUtils.copyProperties(refData, curData);

				try {
					rtDataIntervalDailyRepository.save(curData);
				} catch (Exception e) {

				}
			}
			List<DataIntervalDaily> datas = null;

			datas = rtDataIntervalDailyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
			if (datas.size() < cycleCount) {
				datas = dataIntervalDailyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
				try {
					rtDataIntervalDailyRepository.saveAll(datas);
				} catch (Exception e) {
					
				}
				datas = rtDataIntervalDailyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
			}

			if (datas.size() < cycleCount) {
				return null;
			}
			for (DataIntervalDaily data : datas) {
				Map<String, Object> mapData = ObjectUtils.transBean2MapFilter(data, filters);
				mapData.put("targetClass", "INTERVAL");
				dataList.add(mapData);
			}
		} 
		else if (dataSource.getSchemeType() == 3) {
			DataIntervalMonthly curData = null;
			if (schemeType == refSchemeType) {
				curData = new DataIntervalMonthly();
				BeanUtils.copyProperties(refData, curData);
				try {
					rtDataIntervalMonthlyRepository.save(curData);
				} catch (Exception e) {

				}
			}
			
			List<DataIntervalMonthly> datas = null;
			datas = rtDataIntervalMonthlyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
			if (datas.size() < cycleCount) {
				datas = dataIntervalMonthlyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
				try {
					rtDataIntervalMonthlyRepository.saveAll(datas);
				} catch(Exception e) {
					
				}
				datas = rtDataIntervalMonthlyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
			}

			if (datas.size() < cycleCount) {
				return null;
			}
			for (DataIntervalMonthly data : datas) {
				Map<String, Object> mapData = ObjectUtils.transBean2MapFilter(data, filters);
				mapData.put("targetClass", "INTERVAL");
				dataList.add(mapData);
			}
		}
		break;			

		case 103: {
			DataInstMinutely curData = null;
			if (schemeType == refSchemeType) {
				curData = new DataInstMinutely();
				BeanUtils.copyProperties(refData, curData);
				try {
					rtDataInstMinutelyRepository.save(curData);
				} catch (Exception e) {

				}
			}
			
			List<DataInstMinutely> datas = null;
			datas = rtDataInstMinutelyRepository.findByDataInstPKSdpIdAndDataInstPKDataTypeAndDataInstPKTvIn(objectId, 0, tvs);
			if (datas.size() < cycleCount) {
				datas = dataInstMinutelyRepository.findByDataInstPKSdpIdAndDataInstPKDataTypeAndDataInstPKTvIn(objectId, 0, tvs);
				try {
					rtDataInstMinutelyRepository.saveAll(datas);
				} catch(Exception e) {
					
				}
				datas = rtDataInstMinutelyRepository.findByDataInstPKSdpIdAndDataInstPKDataTypeAndDataInstPKTvIn(objectId, 0, tvs);
			}
	
			if (datas.size() < cycleCount) {
				return null;
			}
			for (DataInstMinutely data : datas) {
				Map<String, Object> mapData = ObjectUtils.transBean2MapFilter(data, filters);
				mapData.put("targetClass", "INST");
				dataList.add(mapData);
			}
		}
		break;
		case 106:
		case 109:
			DataVEEEvent curVEEEvent = new DataVEEEvent();
			BeanUtils.copyProperties(refData, curVEEEvent);	
			List<DataVEEEvent> veeEvents = null;

			veeEvents = new ArrayList<DataVEEEvent>();
			veeEvents.add(curVEEEvent);
			
			List<String> propNames = new ArrayList<>();
			propNames.add("tv");
			for (DataVEEEvent veeEvent : veeEvents) {
				Map<String, Object> mapData = new HashMap<String, Object>();
				mapData.put("tv", veeEvent.getDataVEEEventPK().getTv().getTime());
				mapData.put("value", 1);
				mapData.put("targetClass", "EVENT");
				dataList.add(mapData);				
			}
			break;	
		}
		return dataList;
	}

	public Map<String, Object> invokeRule(String accessToken, Map<String, Object> ruleInfo){
		String packageId = ruleInfo.get("packageId").toString();
		String[] splitPackageIds = packageId.split("/");
		String scriptName = splitPackageIds[1] + ".groovy";
		String methodName = splitPackageIds[1];
		DataVEEEvent event = invokeMethod(scriptName, methodName, ruleInfo);
		if(event != null) {
			List<String> filters = new ArrayList<String>();
			filters.add("dataVEEEventPK");
			Map<String, Object> retMap = ObjectUtils.transBean2MapFilterSelf(event, filters);
			Map<String, Object> retMapPK = ObjectUtils.transBean2MapFilterSelf(event.getDataVEEEventPK(), filters);
			retMap.putAll(retMapPK);
			return retMap;
		} else {
			return null;
		}
	}

	public void invokeRules(List<Map<String, Object>> ruleInfos, String sdpId) {
		if (ruleInfos == null) {
			return;
		}
		for(Map<String, Object> ruleInfo : ruleInfos) {
			String packageId = ruleInfo.get("packageId").toString();
			String[] splitPackageIds = packageId.split("/");
			String scriptName = splitPackageIds[1] + ".groovy";
			String methodName = splitPackageIds[1];

			DataVEEEvent event = invokeMethod(scriptName, methodName, ruleInfo);
			if (event != null) {
				eventStorageService.putEvent(event, null);
			}
		}
	}

	public DataVEEEvent invokeMethod(String scriptName, String methodName, Object... params) {
		DataVEEEvent retEvent = null;
		long startTime = System.currentTimeMillis();
		try {
			Class scriptClass = groovyScriptEngine.loadScriptByName(scriptName);
			GroovyObject scriptInstance = (GroovyObject) scriptClass.newInstance();
			retEvent = (DataVEEEvent)scriptInstance.invokeMethod(methodName, params);
		} catch (Exception e) {
			e.printStackTrace();
		}
		Log.info(methodName + ": taking[" + (System.currentTimeMillis() - startTime) + "ms]");
		return retEvent;
	}
}
