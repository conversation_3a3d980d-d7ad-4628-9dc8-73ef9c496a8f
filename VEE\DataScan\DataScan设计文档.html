<!DOCTYPE html><html><head>
      <title>DataScan 模块设计文档</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      
      <link rel="stylesheet" href="file:///c:\Users\<USER>\.vscode-insiders\extensions\shd101wyy.markdown-preview-enhanced-0.8.18\crossnote\dependencies\katex\katex.min.css">
      
      
      <script type="text/javascript" src="file:///c:\Users\<USER>\.vscode-insiders\extensions\shd101wyy.markdown-preview-enhanced-0.8.18\crossnote\dependencies\mermaid\mermaid.min.js" charset="UTF-8"></script>
      
      
      <style>
      code[class*=language-],pre[class*=language-]{color:#333;background:0 0;font-family:Consolas,"Liberation Mono",Menlo,Courier,monospace;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.4;-moz-tab-size:8;-o-tab-size:8;tab-size:8;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*=language-]{padding:.8em;overflow:auto;border-radius:3px;background:#f5f5f5}:not(pre)>code[class*=language-]{padding:.1em;border-radius:.3em;white-space:normal;background:#f5f5f5}.token.blockquote,.token.comment{color:#969896}.token.cdata{color:#183691}.token.doctype,.token.macro.property,.token.punctuation,.token.variable{color:#333}.token.builtin,.token.important,.token.keyword,.token.operator,.token.rule{color:#a71d5d}.token.attr-value,.token.regex,.token.string,.token.url{color:#183691}.token.atrule,.token.boolean,.token.code,.token.command,.token.constant,.token.entity,.token.number,.token.property,.token.symbol{color:#0086b3}.token.prolog,.token.selector,.token.tag{color:#63a35c}.token.attr-name,.token.class,.token.class-name,.token.function,.token.id,.token.namespace,.token.pseudo-class,.token.pseudo-element,.token.url-reference .token.variable{color:#795da3}.token.entity{cursor:help}.token.title,.token.title .token.punctuation{font-weight:700;color:#1d3e81}.token.list{color:#ed6a43}.token.inserted{background-color:#eaffea;color:#55a532}.token.deleted{background-color:#ffecec;color:#bd2c00}.token.bold{font-weight:700}.token.italic{font-style:italic}.language-json .token.property{color:#183691}.language-markup .token.tag .token.punctuation{color:#333}.language-css .token.function,code.language-css{color:#0086b3}.language-yaml .token.atrule{color:#63a35c}code.language-yaml{color:#183691}.language-ruby .token.function{color:#333}.language-markdown .token.url{color:#795da3}.language-makefile .token.symbol{color:#795da3}.language-makefile .token.variable{color:#183691}.language-makefile .token.builtin{color:#0086b3}.language-bash .token.keyword{color:#0086b3}pre[data-line]{position:relative;padding:1em 0 1em 3em}pre[data-line] .line-highlight-wrapper{position:absolute;top:0;left:0;background-color:transparent;display:block;width:100%}pre[data-line] .line-highlight{position:absolute;left:0;right:0;padding:inherit 0;margin-top:1em;background:hsla(24,20%,50%,.08);background:linear-gradient(to right,hsla(24,20%,50%,.1) 70%,hsla(24,20%,50%,0));pointer-events:none;line-height:inherit;white-space:pre}pre[data-line] .line-highlight:before,pre[data-line] .line-highlight[data-end]:after{content:attr(data-start);position:absolute;top:.4em;left:.6em;min-width:1em;padding:0 .5em;background-color:hsla(24,20%,50%,.4);color:#f4f1ef;font:bold 65%/1.5 sans-serif;text-align:center;vertical-align:.3em;border-radius:999px;text-shadow:none;box-shadow:0 1px #fff}pre[data-line] .line-highlight[data-end]:after{content:attr(data-end);top:auto;bottom:.4em}html body{font-family:'Helvetica Neue',Helvetica,'Segoe UI',Arial,freesans,sans-serif;font-size:16px;line-height:1.6;color:#333;background-color:#fff;overflow:initial;box-sizing:border-box;word-wrap:break-word}html body>:first-child{margin-top:0}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{line-height:1.2;margin-top:1em;margin-bottom:16px;color:#000}html body h1{font-size:2.25em;font-weight:300;padding-bottom:.3em}html body h2{font-size:1.75em;font-weight:400;padding-bottom:.3em}html body h3{font-size:1.5em;font-weight:500}html body h4{font-size:1.25em;font-weight:600}html body h5{font-size:1.1em;font-weight:600}html body h6{font-size:1em;font-weight:600}html body h1,html body h2,html body h3,html body h4,html body h5{font-weight:600}html body h5{font-size:1em}html body h6{color:#5c5c5c}html body strong{color:#000}html body del{color:#5c5c5c}html body a:not([href]){color:inherit;text-decoration:none}html body a{color:#08c;text-decoration:none}html body a:hover{color:#00a3f5;text-decoration:none}html body img{max-width:100%}html body>p{margin-top:0;margin-bottom:16px;word-wrap:break-word}html body>ol,html body>ul{margin-bottom:16px}html body ol,html body ul{padding-left:2em}html body ol.no-list,html body ul.no-list{padding:0;list-style-type:none}html body ol ol,html body ol ul,html body ul ol,html body ul ul{margin-top:0;margin-bottom:0}html body li{margin-bottom:0}html body li.task-list-item{list-style:none}html body li>p{margin-top:0;margin-bottom:0}html body .task-list-item-checkbox{margin:0 .2em .25em -1.8em;vertical-align:middle}html body .task-list-item-checkbox:hover{cursor:pointer}html body blockquote{margin:16px 0;font-size:inherit;padding:0 15px;color:#5c5c5c;background-color:#f0f0f0;border-left:4px solid #d6d6d6}html body blockquote>:first-child{margin-top:0}html body blockquote>:last-child{margin-bottom:0}html body hr{height:4px;margin:32px 0;background-color:#d6d6d6;border:0 none}html body table{margin:10px 0 15px 0;border-collapse:collapse;border-spacing:0;display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all}html body table th{font-weight:700;color:#000}html body table td,html body table th{border:1px solid #d6d6d6;padding:6px 13px}html body dl{padding:0}html body dl dt{padding:0;margin-top:16px;font-size:1em;font-style:italic;font-weight:700}html body dl dd{padding:0 16px;margin-bottom:16px}html body code{font-family:Menlo,Monaco,Consolas,'Courier New',monospace;font-size:.85em;color:#000;background-color:#f0f0f0;border-radius:3px;padding:.2em 0}html body code::after,html body code::before{letter-spacing:-.2em;content:'\00a0'}html body pre>code{padding:0;margin:0;word-break:normal;white-space:pre;background:0 0;border:0}html body .highlight{margin-bottom:16px}html body .highlight pre,html body pre{padding:1em;overflow:auto;line-height:1.45;border:#d6d6d6;border-radius:3px}html body .highlight pre{margin-bottom:0;word-break:normal}html body pre code,html body pre tt{display:inline;max-width:initial;padding:0;margin:0;overflow:initial;line-height:inherit;word-wrap:normal;background-color:transparent;border:0}html body pre code:after,html body pre code:before,html body pre tt:after,html body pre tt:before{content:normal}html body blockquote,html body dl,html body ol,html body p,html body pre,html body ul{margin-top:0;margin-bottom:16px}html body kbd{color:#000;border:1px solid #d6d6d6;border-bottom:2px solid #c7c7c7;padding:2px 4px;background-color:#f0f0f0;border-radius:3px}@media print{html body{background-color:#fff}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{color:#000;page-break-after:avoid}html body blockquote{color:#5c5c5c}html body pre{page-break-inside:avoid}html body table{display:table}html body img{display:block;max-width:100%;max-height:100%}html body code,html body pre{word-wrap:break-word;white-space:pre}}.markdown-preview{width:100%;height:100%;box-sizing:border-box}.markdown-preview ul{list-style:disc}.markdown-preview ul ul{list-style:circle}.markdown-preview ul ul ul{list-style:square}.markdown-preview ol{list-style:decimal}.markdown-preview ol ol,.markdown-preview ul ol{list-style-type:lower-roman}.markdown-preview ol ol ol,.markdown-preview ol ul ol,.markdown-preview ul ol ol,.markdown-preview ul ul ol{list-style-type:lower-alpha}.markdown-preview .newpage,.markdown-preview .pagebreak{page-break-before:always}.markdown-preview pre.line-numbers{position:relative;padding-left:3.8em;counter-reset:linenumber}.markdown-preview pre.line-numbers>code{position:relative}.markdown-preview pre.line-numbers .line-numbers-rows{position:absolute;pointer-events:none;top:1em;font-size:100%;left:0;width:3em;letter-spacing:-1px;border-right:1px solid #999;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.markdown-preview pre.line-numbers .line-numbers-rows>span{pointer-events:none;display:block;counter-increment:linenumber}.markdown-preview pre.line-numbers .line-numbers-rows>span:before{content:counter(linenumber);color:#999;display:block;padding-right:.8em;text-align:right}.markdown-preview .mathjax-exps .MathJax_Display{text-align:center!important}.markdown-preview:not([data-for=preview]) .code-chunk .code-chunk-btn-group{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .status{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .output-div{margin-bottom:16px}.markdown-preview .md-toc{padding:0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link div,.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}.markdown-preview .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}.scrollbar-style::-webkit-scrollbar{width:8px}.scrollbar-style::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}.scrollbar-style::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode]){position:relative;width:100%;height:100%;top:0;left:0;margin:0;padding:0;overflow:auto}html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{position:relative;top:0;min-height:100vh}@media screen and (min-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em calc(50% - 457px + 2em)}}@media screen and (max-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{font-size:14px!important;padding:1em}}@media print{html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{display:none}}html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{position:fixed;bottom:8px;left:8px;font-size:28px;cursor:pointer;color:inherit;z-index:99;width:32px;text-align:center;opacity:.4}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] #sidebar-toc-btn{opacity:1}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc{position:fixed;top:0;left:0;width:300px;height:100%;padding:32px 0 48px 0;font-size:14px;box-shadow:0 0 4px rgba(150,150,150,.33);box-sizing:border-box;overflow:auto;background-color:inherit}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar{width:8px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc a{text-decoration:none}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc{padding:0 16px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link div,html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{left:300px;width:calc(100% - 300px);padding:2em calc(50% - 457px - 300px / 2);margin:0;box-sizing:border-box}@media screen and (max-width:1274px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{width:100%}}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .markdown-preview{left:50%;transform:translateX(-50%)}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .md-sidebar-toc{display:none}
/* Please visit the URL below for more information: */
/*   https://shd101wyy.github.io/markdown-preview-enhanced/#/customize-css */

      </style>
      <!-- The content below will be included at the end of the <head> element. --><script type="text/javascript">
  document.addEventListener("DOMContentLoaded", function () {
    // your code here
  });
</script></head><body for="html-export">
    
    
      <div class="crossnote markdown-preview  ">
      
<h1 id="datascan-模块设计文档">DataScan 模块设计文档 </h1>
<h2 id="1-项目概述">1. 项目概述 </h2>
<h3 id="11-项目背景">1.1 项目背景 </h3>
<p>DataScan是ClouESP平台的核心组件之一，属于主数据管理(MDM)系统的验证、估算和编辑(VEE)子系统的数据扫描模块。该模块负责定期扫描数据库中的各类电力数据记录和事件，并将其发送到消息队列以供其他服务处理和分析。</p>
<h3 id="12-项目目标">1.2 项目目标 </h3>
<ul>
<li>定期扫描不同时间粒度的数据记录(分钟级、日级、月级)</li>
<li>定期扫描VEE事件记录</li>
<li>将扫描到的数据通过消息队列发送至其他模块进行处理</li>
<li>支持Oracle和MySQL两种数据库系统</li>
<li>保证数据扫描的高效性和可靠性</li>
</ul>
<h3 id="13-系统版本">1.3 系统版本 </h3>
<p>当前版本: 2024092310</p>
<h2 id="2-技术架构">2. 技术架构 </h2>
<h3 id="21-开发技术">2.1 开发技术 </h3>
<table>
<thead>
<tr>
<th>技术类型</th>
<th>使用技术</th>
<th>版本</th>
<th>说明</th>
</tr>
</thead>
<tbody>
<tr>
<td>编程语言</td>
<td>Java</td>
<td>1.8</td>
<td>核心开发语言</td>
</tr>
<tr>
<td>框架</td>
<td>Spring Boot</td>
<td>2.4.0</td>
<td>应用开发框架</td>
</tr>
<tr>
<td>ORM框架</td>
<td>MyBatis</td>
<td>2.1.4</td>
<td>数据库对象映射框架</td>
</tr>
<tr>
<td>消息队列</td>
<td>RocketMQ</td>
<td>-</td>
<td>系统间通信</td>
</tr>
<tr>
<td>数据库</td>
<td>H2/Oracle/MySQL</td>
<td>-</td>
<td>支持多种数据库系统</td>
</tr>
<tr>
<td>API文档</td>
<td>Swagger</td>
<td>2.9.2</td>
<td>API接口文档</td>
</tr>
<tr>
<td>脚本语言</td>
<td>Groovy</td>
<td>3.0.2</td>
<td>业务规则扩展</td>
</tr>
</tbody>
</table>
<h3 id="22-系统架构">2.2 系统架构 </h3>
<p>DataScan作为独立的微服务组件运行，其系统架构如下：</p>
<pre data-role="codeBlock" data-info="" class="language-text"><code>+----------------+      +----------------+      +----------------+
|                |      |                |      |                |
|  关系型数据库   |&lt;----&gt;|    DataScan    |&lt;----&gt;|    RocketMQ    |
| (MySQL/Oracle) |      |     服务       |      |   消息队列     |
|                |      |                |      |                |
+----------------+      +----------------+      +----------------+
                               ^
                               |
                               v
                        +----------------+
                        |                |
                        |  H2内存数据库  |
                        |                |
                        +----------------+
</code></pre><h3 id="23-项目依赖">2.3 项目依赖 </h3>
<p>项目的主要依赖包括：</p>
<ul>
<li>
<p><strong>Spring Boot相关</strong></p>
<ul>
<li>spring-boot-starter-web</li>
<li>spring-boot-starter-data-jpa</li>
<li>spring-boot-configuration-processor</li>
<li>spring-boot-starter-test</li>
</ul>
</li>
<li>
<p><strong>数据库驱动</strong></p>
<ul>
<li>h2</li>
<li>ojdbc8 (Oracle)</li>
<li>mysql-connector-java</li>
</ul>
</li>
<li>
<p><strong>MyBatis</strong></p>
<ul>
<li>mybatis-spring-boot-starter</li>
</ul>
</li>
<li>
<p><strong>文档</strong></p>
<ul>
<li>springfox-swagger2</li>
<li>springfox-swagger-ui</li>
</ul>
</li>
<li>
<p><strong>内部依赖模块</strong></p>
<ul>
<li>CommonUtils</li>
<li>DataRepository</li>
<li>DataModel</li>
<li>MqBus</li>
<li>Logger</li>
</ul>
</li>
</ul>
<h2 id="3-系统设计">3. 系统设计 </h2>
<h3 id="31-模块划分">3.1 模块划分 </h3>
<p>DataScan系统划分为以下主要模块：</p>
<table>
<thead>
<tr>
<th>模块名称</th>
<th>路径</th>
<th>功能描述</th>
</tr>
</thead>
<tbody>
<tr>
<td>配置模块</td>
<td>config/</td>
<td>负责系统配置的加载和管理</td>
</tr>
<tr>
<td>控制器模块</td>
<td>controller/</td>
<td>提供对外HTTP接口</td>
</tr>
<tr>
<td>数据访问模块</td>
<td>dao/</td>
<td>处理与数据库的交互</td>
</tr>
<tr>
<td>定时任务模块</td>
<td>schedule/</td>
<td>负责定时执行扫描任务</td>
</tr>
<tr>
<td>服务模块</td>
<td>service/</td>
<td>包含核心业务逻辑</td>
</tr>
<tr>
<td>工具模块</td>
<td>Utils/</td>
<td>提供通用工具类</td>
</tr>
<tr>
<td>应用入口</td>
<td>DataScanApplication.java</td>
<td>系统启动入口</td>
</tr>
<tr>
<td>初始化</td>
<td>InitCommandLineRunner.java</td>
<td>启动时运行的初始化任务</td>
</tr>
</tbody>
</table>
<h3 id="32-功能设计">3.2 功能设计 </h3>
<h4 id="321-数据扫描功能">3.2.1 数据扫描功能 </h4>
<p>系统能够扫描以下类型的数据：</p>
<ol>
<li>
<p><strong>月度数据</strong></p>
<ul>
<li>数据表：mdm_data_reg_monthly</li>
<li>扫描范围：过去6个月</li>
</ul>
</li>
<li>
<p><strong>日度数据</strong></p>
<ul>
<li>数据表：mdm_data_reg_dayly</li>
<li>扫描范围：过去30天</li>
</ul>
</li>
<li>
<p><strong>分钟级数据</strong></p>
<ul>
<li>数据表：mdm_data_reg_minutely</li>
<li>扫描范围：过去7天</li>
</ul>
</li>
<li>
<p><strong>VEE事件数据</strong></p>
<ul>
<li>数据表：mdm_data_vee_event</li>
<li>扫描范围：过去1个月</li>
</ul>
</li>
</ol>
<p><strong>扫描流程</strong>：</p>
<ol>
<li>根据配置的时间范围确定扫描的起始时间</li>
<li>根据数据库类型选择不同的查询策略(Oracle或MySQL)</li>
<li>分批次(每批100000条)查询数据</li>
<li>将查询到的数据转换为对应的实体对象(DataReg或DataVEEEvent)</li>
<li>通过RocketMQ消息队列发送给其他服务处理</li>
</ol>
<h4 id="322-服务状态维护功能">3.2.2 服务状态维护功能 </h4>
<p>系统定期(每60秒)更新自身在系统服务表中的状态信息，确保系统监控正常。主要流程包括：</p>
<ol>
<li>从实时数据库查询服务信息</li>
<li>更新服务状态和在线时间</li>
<li>保存更新后的服务信息</li>
</ol>
<h4 id="323-日志功能">3.2.3 日志功能 </h4>
<p>系统集成了自定义日志系统，包含以下功能：</p>
<ul>
<li>将日志写入文件系统</li>
<li>支持不同的日志级别(INFO, ERROR等)</li>
<li>记录系统操作和异常信息</li>
<li>日志路径配置和管理</li>
</ul>
<h3 id="33-数据模型">3.3 数据模型 </h3>
<p>主要涉及以下数据实体：</p>
<ol>
<li>
<p><strong>DataReg</strong></p>
<ul>
<li>功能：表示各类数据记录(分钟级、日级、月级)</li>
<li>主要属性：sdpId, tv, timeType等</li>
</ul>
</li>
<li>
<p><strong>DataVEEEvent</strong></p>
<ul>
<li>功能：表示VEE事件记录</li>
<li>主要属性：objectId, tv, eventId, schemeType等</li>
</ul>
</li>
<li>
<p><strong>DataCalcProgress</strong></p>
<ul>
<li>功能：表示数据计算进度记录</li>
<li>主要属性：sdpId, schemeId, loadType, tv等</li>
</ul>
</li>
<li>
<p><strong>MdmSysService</strong></p>
<ul>
<li>功能：表示系统服务信息</li>
<li>主要属性：id, serviceType, serverAddress, onlineTime等</li>
</ul>
</li>
</ol>
<h3 id="34-消息队列设计">3.4 消息队列设计 </h3>
<p>使用RocketMQ作为消息队列，主要配置：</p>
<ul>
<li>Producer Group: VEE_IMPORT</li>
<li>Topic: VEE_DATAIMPORT</li>
<li>Tags: VEE_DATA</li>
</ul>
<p>发送的消息包含：</p>
<ul>
<li>加载类型(loadType)：Register或VeeEvent</li>
<li>源服务ID(fromServiceId)</li>
<li>源服务类型(fromServiceType)：DATAIMPORT</li>
<li>源ID(fromId)：sdpId</li>
<li>源类型(fromType)：SDPID</li>
<li>数据负载(load)：具体的数据对象</li>
</ul>
<h3 id="35-业务流程图">3.5 业务流程图 </h3>
<h4 id="351-系统总体业务流程">3.5.1 系统总体业务流程 </h4>
<div class="mermaid">graph TD
    A[系统启动] --&gt; B[初始化服务]
    B --&gt; C[启动日志系统]
    C --&gt; D[加载配置信息]
    D --&gt; E[注册并启动定时任务]
    E --&gt; F[更新服务状态]
    F --&gt; G[定时执行数据扫描]
    
    G --&gt; H{是否有新数据?}
    H --&gt;|是| I[处理数据并发送到MQ]
    H --&gt;|否| J[等待下次扫描]
    I --&gt; J
    J --&gt; G
</div><h4 id="352-数据扫描流程">3.5.2 数据扫描流程 </h4>
<div class="mermaid">graph TD
    A[开始扫描] --&gt; B{检查数据库类型}
    B --&gt;|Oracle| C[使用Oracle查询策略]
    B --&gt;|MySQL| D[使用MySQL查询策略]
    
    C --&gt; E[扫描月度数据]
    D --&gt; E
    E --&gt; F[扫描日度数据]
    F --&gt; G[扫描分钟级数据]
    G --&gt; H[扫描VEE事件数据]
    
    H --&gt; I[结束扫描]
</div><h4 id="353-数据处理流程">3.5.3 数据处理流程 </h4>
<div class="mermaid">graph TD
    A[获取数据批次] --&gt; B[转换为实体对象]
    B --&gt; C{是否已处理过?}
    C --&gt;|否| D[构建消息]
    C --&gt;|是| E[跳过数据]
    D --&gt; F[发送到RocketMQ]
    E --&gt; G[处理下一批]
    F --&gt; G
    G --&gt; H{是否还有更多批次?}
    H --&gt;|是| A
    H --&gt;|否| I[完成数据处理]
</div><h4 id="354-服务状态维护流程">3.5.4 服务状态维护流程 </h4>
<div class="mermaid">graph TD
    A[启动服务状态定时器] --&gt; B[每60秒触发一次]
    B --&gt; C[从实时数据库获取服务信息]
    C --&gt; D[更新服务状态和在线时间]
    D --&gt; E[保存更新后的服务信息]
    E --&gt; B
</div><h4 id="355-与其他系统的交互流程">3.5.5 与其他系统的交互流程 </h4>
<div class="mermaid">graph TD
    subgraph 数据源
        A1[Oracle数据库]
        A2[MySQL数据库]
    end
    
    subgraph DataScan服务
        B1[数据扫描模块]
        B2[消息发送模块]
        B3[服务状态管理]
    end
    
    subgraph 消息队列
        C1[RocketMQ]
    end
    
    subgraph 其他服务
        D1[DataCalc服务]
        D2[其他消费者服务]
    end
    
    A1 --&gt; B1
    A2 --&gt; B1
    B1 --&gt; B2
    B2 --&gt; C1
    C1 --&gt; D1
    C1 --&gt; D2
    B3 -.-&gt; A1
    B3 -.-&gt; A2
</div><h3 id="36-类对象关系图">3.6 类对象关系图 </h3>
<h4 id="361-核心类图">3.6.1 核心类图 </h4>
<div class="mermaid">classDiagram
    class DataScanApplication {
        +main(String[] args) void
        +restTemplate(RestTemplateBuilder builder) RestOperations
    }
    
    class InitCommandLineRunner {
        -ScheduleService scheduleService
        -MdmSysServiceRepository mdmSysServiceRepository
        -MdmSysServiceAttributeRepository mdmSysServiceAttributeRepository
        -int updateServiceCount
        -String scanStartTimeStr
        -int scanCycle
        -ServerConfig serverConfig
        -RtMdmSysServiceRepository rtMdmSysServiceRepository
        +run(String... args) void
        -initService(int serviceType) int
        -startLogger() int
        -scheduledServiceState() void
        -updateServiceState() void
    }
    
    class ServerConfig {
        -String serviceId
        -String serverAddress
        -int serverPort
        -String namesrvAddr
        +getServiceId() String
        +setServiceId(String serviceId) void
        +getServerAddress() String
        +getServerPort() int
        +getNamesrvAddr() String
        +loadCfg() void
        +isOracleDb() boolean
    }
    
    class ScheduleService {
        -CronTaskRegistrar cronTaskRegistrar
        +startSchedule(String id, String startTimeStr, int cycle, String beanName) void
    }
    
    class CronTaskRegistrar {
        -Map~String, ScheduledTask~ scheduledTasks
        +addCronTask(Runnable task, String cronExpression) void
        +removeCronTask(String key) void
        +destroy() void
    }
    
    class ScheduledTask {
        -ScheduledFuture~?~ future
        +cancel() void
        +task() ScheduledFuture
    }
    
    class SchedulingRunnable {
        -String beanName
        -String methodName
        -String params
        -ApplicationContext applicationContext
        +run() void
        +toString() String
    }
    
    class ScheduleScanTask {
        -JdbcTemplate jdbcTemplate
        -ServerConfig serverConfig
        -DataCalcProgressRepository DataCalcProgressRepository
        -ServiceProducer serviceProducer
        -String producerGroup
        -String producerTopic
        -String producerTags
        -DefaultMQProducer producer
        -boolean executing
        -SimpleDateFormat sdflog
        -Map~String, Date~ mapDataScanProgress
        -int scanMinute
        -int scanDay
        -int scanMonth
        -int scanEvent
        +init(String serviceId) void
        +dispatch() void
        -handleDataReg(ResultSet rs, String schemeId, int field, String timeType) long
        -scanDataOracle(String tableName, Date startTv, String schemeId, int field, String timeType) void
        -scanDataMysql(String tableName, Date startTv, String schemeId, int field, String timeType) void
        -handleEvent(ResultSet rs) long
        -scanEventOracle(Date startTv) void
        -scanEventMysql(Date startTv) void
        -sendMsg(String sdpId, String loadType, Object data) void
    }
    
    class DatabaseUtils {
        -static DatabaseUtils instance
        +getInstance() DatabaseUtils
        +queryForFetch(Connection conn, Statement st, String sql, int fetchSize) ResultSet
    }
    
    DataScanApplication ..&gt; InitCommandLineRunner: creates
    InitCommandLineRunner --&gt; ScheduleService: uses
    InitCommandLineRunner --&gt; ServerConfig: configures
    InitCommandLineRunner --&gt; MdmSysServiceRepository: queries
    InitCommandLineRunner --&gt; RtMdmSysServiceRepository: queries
    ScheduleService --&gt; CronTaskRegistrar: manages
    CronTaskRegistrar --&gt; ScheduledTask: creates &amp; manages
    ScheduledTask --&gt; SchedulingRunnable: executes
    SchedulingRunnable ..&gt; ScheduleScanTask: invokes
    ScheduleScanTask --&gt; ServerConfig: uses
    ScheduleScanTask --&gt; ServiceProducer: sends messages
    ScheduleScanTask --&gt; DatabaseUtils: executes queries
    ScheduleScanTask --&gt; DataCalcProgressRepository: queries
</div><h4 id="362-数据实体关系图">3.6.2 数据实体关系图 </h4>
<div class="mermaid">classDiagram
    class DataReg {
        -DataRegPK dataRegPK
        -String timeType
        -String valueString
        -BigDecimal value
        -String status
        -String type
        -String sflag
        -int version
        -Date edt
        -String eby
        +getter/setter methods
    }
    
    class DataRegPK {
        -String sdpId
        -Date tv
        +getter/setter methods
    }
    
    class DataVEEEvent {
        -DataVEEEventPK dataVEEEventPK
        -String valueString
        -BigDecimal value
        -String status
        -int duration
        -String sflag
        -String remark
        -int version
        -Date edt
        -String eby
        +getter/setter methods
    }
    
    class DataVEEEventPK {
        -String objectId
        -Date tv
        -String eventId
        -String schemeType
        +getter/setter methods
    }
    
    class DataCalcProgress {
        -String sdpId
        -String schemeId
        -String loadType
        -Date tv
        -Date loadTime
        -String status
        +getter/setter methods
    }
    
    class MdmSysService {
        -String id
        -int serviceType
        -String serverAddress
        -int serverPort
        -Date onlineTime
        -String status
        +getter/setter methods
    }
    
    class MQMsg~T~ {
        -String loadType
        -String fromServiceId
        -String fromServiceType
        -String fromId
        -String fromType
        -String topic
        -String tags
        -T load
        +getter/setter methods
    }
    
    DataReg *-- DataRegPK: contains
    DataVEEEvent *-- DataVEEEventPK: contains
    ScheduleScanTask ..&gt; DataReg: processes
    ScheduleScanTask ..&gt; DataVEEEvent: processes
    ScheduleScanTask ..&gt; DataCalcProgress: tracks
    ScheduleScanTask ..&gt; MQMsg: creates
    InitCommandLineRunner ..&gt; MdmSysService: updates
</div><h4 id="363-存储库关系图">3.6.3 存储库关系图 </h4>
<div class="mermaid">classDiagram
    class Repository {
        &lt;&lt;interface&gt;&gt;
    }
    
    class CrudRepository {
        &lt;&lt;interface&gt;&gt;
        +save(S entity) S
        +findById(ID id) Optional~T~
        +findAll() List~T~
        +delete(T entity) void
    }
    
    class DataCalcProgressRepository {
        &lt;&lt;interface&gt;&gt;
        +findAll() List~DataCalcProgress~
    }
    
    class MdmSysServiceRepository {
        &lt;&lt;interface&gt;&gt;
        +save(MdmSysService entity) MdmSysService
    }
    
    class MdmSysServiceAttributeRepository {
        &lt;&lt;interface&gt;&gt;
        +findValue(String serviceId, String attributeName) String
    }
    
    class RtMdmSysServiceRepository {
        &lt;&lt;interface&gt;&gt;
        +findById(String id) Optional~MdmSysService~
        +findServiceId(int serviceType, String serverAddress) String
        +findByServiceType(int serviceType) List~MdmSysService~
    }
    
    Repository &lt;|-- CrudRepository
    CrudRepository &lt;|-- DataCalcProgressRepository
    CrudRepository &lt;|-- MdmSysServiceRepository
    CrudRepository &lt;|-- RtMdmSysServiceRepository
    
    ScheduleScanTask --&gt; DataCalcProgressRepository: uses
    InitCommandLineRunner --&gt; MdmSysServiceRepository: uses
    InitCommandLineRunner --&gt; MdmSysServiceAttributeRepository: uses
    InitCommandLineRunner --&gt; RtMdmSysServiceRepository: uses
</div><h2 id="4-接口设计">4. 接口设计 </h2>
<h3 id="41-内部接口">4.1 内部接口 </h3>
<h4 id="411-定时任务接口">4.1.1 定时任务接口 </h4>
<pre data-role="codeBlock" data-info="java" class="language-java java"><code><span class="token comment">// 初始化定时任务</span>
<span class="token keyword keyword-public">public</span> <span class="token keyword keyword-void">void</span> <span class="token function">init</span><span class="token punctuation">(</span><span class="token class-name">String</span> serviceId<span class="token punctuation">)</span>

<span class="token comment">// 执行扫描任务</span>
<span class="token keyword keyword-public">public</span> <span class="token keyword keyword-void">void</span> <span class="token function">dispatch</span><span class="token punctuation">(</span><span class="token punctuation">)</span>
</code></pre><h4 id="412-数据库接口">4.1.2 数据库接口 </h4>
<pre data-role="codeBlock" data-info="java" class="language-java java"><code><span class="token comment">// 扫描Oracle数据库中的数据</span>
<span class="token keyword keyword-private">private</span> <span class="token keyword keyword-void">void</span> <span class="token function">scanDataOracle</span><span class="token punctuation">(</span><span class="token class-name">String</span> tableName<span class="token punctuation">,</span> <span class="token class-name">Date</span> startTv<span class="token punctuation">,</span> <span class="token class-name">String</span> schemeId<span class="token punctuation">,</span> <span class="token keyword keyword-int">int</span> field<span class="token punctuation">,</span> <span class="token class-name">String</span> timeType<span class="token punctuation">)</span>

<span class="token comment">// 扫描MySQL数据库中的数据</span>
<span class="token keyword keyword-private">private</span> <span class="token keyword keyword-void">void</span> <span class="token function">scanDataMysql</span><span class="token punctuation">(</span><span class="token class-name">String</span> tableName<span class="token punctuation">,</span> <span class="token class-name">Date</span> startTv<span class="token punctuation">,</span> <span class="token class-name">String</span> schemeId<span class="token punctuation">,</span> <span class="token keyword keyword-int">int</span> field<span class="token punctuation">,</span> <span class="token class-name">String</span> timeType<span class="token punctuation">)</span>

<span class="token comment">// 扫描Oracle数据库中的事件</span>
<span class="token keyword keyword-private">private</span> <span class="token keyword keyword-void">void</span> <span class="token function">scanEventOracle</span><span class="token punctuation">(</span><span class="token class-name">Date</span> startTv<span class="token punctuation">)</span>

<span class="token comment">// 扫描MySQL数据库中的事件</span>
<span class="token keyword keyword-private">private</span> <span class="token keyword keyword-void">void</span> <span class="token function">scanEventMysql</span><span class="token punctuation">(</span><span class="token class-name">Date</span> startTv<span class="token punctuation">)</span>
</code></pre><h4 id="413-消息发送接口">4.1.3 消息发送接口 </h4>
<pre data-role="codeBlock" data-info="java" class="language-java java"><code><span class="token comment">// 发送消息到RocketMQ</span>
<span class="token keyword keyword-private">private</span> <span class="token keyword keyword-void">void</span> <span class="token function">sendMsg</span><span class="token punctuation">(</span><span class="token class-name">String</span> sdpId<span class="token punctuation">,</span> <span class="token class-name">String</span> loadType<span class="token punctuation">,</span> <span class="token class-name">Object</span> data<span class="token punctuation">)</span>
</code></pre><h3 id="42-外部接口">4.2 外部接口 </h3>
<p>通过Spring Boot提供RESTful API接口，具体接口由controller包中的类定义。</p>
<h2 id="5-配置管理">5. 配置管理 </h2>
<h3 id="51-系统配置">5.1 系统配置 </h3>
<p>系统配置主要存储在<code>application.yml</code>文件中，关键配置项：</p>
<pre data-role="codeBlock" data-info="yaml" class="language-yaml yaml"><code><span class="token key atrule">spring</span><span class="token punctuation">:</span>
  <span class="token key atrule">application</span><span class="token punctuation">:</span>
    <span class="token key atrule">name</span><span class="token punctuation">:</span> DataScan  <span class="token comment"># 服务名称</span>
  <span class="token key atrule">profiles</span><span class="token punctuation">:</span>
    <span class="token key atrule">include</span><span class="token punctuation">:</span> common        
  <span class="token key atrule">realtime</span><span class="token punctuation">:</span>      
    <span class="token key atrule">datasource</span><span class="token punctuation">:</span>
      <span class="token key atrule">jdbc-url</span><span class="token punctuation">:</span> jdbc<span class="token punctuation">:</span>h2<span class="token punctuation">:</span>tcp<span class="token punctuation">:</span>//localhost<span class="token punctuation">:</span>9799/mem<span class="token punctuation">:</span>realtimedb;DB_CLOSE_ON_EXIT=FALSE
      <span class="token key atrule">driver-class-name</span><span class="token punctuation">:</span> org.h2.Driver
      <span class="token key atrule">username</span><span class="token punctuation">:</span> root
      <span class="token key atrule">password</span><span class="token punctuation">:</span> <span class="token number">123</span> 
<span class="token key atrule">server</span><span class="token punctuation">:</span>
  <span class="token key atrule">port</span><span class="token punctuation">:</span> <span class="token number">9806</span>  <span class="token comment"># 端口</span>
<span class="token key atrule">schedule</span><span class="token punctuation">:</span>
  <span class="token key atrule">scanStartTime</span><span class="token punctuation">:</span> <span class="token datetime number">2020-01-01 00:57:00</span>  <span class="token comment"># 扫描起始时间</span>
  <span class="token key atrule">scanCycle</span><span class="token punctuation">:</span> <span class="token number">1</span>  <span class="token comment"># 扫描周期</span>
  <span class="token key atrule">scanMinute</span><span class="token punctuation">:</span> <span class="token number">7</span>  <span class="token comment"># 分钟数据扫描范围(天)</span>
  <span class="token key atrule">scanDay</span><span class="token punctuation">:</span> <span class="token number">30</span>  <span class="token comment"># 日数据扫描范围(天)</span>
  <span class="token key atrule">scanMonth</span><span class="token punctuation">:</span> <span class="token number">6</span>  <span class="token comment"># 月数据扫描范围(月)</span>
  <span class="token key atrule">scanEvent</span><span class="token punctuation">:</span> <span class="token number">1</span>  <span class="token comment"># 事件扫描范围(月)</span>
</code></pre><h3 id="52-配置项说明">5.2 配置项说明 </h3>
<table>
<thead>
<tr>
<th>配置项</th>
<th>说明</th>
<th>默认值</th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="http://spring.application.name">spring.application.name</a></td>
<td>服务名称</td>
<td>DataScan</td>
</tr>
<tr>
<td>server.port</td>
<td>服务端口</td>
<td>9806</td>
</tr>
<tr>
<td>schedule.scanStartTime</td>
<td>扫描起始时间</td>
<td>2020-01-01 00:57:00</td>
</tr>
<tr>
<td>schedule.scanCycle</td>
<td>扫描周期</td>
<td>1</td>
</tr>
<tr>
<td>schedule.scanMinute</td>
<td>分钟数据扫描范围(天)</td>
<td>7</td>
</tr>
<tr>
<td>schedule.scanDay</td>
<td>日数据扫描范围(天)</td>
<td>30</td>
</tr>
<tr>
<td>schedule.scanMonth</td>
<td>月数据扫描范围(月)</td>
<td>6</td>
</tr>
<tr>
<td>schedule.scanEvent</td>
<td>事件扫描范围(月)</td>
<td>1</td>
</tr>
</tbody>
</table>
<h2 id="6-部署架构">6. 部署架构 </h2>
<h3 id="61-部署环境要求">6.1 部署环境要求 </h3>
<ul>
<li>JDK 1.8或以上</li>
<li>MySQL 5.7或Oracle 11g以上</li>
<li>RocketMQ 4.x</li>
<li>内存：至少4GB</li>
<li>磁盘空间：根据数据规模确定，建议至少50GB</li>
</ul>
<h3 id="62-部署步骤">6.2 部署步骤 </h3>
<ol>
<li>安装JDK 1.8+</li>
<li>安装并配置数据库(MySQL或Oracle)</li>
<li>安装并配置RocketMQ</li>
<li>配置application.yml文件</li>
<li>运行install.bat脚本</li>
<li>检查服务是否正常启动</li>
</ol>
<h3 id="63-部署架构图">6.3 部署架构图 </h3>
<pre data-role="codeBlock" data-info="" class="language-text"><code>+----------------+      +----------------+      +----------------+
|                |      |                |      |                |
|  应用服务器    |-----&gt;|   数据库服务器  |      |  消息队列服务器 |
| (DataScan)     |      | (MySQL/Oracle) |      |  (RocketMQ)   |
|                |&lt;-----|                |      |                |
+----------------+      +----------------+      +----------------+
        ^                                              ^
        |                                              |
        +----------------------------------------------+
</code></pre><h2 id="7-安全设计">7. 安全设计 </h2>
<h3 id="71-数据安全">7.1 数据安全 </h3>
<ul>
<li>数据库访问使用加密的用户名和密码</li>
<li>敏感数据传输采用加密方式</li>
<li>定期备份关键数据</li>
</ul>
<h3 id="72-访问控制">7.2 访问控制 </h3>
<ul>
<li>服务间通信采用认证机制</li>
<li>API接口调用需要权限验证</li>
<li>系统操作记录完整日志便于审计</li>
</ul>
<h2 id="8-异常处理">8. 异常处理 </h2>
<h3 id="81-常见异常">8.1 常见异常 </h3>
<table>
<thead>
<tr>
<th>异常类型</th>
<th>处理方式</th>
</tr>
</thead>
<tbody>
<tr>
<td>数据库连接异常</td>
<td>记录日志，尝试重连</td>
</tr>
<tr>
<td>消息发送失败</td>
<td>记录日志，设置重试机制</td>
</tr>
<tr>
<td>数据格式错误</td>
<td>记录日志，跳过错误数据</td>
</tr>
<tr>
<td>服务启动失败</td>
<td>记录日志，自动重启</td>
</tr>
</tbody>
</table>
<h3 id="82-异常恢复机制">8.2 异常恢复机制 </h3>
<ul>
<li>对于临时性故障，实现自动重试机制</li>
<li>对于持久性故障，通过日志告警通知管理员</li>
<li>实现服务监控和自动恢复功能</li>
</ul>
<h2 id="9-性能考虑">9. 性能考虑 </h2>
<h3 id="91-性能优化措施">9.1 性能优化措施 </h3>
<ul>
<li>数据库查询分批处理(每批100000条)</li>
<li>针对不同数据库(Oracle/MySQL)优化的查询语句</li>
<li>使用消息队列进行异步处理，避免阻塞</li>
<li>定时任务的串行化执行(避免并发冲突)</li>
<li>利用数据库索引提高查询效率</li>
</ul>
<h3 id="92-性能指标">9.2 性能指标 </h3>
<table>
<thead>
<tr>
<th>指标</th>
<th>目标值</th>
</tr>
</thead>
<tbody>
<tr>
<td>单次数据扫描处理能力</td>
<td>≥100万条/分钟</td>
</tr>
<tr>
<td>系统CPU使用率</td>
<td>&lt;70%</td>
</tr>
<tr>
<td>系统内存使用率</td>
<td>&lt;80%</td>
</tr>
<tr>
<td>响应时间</td>
<td>&lt;1秒</td>
</tr>
<tr>
<td>系统可用性</td>
<td>&gt;99.9%</td>
</tr>
</tbody>
</table>
<h2 id="10-测试计划">10. 测试计划 </h2>
<h3 id="101-测试类型">10.1 测试类型 </h3>
<ul>
<li><strong>单元测试</strong>：验证各模块功能的正确性</li>
<li><strong>集成测试</strong>：验证与其他服务的交互</li>
<li><strong>性能测试</strong>：验证大数据量下的系统性能</li>
<li><strong>发布测试</strong>：验证部署和配置的正确性</li>
</ul>
<h3 id="102-测试用例">10.2 测试用例 </h3>
<table>
<thead>
<tr>
<th>测试场景</th>
<th>预期结果</th>
</tr>
</thead>
<tbody>
<tr>
<td>系统启动</td>
<td>服务正常启动，初始化成功</td>
</tr>
<tr>
<td>数据扫描</td>
<td>按照指定周期和范围完成扫描</td>
</tr>
<tr>
<td>消息发送</td>
<td>成功发送消息到队列</td>
</tr>
<tr>
<td>异常处理</td>
<td>系统能正确处理各类异常</td>
</tr>
<tr>
<td>大数据量测试</td>
<td>系统在大数据量下稳定运行</td>
</tr>
</tbody>
</table>
<h2 id="11-维护计划">11. 维护计划 </h2>
<h3 id="111-日常维护">11.1 日常维护 </h3>
<ul>
<li>系统日志监控和分析</li>
<li>数据库性能监控</li>
<li>服务状态监控</li>
<li>定期数据备份</li>
</ul>
<h3 id="112-计划性维护">11.2 计划性维护 </h3>
<ul>
<li>定期代码审查和优化</li>
<li>系统版本升级</li>
<li>安全漏洞修复</li>
<li>性能调优</li>
</ul>
<h2 id="12-变更历史">12. 变更历史 </h2>
<table>
<thead>
<tr>
<th>版本号</th>
<th>日期</th>
<th>变更说明</th>
<th>责任人</th>
</tr>
</thead>
<tbody>
<tr>
<td>2024092310</td>
<td>2024-09-23</td>
<td>初始版本</td>
<td>-</td>
</tr>
<tr>
<td>2024092410</td>
<td>2024-09-24</td>
<td>增加业务流程图</td>
<td>-</td>
</tr>
<tr>
<td>2025032010</td>
<td>2025-03-20</td>
<td>增加类对象关系图</td>
<td>-</td>
</tr>
</tbody>
</table>
<h2 id="13-附录">13. 附录 </h2>
<h3 id="131-术语表">13.1 术语表 </h3>
<table>
<thead>
<tr>
<th>术语</th>
<th>全称</th>
<th>说明</th>
</tr>
</thead>
<tbody>
<tr>
<td>VEE</td>
<td>Validation, Estimation, and Editing</td>
<td>验证、估算和编辑</td>
</tr>
<tr>
<td>SDP</td>
<td>Service Delivery Point</td>
<td>服务交付点</td>
</tr>
<tr>
<td>MDM</td>
<td>Meter Data Management</td>
<td>表计数据管理</td>
</tr>
<tr>
<td>ClouESP</td>
<td>Cloud Energy Service Platform</td>
<td>云能源服务平台</td>
</tr>
</tbody>
</table>
<h3 id="132-参考文档">13.2 参考文档 </h3>
<ul>
<li>Spring Boot官方文档：<a href="https://spring.io/projects/spring-boot">https://spring.io/projects/spring-boot</a></li>
<li>MyBatis官方文档：<a href="https://mybatis.org/mybatis-3/">https://mybatis.org/mybatis-3/</a></li>
<li>RocketMQ官方文档：<a href="http://rocketmq.apache.org/docs/">http://rocketmq.apache.org/docs/</a></li>
<li>Oracle/MySQL数据库文档</li>
</ul>
<hr>
<p><em>文档创建日期：2024年9月23日</em><br>
<em>文档版本：1.0</em></p>

      </div>
      
      
    
    
    <script type="module">
// TODO: If ZenUML gets integrated into mermaid in the future,
//      we can remove the following lines.


var MERMAID_CONFIG = ({"startOnLoad":false});
if (typeof MERMAID_CONFIG !== 'undefined') {
  MERMAID_CONFIG.startOnLoad = false
  MERMAID_CONFIG.cloneCssStyles = false
  MERMAID_CONFIG.theme = "default"
}

mermaid.initialize(MERMAID_CONFIG || {})
if (typeof(window['Reveal']) !== 'undefined') {
  function mermaidRevealHelper(event) {
    var currentSlide = event.currentSlide
    var diagrams = currentSlide.querySelectorAll('.mermaid')
    for (var i = 0; i < diagrams.length; i++) {
      var diagram = diagrams[i]
      if (!diagram.hasAttribute('data-processed')) {
        mermaid.init(null, diagram, ()=> {
          Reveal.slide(event.indexh, event.indexv)
        })
      }
    }
  }
  Reveal.addEventListener('slidetransitionend', mermaidRevealHelper)
  Reveal.addEventListener('ready', mermaidRevealHelper)
  await mermaid.run({
    nodes: document.querySelectorAll('.mermaid')
  })
} else {
  await mermaid.run({
    nodes: document.querySelectorAll('.mermaid')
  })
}
</script>
    
    
    
  
    </body></html>