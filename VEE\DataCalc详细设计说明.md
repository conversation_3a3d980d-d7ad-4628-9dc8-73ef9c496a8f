# DataCalc详细设计说明

## 1. 概述

DataCalc是电能数据管理系统中负责数据处理与计算的核心模块。它通过消息队列接收来自DataScan的数据，执行一系列计算和处理，并将结果保存到MDM持久化数据库和RealTimeDB实时数据库中。

DataCalc支持四种主要处理类型:
- 数据验证(VALIDATION)
- 电量计算(CALCENERGY)
- 对象计算(CALCOBJ)
- 自动估算(AUTOESTIMATION)

## 2. 系统架构

### 2.1 整体架构

```mermaid
graph TD
    subgraph 输入层
        MqBus[消息队列总线<br>MqBus]
        Messages[消息类型<br>Register/VeeEvent]
    end
    
    subgraph 处理层
        HandlerService[处理服务<br>HandlerService]
        HandlerFactory[处理器工厂<br>HandlerFactory]
        
        subgraph 处理器
            ValidationHandler[数据验证处理器<br>ValidationHandler]
            CalcEnergyHandler[电量计算处理器<br>CalcEnergyHandler]
            CalcObjHandler[对象计算处理器<br>CalcObjHandler]
            AutoEstimationHandler[自动估算处理器<br>AutoEstimationHandler]
        end
    end
    
    subgraph 持久层
        RealTimeDB[实时内存数据库<br>RealTimeDB]
        MDM[MDM持久化数据库]
        Progress[计算进度表<br>mdm_data_calc_progress]
    end
    
    MqBus --> Messages
    Messages --> HandlerService
    HandlerService --> HandlerFactory
    HandlerFactory --> ValidationHandler & CalcEnergyHandler & CalcObjHandler & AutoEstimationHandler
    
    ValidationHandler --> CalcEnergyHandler
    ValidationHandler & CalcEnergyHandler & CalcObjHandler & AutoEstimationHandler --> RealTimeDB
    ValidationHandler & CalcEnergyHandler & CalcObjHandler & AutoEstimationHandler --> MDM
    CalcEnergyHandler --> Progress
```

### 2.2 模块组成

DataCalc由以下主要组件组成:

1. **消息接收模块**: 负责从MqBus接收消息
2. **处理器工厂**: 根据消息类型创建对应的处理器
3. **数据处理器**: 执行具体的数据处理逻辑
4. **进度管理模块**: 管理数据处理进度，避免重复处理
5. **数据存储模块**: 将处理结果保存到数据库

## 3. 数据流程

### 3.1 基本流程

DataCalc的基本处理流程如下:

```mermaid
graph TD
    start[开始] --> listen[监听MqBus消息]
    listen --> receive[接收数据处理消息]
    receive --> identify{识别处理类型}
    
    identify -->|VALIDATION| valid[数据验证处理<br>ValidationHandler]
    identify -->|CALCENERGY| energy[电量计算处理<br>CalcEnergyHandler]
    identify -->|CALCOBJ| object[对象计算处理<br>CalcObjHandler]
    identify -->|AUTOESTIMATION| estimate[自动估算处理<br>AutoEstimationHandler]
    
    valid & energy & object & estimate --> result[生成处理结果]
    result --> saveDB[保存到数据库]
    saveDB --> listen
```

### 3.2 消息接收与分发

1. 从MqBus接收消息
2. 解析消息内容，确定处理类型
3. 通过HandlerFactory创建对应的处理器
4. 调用处理器的处理方法

### 3.3 处理器调用链

处理器之间存在调用关系:

```mermaid
graph LR
    ValidationHandler -->|验证通过| CalcEnergyHandler
    CalcEnergyHandler -->|电量计算完成| CalcObjHandler
    CalcObjHandler -->|需要估算| AutoEstimationHandler
```

## 4. 核心组件详解

### 4.1 HandlerService

HandlerService是DataCalc的核心服务类，负责接收消息并调度处理器:

主要功能:
- 接收来自MqBus的消息
- 解析消息类型和内容
- 调用HandlerFactory创建处理器
- 执行处理逻辑并返回结果

处理流程:
1. 接收消息并解析
2. 根据消息中的服务ID和处理类型决定使用哪种处理器
3. 调用处理器的处理方法
4. 保存处理结果

### 4.2 HandlerFactory

HandlerFactory负责创建不同类型的处理器:

```mermaid
graph TD
    Factory[HandlerFactory] --> Valid[ValidationHandler]
    Factory --> Energy[CalcEnergyHandler]
    Factory --> Object[CalcObjHandler]
    Factory --> Estimate[AutoEstimationHandler]
    
    Valid --> BaseHandler[BaseHandler接口]
    Energy --> BaseHandler
    Object --> BaseHandler
    Estimate --> BaseHandler
```

### 4.3 ValidationHandler

ValidationHandler负责数据验证处理:

主要功能:
- 验证表码数据的有效性
- 识别异常数据(如跳码、倒走等)
- 标记验证结果
- 将验证通过的数据传递给CalcEnergyHandler

处理流程:
1. 接收表码数据(DataReg)
2. 执行规则验证
3. 记录验证结果
4. 将验证通过的数据传递给下一个处理器

### 4.4 CalcEnergyHandler

CalcEnergyHandler是DataCalc中最复杂的处理器，负责电量计算:

#### 4.4.1 初始化流程

```mermaid
graph TD
    init[CalcEnergyHandler初始化] --> loadProgress[加载所有计算进度记录]
    loadProgress --> memoryMap[将进度记录加载到mapDataCalcProgress]
    memoryMap --> startHandler[启动HandlerProc线程]
    startHandler --> startSave[启动DataSaveProc线程]
```

初始化时，CalcEnergyHandler会:
1. 从`mdm_data_calc_progress`表加载所有计算进度记录
2. 将进度记录存入内存中的`mapDataCalcProgress`
3. 启动`HandlerProc`线程处理接收到的数据
4. 启动`DataSaveProc`线程保存进度和计算结果

#### 4.4.2 数据处理流程

```mermaid
graph TD
    receive[接收表码数据] --> checkType{检查数据类型}
    checkType -->|表码数据| buildKey[构建进度键<br>sdpId#schemeId#loadType]
    buildKey --> checkProgress{检查是否已处理}
    
    checkProgress -->|数据时间>最后进度时间| process[处理数据]
    checkProgress -->|数据时间<=最后进度时间| skip[跳过处理]
    
    process --> calculate[计算电量差值]
    calculate --> createResult[创建DataEnergy记录]
    createResult --> queue[加入保存队列]
    queue --> updateMap[更新内存进度映射]
```

处理表码数据时:
1. 检查数据类型(月度、日度、分钟级)
2. 构建唯一键: `sdpId + "#" + schemeId + "#" + loadType`
3. 检查该键在内存中是否有进度记录，判断是否需要处理
4. 处理数据，计算电量差值
5. 创建计算结果记录并加入保存队列
6. 更新内存中的进度记录

#### 4.4.3 进度管理

CalcEnergyHandler实现了高效的批量更新机制:

```mermaid
graph TD
    saveThread[DataSaveProc线程] --> checkQueue{检查队列状态}
    checkQueue -->|队列为空| wait[等待1秒]
    checkQueue -->|时间间隔<15秒<br>且记录数<100| waitMore[继续等待]
    checkQueue -->|时间间隔>=15秒<br>或记录数>=100| drain[从队列取出记录<br>最多1000条]
    drain --> sort[按时间排序]
    sort --> saveDB[批量更新mdm_data_calc_progress表]
    saveDB -->|成功| logSuccess[记录日志]
    saveDB -->|失败| retry[放回队列下次重试]
    logSuccess & retry --> wait
```

进度更新机制:
1. 专门的线程(`DataSaveProc`)定期检查进度更新队列
2. 当队列中有足够记录(100条)或时间间隔足够长(15秒)时执行批量更新
3. 从队列取出记录(最多1000条)并按时间排序
4. 根据数据库类型选择不同的SQL语句(Oracle用MERGE，MySQL用REPLACE)
5. 执行批量更新，更新失败的记录放回队列重试

#### 4.4.4 计算结果保存

计算结果同样采用批量保存机制:
1. 计算结果放入`dataEnergyQueue`队列
2. `DataSaveProc`线程同时负责保存计算结果和进度记录
3. 批量保存逻辑类似于进度记录的保存

### 4.5 CalcObjHandler

CalcObjHandler负责对象计算处理:

主要功能:
- 根据计算方案执行对象级别的计算
- 处理复杂的计算逻辑，如汇总、平均等
- 生成对象计算结果

处理流程:
1. 接收电量数据
2. 查询计算方案
3. 执行对象计算
4. 保存计算结果

### 4.6 AutoEstimationHandler

AutoEstimationHandler负责自动估算处理:

主要功能:
- 对缺失或异常数据进行自动估算
- 应用估算算法生成合理的替代值
- 标记估算结果

处理流程:
1. 接收需要估算的数据
2. 确定估算方法
3. 执行估算算法
4. 保存估算结果

## 5. 数据结构

### 5.1 主要实体类

DataCalc处理的主要数据实体包括:

1. **DataReg**: 表码数据实体
   - 包含表计读数、时间戳等信息
   - 主键由sdpId和tv组成

2. **DataEnergy**: 电量数据实体
   - 包含计算后的电量差值
   - 按时间粒度分为分钟级、日度和月度

3. **DataCalcProgress**: 计算进度实体
   - 记录数据处理进度
   - 包含sdpId、schemeId、loadType和最后处理时间

4. **DataVEEEvent**: VEE事件数据实体
   - 记录验证、估算、编辑等事件

### 5.2 关键数据表

DataCalc涉及的主要数据表包括:

1. **mdm_data_reg_monthly/dayly/minutely**: 表码数据表
2. **mdm_data_energy_monthly/dayly/minutely**: 电量数据表
3. **mdm_data_calc_progress**: 计算进度表
4. **mdm_data_vee_event**: VEE事件数据表

## 6. 进度管理机制

### 6.1 进度表结构

`mdm_data_calc_progress`表是DataCalc的核心进度管理表:

| 字段 | 类型 | 说明 |
|------|------|------|
| sdp_id | VARCHAR | 服务点ID |
| scheme_id | VARCHAR | 方案ID |
| load_type | VARCHAR | 数据类型(Register/VeeEvent) |
| tv | TIMESTAMP | 最后处理时间 |
| update_tv | TIMESTAMP | 更新时间 |

主键: (sdp_id, scheme_id, load_type)

### 6.2 进度管理流程

进度管理是DataCalc的重要功能，确保数据不会被重复处理:

1. **初始化阶段**:
   - 加载所有进度记录到内存
   - 构建键: `sdpId + "#" + schemeId + "#" + loadType`
   - 存入ConcurrentHashMap

2. **数据处理前检查**:
   - 构建待处理数据的键
   - 检查内存中是否有对应进度记录
   - 比较数据时间与进度时间
   - 只处理时间较新的数据

3. **进度更新**:
   - 处理完数据后创建或更新进度记录
   - 放入进度更新队列
   - 更新内存中的进度映射
   - 定期批量保存进度记录

### 6.3 与DataScan的协作

DataCalc通过进度表与DataScan形成闭环:

```mermaid
graph TD
    DS[DataScan] -->|扫描开始时<br>加载进度记录| PT[mdm_data_calc_progress]
    DS -->|根据进度跳过<br>已处理数据| MB[MqBus]
    MB -->|发送未处理数据| DC[DataCalc]
    DC -->|处理数据并<br>更新进度| PT
    PT -->|下次扫描时使用| DS
```

## 7. 错误处理机制

### 7.1 异常处理策略

DataCalc实现了多层次的异常处理:

1. **消息处理异常**:
   - 捕获消息处理过程中的异常
   - 记录错误日志
   - 确保消息队列能继续工作

2. **数据处理异常**:
   - 单条数据处理失败不影响其他数据
   - 记录错误数据日志
   - 标记异常记录

3. **数据库操作异常**:
   - 使用重试机制
   - 批量操作失败时，将数据放回队列
   - 记录数据库错误日志

### 7.2 数据一致性保障

为保证数据一致性，DataCalc采用以下策略:

1. **事务管理**:
   - 针对关键操作使用数据库事务
   - 确保相关数据的原子性更新

2. **批量操作重试**:
   - 批量更新失败时进行重试
   - 使用队列机制暂存失败的操作

3. **进度记录保护**:
   - 进度记录按时间排序后更新
   - 确保进度的单调递增

## 8. 性能优化措施

### 8.1 内存缓存

CalcEnergyHandler使用内存缓存提高性能:

1. **进度记录缓存**:
   - 将全部进度记录加载到内存
   - 避免频繁的数据库查询
   - 使用ConcurrentHashMap保证线程安全

2. **计算结果缓存**:
   - 临时存储计算结果
   - 批量写入数据库
   - 减少数据库操作次数

### 8.2 多线程处理

DataCalc采用多线程提高处理效率:

1. **消息处理线程**:
   - 专门线程(`HandlerProc`)处理接收到的消息
   - 避免阻塞消息接收

2. **数据保存线程**:
   - 专门线程(`DataSaveProc`)批量保存数据
   - 与处理线程并行工作
   - 提高整体处理吞吐量

### 8.3 批量操作

批量操作是提高性能的关键:

1. **批量保存机制**:
   - 累积一定数量的记录再一次性保存
   - 减少数据库连接次数
   - 利用数据库批量操作能力

2. **触发条件优化**:
   - 队列记录数达到阈值(100条)
   - 时间间隔超过阈值(15秒)
   - 平衡实时性和效率

## 9. 配置管理

### 9.1 主要配置项

DataCalc支持以下主要配置项:

1. **消息队列配置**:
   - RocketMQ服务器地址和端口
   - 消息主题和标签
   - 消费者组ID

2. **数据库配置**:
   - 数据库连接信息
   - 数据库类型(Oracle/MySQL)
   - 连接池参数

3. **处理参数配置**:
   - 批量处理大小
   - 处理线程数
   - 重试间隔和次数

### 9.2 配置加载机制

1. **启动时加载**:
   - 应用启动时从配置文件加载
   - 支持环境变量覆盖
   - 支持配置热更新

2. **配置参数注入**:
   - 使用依赖注入方式
   - 避免硬编码配置值
   - 方便测试和部署

## 10. 模块交互

### 10.1 与DataScan的交互

DataCalc通过MqBus与DataScan交互:

1. **数据流向**:
   - DataScan扫描MDM数据库
   - 将未处理的数据发送到MqBus
   - DataCalc从MqBus接收数据
   - 处理后更新进度表

2. **协调机制**:
   - 通过进度表协调工作
   - DataScan根据进度表跳过已处理数据
   - 确保数据不会重复处理

### 10.2 与数据库的交互

DataCalc与两种数据库交互:

1. **MDM持久化数据库**:
   - 保存计算结果和进度信息
   - 长期存储历史数据
   - 支持Oracle和MySQL

2. **RealTimeDB实时数据库**:
   - 保存活跃数据供快速访问
   - 内存数据库，访问速度快
   - 定期与MDM同步

## 11. 总结

DataCalc作为数据处理系统的核心模块，具有以下特点:

1. **多功能处理**:
   - 支持数据验证、电量计算、对象计算和自动估算
   - 灵活的处理器框架

2. **高效处理**:
   - 内存缓存和多线程并行处理
   - 批量操作提高吞吐量
   - 进度管理避免重复处理

3. **可靠性保障**:
   - 完善的异常处理
   - 数据一致性保护
   - 批量操作重试机制

4. **可扩展性**:
   - 工厂模式创建处理器
   - 松耦合的模块设计
   - 配置化的处理参数 