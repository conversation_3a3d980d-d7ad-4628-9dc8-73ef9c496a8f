package clouesp.hes.core.DataScan;

import clouesp.hes.common.DataEntity.System.MdmSysService;
import clouesp.hes.common.DataRepository.Persistence.System.MdmSysServiceAttributeRepository;
import clouesp.hes.common.DataRepository.Persistence.System.MdmSysServiceRepository;
import clouesp.hes.common.DataRepository.RealTime.System.RtMdmSysServiceRepository;
import clouesp.hes.common.logger.logger.Logger;
import clouesp.hes.common.logger.logger.LoggerAppenderType;
import clouesp.hes.common.logger.logger.LoggerLevel;
import clouesp.hes.core.DataScan.config.ServerConfig;
import clouesp.hes.core.DataScan.schedule.ScheduleService;
import jline.internal.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;

@Component
public class InitCommandLineRunner implements CommandLineRunner {

	@Resource(name="scheduleService")
	private ScheduleService scheduleService;
	
	@Autowired
	private MdmSysServiceRepository mdmSysServiceRepository;

	@Autowired
	private MdmSysServiceAttributeRepository mdmSysServiceAttributeRepository;
	
	private int updateServiceCount = 0;	

	@Value("${schedule.scanStartTime}")
	private String scanStartTimeStr;

	@Value("${schedule.scanCycle}")
	private int scanCycle;

	@Autowired
    private ServerConfig serverConfig;


	@Autowired
	private RtMdmSysServiceRepository rtMdmSysServiceRepository;

	@Override
	public void run(String... args) throws Exception {

		String logInfo = "        Version: 2024092310" ;
		Log.info("");
		Log.info("************************************************************");
		Log.info(logInfo);
		Log.info("************************************************************");
		Log.info("");
		Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "System", logInfo);

		Log.info("Server IP: " + serverConfig.getServerAddress());
		Log.info("Server Port: " + serverConfig.getServerPort());
		startLogger();		
		if (initService(3) == -1) {
			return;
		}	
		updateServiceState();
		scheduleService.startSchedule("1006", scanStartTimeStr, scanCycle, "scheduleScanTask");
	}
	
	private int initService(int serviceType) {
		String serviceId = rtMdmSysServiceRepository.findServiceId(serviceType, serverConfig.getServerAddress());
 		serviceId = "50010005";
//		if (serviceId == null) {
//			 Log.info(Failed to get the service");
//	         Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, null, "0", "System", "Failed to get the service");
//			return -1;
//		}
		serverConfig.setServiceId(serviceId);
		serverConfig.loadCfg();
		String logInfo;
		logInfo = "Startup Param[Service id: " + serverConfig.getServiceId() + "]";
		Log.info(logInfo);
		Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "System", logInfo); 
		
		logInfo = "Startup Param[RocketMQ namesrvAddr: " + serverConfig.getNamesrvAddr() + "]";
		Log.info(logInfo);
		Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "System", logInfo);

		return 0;
	}	
	
    private int startLogger(){
    	
    	try {
	    	File directory = new File("");// 参数为空
	        String courseFile;
			
			courseFile = directory.getCanonicalPath();
			File parent = new File(courseFile);
			String parentPath = parent.getParent() + "//log//";
			 //设置写日志路径
	        Logger.getInstance().setIndexPath(parentPath);
	        //输出源固定为LoggerAppenderType.Lucene
	        Logger.getInstance().setAppenderType(LoggerAppenderType.Lucene);
	        //启动日志
	        Logger.getInstance().start();
	        //设置日志级别
	        Logger.getInstance().setLevel(LoggerLevel.INFO);       
			
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
        return 0;
    }	

	@Scheduled(fixedDelay = 60000)
	private void scheduledServiceState() {
		if (serverConfig.getServiceId() == null) {
			return;
		}
		
		int cycle = 10;
		try {
			List<MdmSysService> mdmSysServices = rtMdmSysServiceRepository.findByServiceType(6);
			String appServiceId = null;
			if (mdmSysServices != null && mdmSysServices.size() > 0) {
				appServiceId = mdmSysServices.get(0).getId();
			}
			if (appServiceId != null) {
				String strCycle = mdmSysServiceAttributeRepository.findValue(appServiceId,
						"Application.UpdateModuleStatus.Cycle");

				try {
					cycle = Integer.parseInt(strCycle);
				} catch (Exception e) {

				}
			}

			if (updateServiceCount < cycle) {
				updateServiceCount++;
				return;
			}
			updateServiceCount = 0;
			updateServiceState();
		} catch (Exception e) {
			Log.info("scheduledServiceState: " + e.getMessage());
		}
	}
	
	private void updateServiceState() {
		Optional<MdmSysService> optionalMdmSysService = rtMdmSysServiceRepository.findById(serverConfig.getServiceId());
		if (!optionalMdmSysService.isPresent()) {
			return;
		}
		MdmSysService mdmSysService = optionalMdmSysService.get();
		mdmSysService.setOnlineTime(new Date());
		mdmSysServiceRepository.save(mdmSysService);
	}		
}
