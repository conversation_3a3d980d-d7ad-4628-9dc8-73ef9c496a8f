package clouesp.hes.core.DataImport.service;

import clouesp.hes.common.DataEntity.Asset.MdmAssetHes;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetHesRepository;
import clouesp.hes.common.logger.logger.Logger;
import clouesp.hes.common.logger.logger.LoggerLevel;
import clouesp.hes.core.DataImport.config.ServerConfig;
import clouesp.hes.core.DataImport.handler.DataFileHandler;
import clouesp.hes.core.DataImport.service.Data.MeterSdpMapService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileFilter;
import java.text.SimpleDateFormat;
import java.util.*;
@Slf4j
@Service("dataImportService")
public class DataImportService {
	
	@Autowired
	private DataFileHandler dataFileHandler;
	
	@Autowired
    private ServerConfig serverConfig;	

	@Autowired
	private RtMdmAssetHesRepository rtMdmAssetHesRepository;
	
	@Value("${ftp.localDir}")
	private String localDir;
	
	@Value("${ftp.manualDir}")
	private String manualDir;

	@Autowired
	private MeterSdpMapService meterSdpMapService ;
	private boolean executing = false;

	
	private SimpleDateFormat sdflog = new SimpleDateFormat("MM-dd HH:mm:ss");
	
    private List<String> getFilePaths(String path, int index){
        File file = new File(path);
        FileFilter ff = new FileFilter() {
            public boolean accept(File file) {
            	if (file.getName().startsWith("EVENT_"))
            		return false;
                return file.getName().endsWith(".csv");
            }
        };
        File[] tempList = file.listFiles(ff);
        
		Arrays.sort(tempList, new Comparator<File>() {
			public int compare(File f1, File f2) {
				String[] nameSplit1 = f1.getName().split("_");
				String[] nameSplit2 = f2.getName().split("_");
				String name1 = nameSplit1[index];
				String name2 = nameSplit2[index];
				if (name1.equals(name2)) {
					long diff = f1.lastModified() - f2.lastModified();
					if (diff > 0)
						return 1;
					else if (diff == 0)
						return 0;
					else
						return -1;
				} else {
					if("MONTH".equals(name1)) {
						return 1;
					} else if("MONTH".equals(name2)) {
						return -1;
					} else if("DAY".equals(name1)) {
						return 1;
					} else if("DAY".equals(name2)) {
						return -1;
					}
					return 0;
				}

			}

			public boolean equals(Object obj) {
				return true;
			}

		});
       
        List<String> names = new ArrayList<String>();
        
        for (int i = 0; i < tempList.length; i++){
            if (tempList[i].isFile()){
                names.add(tempList[i].getPath());
            }
        }
        
        return names;
    }        
	
	public void execute() {		
		if (executing) {
			return;
		}
		executing = true;



		try {

			String logInfo = "Start import data";
			meterSdpMapService.RunTimerLoad();
			//System.out.println("[" + sdflog.format(new Date()) + "] " + logInfo);
			log.info(logInfo);
			Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "System", logInfo);

			List<String> localFilePaths = getFilePaths(localDir, 0);
			for (String filePath : localFilePaths) {
				File srcFile = new File(filePath);
				if (!srcFile.exists()) {
					continue;
				}
				if (System.currentTimeMillis() - srcFile.lastModified() < 1000 * 300) {
					continue;
				}
				dataFileHandler.handleCsvFiles(filePath, null);
			}

			localFilePaths = getFilePaths(manualDir, 1);
			for (String filePath : localFilePaths) {
				File srcFile = new File(filePath);
				if (!srcFile.exists()) {
					continue;
				}
				if (System.currentTimeMillis() - srcFile.lastModified() < 1000 * 600) {
					continue;
				}
				String fileName = srcFile.getName();
				String[] splitFileName = fileName.split("_");
				String dataType = splitFileName[0];

				dataFileHandler.handleCsvFiles(filePath, dataType);
			}

			//check all assetHes
			List<String> localDirList = new ArrayList<>();		
			List<MdmAssetHes> mdmAssetHess = rtMdmAssetHesRepository.findAll();
			for (MdmAssetHes mdmAssetHes : mdmAssetHess) {
				localDirList.add(mdmAssetHes.getLocalFileDir());
				Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "System", "Data Import: HesID = " + mdmAssetHes.getHesId() + " , Data import Dir = " + mdmAssetHes.getLocalFileDir());
			}
			for (String dirPath : localDirList) {

				List<String> localFilePathTmps = getFilePaths(dirPath, 0);
				for (String filePath : localFilePathTmps) {
					File srcFile = new File(filePath);
					if (!srcFile.exists()) {
						continue;
					}
					if (System.currentTimeMillis() - srcFile.lastModified() < 1000 * 300) {
						continue;
					}
					dataFileHandler.handleCsvFiles(filePath, null);
				}
			}
			executing = false;

			logInfo = "End import data";
			//System.out.println("[" + sdflog.format(new Date()) + "] " + logInfo);
			log.info(logInfo);
			Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "System", logInfo);
		}catch (Exception e){
			System.out.println("localDir = " + localDir);
			System.out.println("manualDir = " + manualDir);
			log.error("DataImport Error : " , e);
		}finally {
			executing = false;
		}
	}
}
