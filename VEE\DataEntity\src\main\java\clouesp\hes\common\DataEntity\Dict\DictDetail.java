package clouesp.hes.common.DataEntity.Dict;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.IdClass;
import javax.persistence.Table;

@Entity
@Table(name= "mdm_dict_detail")
@IdClass(DictDetailPK.class)
public class DictDetail {
	@Id
	@Column(name = "dict_id", nullable = false, columnDefinition = "varchar(32)")
	private String dictId;
	@Id
	@Column(name = "inner_value")
	private Integer innerValue;
	@Column(name = "gui_display_name")
	private String guiDisplayName;
	@Column(name = "readme")
	private String readme;
	
	public String getDictId() {
		return dictId;
	}
	public void setDictId(String dictId) {
		this.dictId = dictId;
	}
	public Integer getInnerValue() {
		return innerValue;
	}
	public void setInnerValue(Integer innerValue) {
		this.innerValue = innerValue;
	}
	public String getGuiDisplayName() {
		return guiDisplayName;
	}
	public void setGuiDisplayName(String guiDisplayName) {
		this.guiDisplayName = guiDisplayName;
	}
	public String getReadme() {
		return readme;
	}
	public void setReadme(String readme) {
		this.readme = readme;
	}
}
