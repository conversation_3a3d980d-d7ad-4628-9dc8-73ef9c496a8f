package clouesp.hes.common.DataModel.api;

import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;

/**
 * 文件名：AjaxJson.java 版权：Copyright by Power7000g Team 描述：ajax请求返回结构 修改人：严浪
 * 修改时间：2017年3月25日 跟踪单号： 修改单号： 修改内容：
 */
public class AjaxJson {
	private boolean success = true;// 是否成功
	private String msg = "操作成功";// 提示信息
	private Object obj = null;// 其他信息
	private Map<String, Object> attributes;// 其他参数

	public boolean isSuccess() {
		return success;
	}

	public String getMsg() {
		return msg;
	}

	public Object getObj() {
		return obj;
	}

	public Map<String, Object> getAttributes() {
		return attributes;
	}

	public void setSuccess(boolean success) {
		this.success = success;
	}

	public void setErrorMsg(String msg) {
		this.msg = msg;
		this.success = false;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public void setObj(Object obj) {
		this.obj = obj;
	}

	public void setAttributes(Map<String, Object> attributes) {
		this.attributes = attributes;
	}
	/**
	 * put进 attributes
	 * @param key
	 * @param value
	 */
	public void put(String key,Object value){
		if(this.attributes==null){
			this.attributes=new HashMap<String, Object>();
		}
		this.attributes.put(key, value);
	}
	

	@Override
	public String toString() {
		JSONObject json = new JSONObject();
		json.put("success", success);
		json.put("msg", msg);
		json.put("obj", obj);
		json.put("attributes", attributes);
		return json.toString();
	}

}
