package clouesp.hes.common.DataRepository.Persistence.Asset;

import clouesp.hes.common.DataEntity.Asset.AssetMeter;
import clouesp.hes.common.DataEntity.Asset.MdmAssetVeeGroup;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface MdmAssetVeeGroupRepository extends JpaRepository<MdmAssetVeeGroup, String>{

    List<MdmAssetVeeGroup> findAllByName(String name);
}
