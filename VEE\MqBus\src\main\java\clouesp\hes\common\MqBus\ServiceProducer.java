package clouesp.hes.common.MqBus;

import java.util.List;

import org.apache.rocketmq.client.ClientConfig;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.MessageQueueSelector;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageQueue;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;

@Component
public class ServiceProducer {
	
	public DefaultMQProducer start(String namesrvAddr ,String mqGroup) {
		DefaultMQProducer producer = null;
		try {
			producer = new DefaultMQProducer(mqGroup);
			producer.setNamesrvAddr(namesrvAddr);
			producer.setSendMsgTimeout(60000);
			producer.setMqClientApiTimeout(300000);
			producer.start();
		}
		catch(Exception e) {
			producer = null;
			e.printStackTrace();
		}
		return producer;
	}
	
	public <T> void sendMsg(DefaultMQProducer producer, MQMsg<T> mqMsg) {
		try {					
			String jsonStr = JSON.toJSONString(mqMsg);		
			Message message = new Message(mqMsg.getTopic(), mqMsg.getTags(),
					jsonStr.getBytes(RemotingHelper.DEFAULT_CHARSET));		
//			producer.send(message);
			
			producer.send(message, new MessageQueueSelector() {
			    public MessageQueue select(List<MessageQueue> list, Message message, Object o) {
			        Integer id = (Integer) o;
			        int index = id % list.size();
			        return list.get(index);
			    }
			},0);
			
		}
		catch(Exception e) {
			e.printStackTrace();
		}
	}
}
