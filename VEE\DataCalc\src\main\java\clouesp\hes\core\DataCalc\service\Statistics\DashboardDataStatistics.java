package clouesp.hes.core.DataCalc.service.Statistics;

import clouesp.hes.common.DataEntity.Statistics.MeterStatistics;
import clouesp.hes.common.DataEntity.Statistics.ObjectStatistics;
import clouesp.hes.common.DataMode.Statistics.OutageEvtStatics;
import clouesp.hes.common.DataEntity.System.SysOrg;
import clouesp.hes.common.DataMode.Statistics.MdmDataOutageStatistics;
import clouesp.hes.common.DataMode.Statistics.MdmDataOutageStatisticsPK;
import clouesp.hes.common.DataMode.Statistics.MdmStatisticsObj;
import clouesp.hes.common.DataRepository.Persistence.Statistics.MeterStatisticsRepository;
import clouesp.hes.common.DataRepository.Persistence.Statistics.ObjectStatisticsRepository;
import clouesp.hes.common.DataRepository.Persistence.System.SysOrgRepository;
import clouesp.hes.common.logger.logger.Logger;
import clouesp.hes.common.logger.logger.LoggerLevel;
import clouesp.hes.core.DataCalc.config.ServerConfig;
import clouesp.hes.core.DataCalc.service.StatisData.StatisticsAccessService;
import jline.internal.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;

@Service("dashboardDataStatistics")
public class DashboardDataStatistics {
    @Autowired
    private SysOrgRepository sysOrgRepository;
    @Autowired
    private MeterStatisticsRepository meterStatisticsRepository;

    @Autowired
    private ObjectStatisticsRepository objectStatisticsRepository;

    @Autowired
    private StatisticsAccessService statisticsAccessService;

//
//
//    @Autowired
//    private OutageEvtStaticsRepository outageEvtStaticsRepository ;

    @Autowired
    ServerConfig serverConfig;

    private long lastSaveTime = System.currentTimeMillis();
    private long lastOutageSaveTime = System.currentTimeMillis();
    private Thread handlerProcThread = null;
    private DataSaveProc dataSaveProc = null;

    private LinkedBlockingQueue<MdmStatisticsObj> statisticsObjQueue = new LinkedBlockingQueue<MdmStatisticsObj>();

    private LinkedBlockingQueue<MdmDataOutageStatistics> mdmOutageStaticObjQueue = new LinkedBlockingQueue<MdmDataOutageStatistics>();

    private List<SysOrg> listSysOrg = new LinkedList<SysOrg>();
    private Map<String, SysOrg> mapSysOrgById = new ConcurrentHashMap<String, SysOrg>();


    private SimpleDateFormat sdflog = new SimpleDateFormat("MM-dd HH:mm:ss");

    public void PutStatisticsData(MdmStatisticsObj mdmStatisticsObj) {
        if (statisticsObjQueue == null) return;
        try {
            statisticsObjQueue.put(mdmStatisticsObj);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }


    public void PutStatisticsOutageData(MdmDataOutageStatistics mdmStatisticsObj) {
        if (mdmOutageStaticObjQueue == null) return;
        try {
            Log.info("PutStatisticsOutageData 1");
            mdmOutageStaticObjQueue.put(mdmStatisticsObj);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public void Init() {
        if (handlerProcThread == null) {
            dataSaveProc = new DataSaveProc();
            handlerProcThread = new Thread(dataSaveProc);
            handlerProcThread.start();
            Log.info("Dashborad Data Statistics Init complete.");
        }
    }

	public void execute() {
		LoadInitData();
		ProvisioningStatistics();
		DataCollectionProgressStatistics();
		DeviceEventsStatistics();
		VeeEventsStatistics();
		VeeDataStatistics();
		LossObjectStatistics();
		BillingStatistics();
        OutageStatistics() ;
	}

    private void LoadInitData() {

        listSysOrg = sysOrgRepository.findAll();
        mapSysOrgById.clear();
        for (SysOrg sysOrg : listSysOrg) {
            if (mapSysOrgById.get(sysOrg.getId()) == null) {
                mapSysOrgById.put(sysOrg.getId(), sysOrg);
            }
        }

    }

    private OrgLevelNodeManage GetOrgLevelNodeManageByMeterList(List<MeterStatistics> meterStatisticsList) {


        OrgLevelNodeManage orgLevelNodeManage = new OrgLevelNodeManage();
        OrgLevelNodeManage.OrgNodeInfo addOrgNodeInfo = orgLevelNodeManage.NewOrgNodeInfo();

        SysOrg sysOrg;

        if (meterStatisticsList == null || meterStatisticsList.isEmpty()) return orgLevelNodeManage;
        for (MeterStatistics meterStatistics : meterStatisticsList) {
            if (meterStatistics == null) continue;
            sysOrg = mapSysOrgById.get(meterStatistics.getOrgId());
            if (sysOrg == null) continue;
            addOrgNodeInfo.setOrgId(sysOrg.getId());
            addOrgNodeInfo.setParentOrgId(sysOrg.getParentOrgTid());
            addOrgNodeInfo.setDbStatisticsCount(meterStatistics.getMeterCount());
            orgLevelNodeManage.AddNode(addOrgNodeInfo);

        }
        for (MeterStatistics meterStatistics : meterStatisticsList) {
            if (meterStatistics == null) continue;
            sysOrg = mapSysOrgById.get(meterStatistics.getOrgId());
            if (sysOrg == null) continue;
            sysOrg = mapSysOrgById.get(sysOrg.getParentOrgTid());
            while (sysOrg != null) {
                addOrgNodeInfo.setOrgId(sysOrg.getId());
                addOrgNodeInfo.setParentOrgId(sysOrg.getParentOrgTid());
                addOrgNodeInfo.setDbStatisticsCount(0);
                orgLevelNodeManage.AddNode(addOrgNodeInfo);
                sysOrg = mapSysOrgById.get(sysOrg.getParentOrgTid());
            }

        }
        orgLevelNodeManage.StatisticsAllNode();
        return orgLevelNodeManage;
    }

    private void ProvisioningStatistics() {

        String logInfo = "Start Provisioning Statistics";
        Log.info(logInfo);

        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -1);
        int totalCount;

        int[] idTypes = {1, 3, 9};//电表运行状态(字典表 5:  1=待安装    2=待投运   3=运行   4 =停运  5=故障  6=新装未核对  7=变更未核对 8=换表 9=拆除)

        List<MeterStatistics>[] listMeterStatistics = new List[idTypes.length];
        OrgLevelNodeManage[] orgLevelNodeManages = new OrgLevelNodeManage[idTypes.length];

        if ( serverConfig.isOracleDb()) {
            listMeterStatistics[0] = meterStatisticsRepository.findOracleYesterdayNewMeterCount();
            listMeterStatistics[1] = meterStatisticsRepository.findOracleYesterdayInstallMeterCount();
            listMeterStatistics[2] = meterStatisticsRepository.findOracleYesterdayRemoveMeterCount();
        }else {
            listMeterStatistics[0] = meterStatisticsRepository.findMysqlYesterdayNewMeterCount();
            listMeterStatistics[1] = meterStatisticsRepository.findMysqlYesterdayInstallMeterCount();
            listMeterStatistics[2] = meterStatisticsRepository.findMysqlYesterdayRemoveMeterCount();
        }
        orgLevelNodeManages[0] = GetOrgLevelNodeManageByMeterList(listMeterStatistics[0]);
        orgLevelNodeManages[1] = GetOrgLevelNodeManageByMeterList(listMeterStatistics[1]);
        orgLevelNodeManages[2] = GetOrgLevelNodeManageByMeterList(listMeterStatistics[2]);


        for (int i = 0; i < idTypes.length; i++) {

            if (orgLevelNodeManages[i] == null) continue;

            for (Map.Entry<String, OrgLevelNodeManage.OrgLevelNode> entry : orgLevelNodeManages[i].getMapNodeList().entrySet()) {

                if (entry.getValue() == null) continue;
                OrgLevelNodeManage.OrgLevelNode orgLevelNode = entry.getValue();
                MdmStatisticsObj mdmStatisticsObj = new MdmStatisticsObj();
                mdmStatisticsObj.setCountCurrent(orgLevelNode.getOrgNodeInfo().getCalcStatisticsCount());

                totalCount = 0;
                for (int k = 0; k < idTypes.length; k++) {
                    OrgLevelNodeManage.OrgLevelNode orgLevelNode1 = orgLevelNodeManages[k].FindNodeByOrgId(orgLevelNode.getOrgNodeInfo().getOrgId());
                    if (orgLevelNode1 != null) totalCount += orgLevelNode1.getOrgNodeInfo().getCalcStatisticsCount();
                }

                if (totalCount != 0)
                    mdmStatisticsObj.setPercent((orgLevelNode.getOrgNodeInfo().getCalcStatisticsCount() * 100.0) / totalCount);
                else
                    mdmStatisticsObj.setPercent(0.0);

                mdmStatisticsObj.getMdmStatisticsObjPK().setOrgId(orgLevelNode.getOrgNodeInfo().getOrgId());
                mdmStatisticsObj.getMdmStatisticsObjPK().setId("1145010");
                mdmStatisticsObj.getMdmStatisticsObjPK().setIdType("" + idTypes[i]);
                mdmStatisticsObj.getMdmStatisticsObjPK().SetTvOnlyDate(cal.getTime());
                //  mdmStatisticsObj.getMdmStatisticsObjPK().setTv(cal.getTime());
                PutStatisticsData(mdmStatisticsObj);

            }
        }

        logInfo = "End Provisioning Statistics";
        Log.info(logInfo);
    }

    private void DataCollectionProgressStatistics() {
        String logInfo = "Start Data Collection Progress Statistics";

        Log.info(logInfo);
        logInfo = "End Data Collection Progress Statistics";

        List<Integer> innerList;
        int totalCount;

        innerList = meterStatisticsRepository.findDictInnerValueByDictId("1107");
        if (innerList == null || innerList.isEmpty()) {
            Log.info(logInfo);
            return;
        }

        List<MeterStatistics>[] listProcess = new List[innerList.size()];
        OrgLevelNodeManage[] orgLevelNodeManages = new OrgLevelNodeManage[innerList.size()];
        int schemeType;

        Calendar cal;
        try {
            statisticsAccessService.deleteAllProgessStatistics();
        } catch (Exception e) {
            e.printStackTrace();
        }
        for (int noDay = 0; noDay <  7; noDay++) {
            cal = Calendar.getInstance();
            if (noDay > 0) cal.add(Calendar.DATE, -noDay);
            for (int i = 0; i < innerList.size(); i++) {
                if (innerList.get(i) == 102 || innerList.get(i) == 103 || innerList.get(i) == 109)
                    schemeType = 1;
                else
                    schemeType = 2;
                if ( serverConfig.isOracleDb() ) {
                    listProcess[i] = meterStatisticsRepository.findOraclePrgressCountByDataTypeAndDay(schemeType, innerList.get(i), noDay);
                }else {
                    listProcess[i] = meterStatisticsRepository.findMysqlPrgressCountByDataTypeAndDay(schemeType, innerList.get(i), noDay);
                }
                orgLevelNodeManages[i] = GetOrgLevelNodeManageByMeterList(listProcess[i]);
            }
            for (int i = 0; i < innerList.size(); i++) {

                if (orgLevelNodeManages[i] == null) continue;

                for (Map.Entry<String, OrgLevelNodeManage.OrgLevelNode> entry : orgLevelNodeManages[i].getMapNodeList().entrySet()) {

                    if (entry.getValue() == null) continue;
                    OrgLevelNodeManage.OrgLevelNode orgLevelNode = entry.getValue();
                    MdmStatisticsObj mdmStatisticsObj = new MdmStatisticsObj();
                    mdmStatisticsObj.setCountCurrent(orgLevelNode.getOrgNodeInfo().getCalcStatisticsCount());

                    totalCount = 0;
                    for (int k = 0; k < innerList.size(); k++) {
                        OrgLevelNodeManage.OrgLevelNode orgLevelNode1 = orgLevelNodeManages[k].FindNodeByOrgId(orgLevelNode.getOrgNodeInfo().getOrgId());
                        if (orgLevelNode1 != null)
                            totalCount += orgLevelNode1.getOrgNodeInfo().getCalcStatisticsCount();
                    }

                    if (totalCount != 0)
                        mdmStatisticsObj.setPercent((orgLevelNode.getOrgNodeInfo().getCalcStatisticsCount() * 100.0) / totalCount);
                    else
                        mdmStatisticsObj.setPercent(0.0);

                    mdmStatisticsObj.getMdmStatisticsObjPK().setOrgId(orgLevelNode.getOrgNodeInfo().getOrgId());
                    mdmStatisticsObj.getMdmStatisticsObjPK().setId("1145020");
                    mdmStatisticsObj.getMdmStatisticsObjPK().setIdType("" + innerList.get(i));
                    mdmStatisticsObj.getMdmStatisticsObjPK().SetTvOnlyDate(cal.getTime());
                    //  mdmStatisticsObj.getMdmStatisticsObjPK().setTv(cal.getTime());
                    PutStatisticsData(mdmStatisticsObj);

                }
            }
        }

        Log.info(logInfo);
    }

    private void ObjectYesterdayStatisticsByPara(List<ObjectStatistics> objectStatisticsList ,String  statisId  , Date tv){
        Map<String, List<ObjectStatistics>> mapListObjects = new ConcurrentHashMap<String, List<ObjectStatistics>>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        int totalCount;
        for (ObjectStatistics objectStatistics : objectStatisticsList) {
            if ( objectStatistics == null ){
                Log.info("objectStatistics = null , StatisId = " + statisId + " , Tv = " + sdf.format(tv));
                continue;
            }
            if (objectStatistics.getObjectStatisticsPK() == null ){
                Log.info("objectStatistics.getObjectStatisticsPK() = null , Object_count = " + objectStatistics.getObjectCount() + " , StatisId = " + statisId + " , Tv = " + sdf.format(tv));
                continue;
            }
            List<ObjectStatistics> tmpObjectStatitisList = mapListObjects.get(objectStatistics.getObjectStatisticsPK().getObjectId());
            if (tmpObjectStatitisList != null) {
                tmpObjectStatitisList.add(objectStatistics);
            } else {
                tmpObjectStatitisList = new ArrayList<ObjectStatistics>();
                tmpObjectStatitisList.add(objectStatistics);
                mapListObjects.put(objectStatistics.getObjectStatisticsPK().getObjectId(), tmpObjectStatitisList);
            }
        }

        List<MeterStatistics>[] listProcess = new List[mapListObjects.size()];
        OrgLevelNodeManage[] orgLevelNodeManages = new OrgLevelNodeManage[mapListObjects.size()];
        String []  arrayObjects = new String[mapListObjects.size()]    ;


        int i = 0;
        for (List<ObjectStatistics> curObjectStatistList : mapListObjects.values()) {

            listProcess[i] = new ArrayList<MeterStatistics>();
            for (ObjectStatistics objectStatistics : curObjectStatistList) {
                MeterStatistics meterStatistics = new MeterStatistics();
                meterStatistics.setOrgId(objectStatistics.getObjectStatisticsPK().getOrgId());
                meterStatistics.setMeterCount(objectStatistics.getObjectCount());
                listProcess[i].add(meterStatistics);
                if ( arrayObjects[i] == null )  arrayObjects[i] = objectStatistics.getObjectStatisticsPK().getObjectId() ;
            }
            orgLevelNodeManages[i] = GetOrgLevelNodeManageByMeterList(listProcess[i]);

            i++;
        }
        for (i = 0; i < mapListObjects.size(); i++) {

            if (orgLevelNodeManages[i] == null) continue;

            for (Map.Entry<String, OrgLevelNodeManage.OrgLevelNode> entry : orgLevelNodeManages[i].getMapNodeList().entrySet()) {

                if (entry.getValue() == null) continue;
                OrgLevelNodeManage.OrgLevelNode orgLevelNode = entry.getValue();
                MdmStatisticsObj mdmStatisticsObj = new MdmStatisticsObj();
                mdmStatisticsObj.setCountCurrent(orgLevelNode.getOrgNodeInfo().getCalcStatisticsCount());

                totalCount = 0;
                for (int k = 0; k < mapListObjects.size(); k++) {
                    OrgLevelNodeManage.OrgLevelNode orgLevelNode1 = orgLevelNodeManages[k].FindNodeByOrgId(orgLevelNode.getOrgNodeInfo().getOrgId());
                    if (orgLevelNode1 != null) totalCount += orgLevelNode1.getOrgNodeInfo().getCalcStatisticsCount();
                }

                if (totalCount != 0)
                    mdmStatisticsObj.setPercent((orgLevelNode.getOrgNodeInfo().getCalcStatisticsCount() * 100.0) / totalCount);
                else
                    mdmStatisticsObj.setPercent(0.0);

                mdmStatisticsObj.getMdmStatisticsObjPK().setOrgId(orgLevelNode.getOrgNodeInfo().getOrgId());
                mdmStatisticsObj.getMdmStatisticsObjPK().setId(statisId);
                mdmStatisticsObj.getMdmStatisticsObjPK().setIdType(arrayObjects[i]);
                mdmStatisticsObj.getMdmStatisticsObjPK().SetTvOnlyDate(tv);
                //  mdmStatisticsObj.getMdmStatisticsObjPK().setTv(cal.getTime());
                PutStatisticsData(mdmStatisticsObj);

            }
        }

    }

    private void DeviceEventsStatistics() {
        String logInfo = "Start Device Events Statistics";

        Log.info(logInfo);
        logInfo = "End  Device Events Statistics";

        List<ObjectStatistics> objectStatisticsList;

        Calendar cal;
        cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -1);

        if ( serverConfig.isOracleDb() ) {
            objectStatisticsList = objectStatisticsRepository.findOracleYesterdayDeviceEventCount();
        }else {
            objectStatisticsList = objectStatisticsRepository.findMysqlYesterdayDeviceEventCount();
        }
        if (objectStatisticsList == null || objectStatisticsList.isEmpty()) {
            Log.info(logInfo);
            return;
        }

       ObjectYesterdayStatisticsByPara(objectStatisticsList, "1145030", cal.getTime()) ;
        Log.info(logInfo + " ObjectCount = " + objectStatisticsList.size());
    }

    private void VeeEventsStatistics() {
        String logInfo = "Start VEE Events Statistics";

        Log.info(logInfo);
        logInfo = "End  VEE Events Statistics";

        List<ObjectStatistics> objectStatisticsList;

        Calendar cal;
        cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -1);

        if ( serverConfig.isOracleDb() ) {
            objectStatisticsList = objectStatisticsRepository.findOracleYesterdayVeeEventCount();
        }else {
            objectStatisticsList = objectStatisticsRepository.findMysqlYesterdayVeeEventCount();
        }
        if (objectStatisticsList == null || objectStatisticsList.isEmpty()) {
            Log.info(logInfo);
            return;
        }

        ObjectYesterdayStatisticsByPara(objectStatisticsList, "1145040",cal.getTime()) ;
        Log.info(logInfo + " ObjectCount = " + objectStatisticsList.size());
    }

    private void VeeDataStatistics() {

        String logInfo = "Start VEE Data Statistics";

        Log.info(logInfo);
        logInfo = "End  VEE Data Statistics";

        List<ObjectStatistics> objectStatisticsList;

        Calendar cal;
        cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -1);

        if ( serverConfig.isOracleDb() ) {
            objectStatisticsList = objectStatisticsRepository.findOracleYesterdayVeeDataCount();
        }else {
            objectStatisticsList = objectStatisticsRepository.findMysqlYesterdayVeeDataCount();
        }
        if (objectStatisticsList == null || objectStatisticsList.isEmpty()) {
            Log.info(logInfo);
            return;
        }

        ObjectYesterdayStatisticsByPara(objectStatisticsList, "1145050",cal.getTime()) ;
        Log.info(logInfo+ " ObjectCount = " + objectStatisticsList.size());
    }

    private void LossObjectStatistics() {

        String logInfo = "Start Loss Object Statistics";

        Log.info(logInfo);
        logInfo = "End  Loss Object Statistics";

        List<ObjectStatistics> objectStatisticsList;


        Calendar cal;
        cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -1);

        if ( serverConfig.isOracleDb() ) {
            objectStatisticsList = objectStatisticsRepository.findOracleYesterdayLossDataCount();
        }else {
            objectStatisticsList = objectStatisticsRepository.findMysqlYesterdayLossDataCount();
        }
        if (objectStatisticsList == null || objectStatisticsList.isEmpty()) {
            Log.info(logInfo);
            return;
        }

        ObjectYesterdayStatisticsByPara(objectStatisticsList, "1145060",cal.getTime()) ;
        Log.info(logInfo+ " ObjectCount = " + objectStatisticsList.size());
    }

    private void BillingStatistics() {
        String logInfo = "Start Billing  Statistics";

        Log.info(logInfo);
        logInfo = "End Billing  Statistics";

        List<ObjectStatistics> objectStatisticsList;

        Calendar cal;
        cal = Calendar.getInstance();
        cal.add(Calendar.MONTH, -1);

        if ( serverConfig.isOracleDb() ) {
            objectStatisticsList = objectStatisticsRepository.findOracleLastMonthsBillingCount();
        }else {
            objectStatisticsList = objectStatisticsRepository.findMysqlLastMonthsBillingCount();
        }
        if (objectStatisticsList == null || objectStatisticsList.isEmpty()) {
            Log.info(logInfo);
            return;
        }

        ObjectYesterdayStatisticsByPara(objectStatisticsList, "1145070",cal.getTime()) ;
        Log.info(logInfo+ " ObjectCount = " + objectStatisticsList.size());

    }

    private  void OutageObjectAssign(OutageEvtStatics srcObj , MdmDataOutageStatistics destObj){

        if ( srcObj == null || destObj == null) return;

        MdmDataOutageStatisticsPK  mdmDataOutageStatisticsPK = destObj.getMdmDataOutageStatisticsPK() ;

        if ( mdmDataOutageStatisticsPK == null ){
            mdmDataOutageStatisticsPK = new MdmDataOutageStatisticsPK() ;
            destObj.setMdmDataOutageStatisticsPK(mdmDataOutageStatisticsPK);
        }

        mdmDataOutageStatisticsPK.setOrgId(srcObj.getOrgId());
        mdmDataOutageStatisticsPK.setLineId(srcObj.getLinId());

        destObj.setSdpTotalNum(srcObj.getSdpTotalNum());
        destObj.setTotalNum(srcObj.getTotalNum());
        destObj.setTotalTime(srcObj.getTotalTime());
        destObj.setAvgNum(srcObj.getAvgNum());
        destObj.setAvgTime(srcObj.getAvgTime());

    }

    public void OutageStatistics()  {
        String logInfo = "Start Outage  Statistics";

        Log.info(logInfo);
        logInfo = "End Outage  Statistics";

        List<OutageEvtStatics> objectStatisticsList = null ;
        MdmDataOutageStatistics mdmStatisticsObj = null ;

        Calendar cal;
        cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -1);
        cal.set(Calendar.MILLISECOND , 0 ) ;
        cal.set(Calendar.SECOND , 0 ) ;
        cal.set(Calendar.MINUTE,0);
        cal.set(Calendar.HOUR_OF_DAY,0);

        //昨天统计
        try{
             objectStatisticsList =  statisticsAccessService.findYesterdayOutageStatics(); // outageEvtStaticsRepository.findYesterdayOutageStatics() ;
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (objectStatisticsList != null && !objectStatisticsList.isEmpty()) {
      //      Log.info(Yesterday Outage statists count: " + objectStatisticsList.size() );
            for( OutageEvtStatics outageEvtStatics : objectStatisticsList ){
                if ( outageEvtStatics == null || outageEvtStatics.getLinId() == null  ){
                    Log.info("findYesterdayOutageStatics error : " + outageEvtStatics == null ? " null " : outageEvtStatics.toString());
                    Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, serverConfig.getServiceId(), "0", "Database","findYesterdayOutageStatics error : " + outageEvtStatics == null ? " null " : outageEvtStatics.toString()) ;
                    continue;
                }
                mdmStatisticsObj = new MdmDataOutageStatistics() ;
                OutageObjectAssign(outageEvtStatics ,mdmStatisticsObj) ;
                mdmStatisticsObj.getMdmDataOutageStatisticsPK().setSdpType(0);
                mdmStatisticsObj.getMdmDataOutageStatisticsPK().setTimeType(2);
                mdmStatisticsObj.getMdmDataOutageStatisticsPK().setTv(cal.getTime());
                PutStatisticsOutageData(mdmStatisticsObj ) ;
            }
        }

        //当前月统计
        cal.add(Calendar.DATE, 1);
        cal.set(Calendar.DAY_OF_MONTH,1);//设置为1号,当前日期既为本月第一天
        try{
              objectStatisticsList = statisticsAccessService.findCurMonthOutageStatics();  // outageEvtStaticsRepository.findCurMonthOutageStatics() ;
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (objectStatisticsList != null && !objectStatisticsList.isEmpty()) {
        //    Log.info(Cur Month Outage statists count: " + objectStatisticsList.size() );
            for( OutageEvtStatics outageEvtStatics : objectStatisticsList ){
                if ( outageEvtStatics == null || outageEvtStatics.getLinId() == null  ){
                    Log.info("findCurMonthOutageStatics error : " + outageEvtStatics == null ? " null " : outageEvtStatics.toString());
                    Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, serverConfig.getServiceId(), "0", "Database","findCurMonthOutageStatics error : " + outageEvtStatics == null ? " null " : outageEvtStatics.toString()) ;
                    continue;
                }
                mdmStatisticsObj = new MdmDataOutageStatistics() ;
                OutageObjectAssign(outageEvtStatics ,mdmStatisticsObj) ;
                mdmStatisticsObj.getMdmDataOutageStatisticsPK().setSdpType(0);
                mdmStatisticsObj.getMdmDataOutageStatisticsPK().setTimeType(3);
                mdmStatisticsObj.getMdmDataOutageStatisticsPK().setTv(cal.getTime());
                PutStatisticsOutageData(mdmStatisticsObj );
            }
        }




        Log.info(logInfo);

    }

    private class DataSaveProc implements Runnable {
        private int dataSaveObj(int count) {
            if (statisticsObjQueue.size() == 0)
                return 0;
            if (System.currentTimeMillis() - lastSaveTime < 15000
                    && statisticsObjQueue.size() < 100)
                return 0;
            if (count > statisticsObjQueue.size())
                count = statisticsObjQueue.size();
            lastSaveTime = System.currentTimeMillis();
            List<MdmStatisticsObj> datas = new ArrayList<MdmStatisticsObj>();
            statisticsObjQueue.drainTo(datas, count);
            try {
                long startTime = System.currentTimeMillis();

                statisticsAccessService.batchSaveMdmStatisticsObj(datas);
                String loggerInfo = "Into mdm_statistics_obj[count: " + count + ", time: "
                        + (System.currentTimeMillis() - startTime) + "]";
                Log.info(loggerInfo);
                Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "Database", loggerInfo);


            } catch (Exception e) {
 				e.printStackTrace();
                statisticsObjQueue.addAll(datas);
            }
            return count;
        }


        private int dataSaveOutageObj(int count) {

    //        Log.info(mdmOutageStaticObjQueue size = " + mdmOutageStaticObjQueue.size() + " System.currentTimeMillis() - lastOutageSaveTime = " + (System.currentTimeMillis() - lastOutageSaveTime));
            if (mdmOutageStaticObjQueue.size() == 0)  return 0;
            if (System.currentTimeMillis() - lastOutageSaveTime < 15000  && mdmOutageStaticObjQueue.size() < 100)  return 0;
            if (count > mdmOutageStaticObjQueue.size())  count = mdmOutageStaticObjQueue.size();
       //     Log.info(dataSaveOutageObj Insert into DB ");
            lastOutageSaveTime = System.currentTimeMillis();
            List<MdmDataOutageStatistics> datas = new ArrayList<MdmDataOutageStatistics>();
            mdmOutageStaticObjQueue.drainTo(datas, count);
            if ( datas == null || datas.isEmpty() ) return  0 ;
            try {
                long startTime = System.currentTimeMillis();

                for(MdmDataOutageStatistics mdmDataOutageStatistics : datas){
                    if ( mdmDataOutageStatistics.getMdmDataOutageStatisticsPK() == null || mdmDataOutageStatistics.getMdmDataOutageStatisticsPK().getLineId() == null ){
                        Log.info("mdmDataOutageStatistics error data: " + mdmDataOutageStatistics == null ? " null " : mdmDataOutageStatistics.toString());
                        Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, serverConfig.getServiceId(), "0", "Database", "mdmDataOutageStatistics error data: " + mdmDataOutageStatistics == null ? " null " : mdmDataOutageStatistics.toString());
                    }

                }



                statisticsAccessService.batchSaveMdmStatisticsOutageObj(datas);
                String loggerInfo = "Into mdm_data_outage_statistics[count: " + count + ", time: "
                        + (System.currentTimeMillis() - startTime) + "]";
                Log.info(loggerInfo);
                Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "Database", loggerInfo);


            } catch (Exception e) {
 			    e.printStackTrace();
              //  mdmOutageStaticObjQueue.addAll(datas);
            }
            return count;
        }

        @Override
        public void run() {
            while (true) {
                int retCount = 0;
                int outAgeStatCnt = 0 ;
                retCount = dataSaveObj(1000);
                outAgeStatCnt = dataSaveOutageObj(100) ;
                if (retCount == 0 && outAgeStatCnt == 0 ) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        // TODO Auto-generated catch block
                        e.printStackTrace();
                    }
                }
            }
        }
    }

}
