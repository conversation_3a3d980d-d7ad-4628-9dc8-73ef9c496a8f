package clouesp.hes.common.DataRepository.RealTime.Asset;

import clouesp.hes.common.DataEntity.Asset.MdmAssetVEERuleDataSource;
import clouesp.hes.common.DataEntity.Asset.MdmAssetVEERuleDataSourcePK;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface RtMdmAssetVEERuleDataSourceRepository extends JpaRepository<MdmAssetVEERuleDataSource, MdmAssetVEERuleDataSourcePK>{
	List<MdmAssetVEERuleDataSource> findByRuleId(String ruleId);
	List<MdmAssetVEERuleDataSource> findByRuleIdAndSchemeType(String ruleId, Integer schemeType);


	@Query(value ="SELECT distinct(source_event_type)  FROM MDM_ASSET_VEE_RULE_DATASOURCE  where source_event_type != '0'",nativeQuery = true)
	List<String> findCalEventIds();
}
