package clouesp.hes.common.DataEntity.Dict;

import java.io.Serializable;

public class DictDetailPK implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 7918650198003158024L;
	private String dictId;
	private Integer innerValue;
	public String getDictId() {
		return dictId;
	}
	public void setDictId(String dictId) {
		this.dictId = dictId;
	}
	public Integer getInnerValue() {
		return innerValue;
	}
	public void setInnerValue(Integer innerValue) {
		this.innerValue = innerValue;
	}
	
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((dictId == null) ? 0 : dictId.hashCode());
		result = prime * result
				+ ((innerValue == null) ? 0 : innerValue.hashCode());
		return result;
	}
	
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		DictDetailPK other = (DictDetailPK) obj;
		if (dictId == null) {
			if (other.dictId != null)
				return false;
		} else if (!dictId.equals(other.dictId))
			return false;
		if (innerValue == null) {
			if (other.innerValue != null)
				return false;
		} else if (!innerValue.equals(other.innerValue))
			return false;	
		return true;
	}					
}
