package clouesp.hes.core.DataCalc.schedule.Task;

import clouesp.hes.common.DataEntity.Asset.MdmAssetCalcObj;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetCalcObjRepository;
import clouesp.hes.common.MqBus.MQMsg;
import clouesp.hes.common.MqBus.ServiceProducer;
import clouesp.hes.common.logger.logger.Logger;
import clouesp.hes.common.logger.logger.LoggerLevel;
import clouesp.hes.core.DataCalc.config.ServerConfig;
import clouesp.hes.core.DataCalc.service.Statistics.DashboardDataStatistics;
import jline.internal.Log;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Component("scheduleCalcTask")
public class ScheduleCalcTask {
	@Autowired
	private RtMdmAssetCalcObjRepository rtMdmAssetCalcObjRepository;
	
	@Autowired
	private ServerConfig serverConfig;

	@Autowired
	private ServiceProducer serviceProducer;
	private String producerGroup;
	private String producerTopic;
	private String producerTags;
	private DefaultMQProducer producer;
	private String serviceId;
	
	private boolean executing = false;
	
	private SimpleDateFormat sdflog = new SimpleDateFormat("MM-dd HH:mm:ss");
	
	public void init(String serviceId) {
		String prefix = "VEE";
		String producerInfo = "CALCOBJ.CALCULATION.CALC";
		String[] infos = producerInfo.split("\\.");
		producerGroup = prefix + "_" + infos[0];
		producerTopic = prefix + "_" + infos[1];
		producerTags = prefix + "_" + infos[2];
		String namesrvAddr = serverConfig.getNamesrvAddr();
		producer = serviceProducer.start(namesrvAddr, producerGroup);
		Log.info("ScheduleCalcTask init finish : namesrvAddr = " + namesrvAddr + " , producerGroup = " + producerGroup + " , producerTopic = " + producerTopic + " , producerTags = " + producerTags);
	}
		
	public void dispatch() {
		String logInfo = null;
		if (executing) {
			return;
		}
		executing = true;
		
		logInfo = "Start Calcultion Object(Task)";
		Log.info(logInfo);
		Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "Calculation", logInfo); 
		
		List<MdmAssetCalcObj> calcObjs = rtMdmAssetCalcObjRepository.findByCalcEnable(1);

		logInfo = "Calcultion Object: " + calcObjs.size() + " , ScheduleCalcTask = " + producerTopic + " , producerTags = " + producerTags  ;
		Log.info(logInfo);

		if(calcObjs != null && calcObjs.size() > 0) {
			for (MdmAssetCalcObj calcObj : calcObjs) {

				//Log.info( calcObjs : " + calcObjs.toString());
				MQMsg<MdmAssetCalcObj> mqMsg = new MQMsg<MdmAssetCalcObj>();
				mqMsg.setFromServiceId(serviceId);
				mqMsg.setFromServiceType("CALC");
				mqMsg.setFromId(calcObj.getId());
				mqMsg.setFromType("CALCOBJ");
				mqMsg.setTopic(producerTopic);
				mqMsg.setTags(producerTags);
				calcObj.setManaual(false);
				mqMsg.setLoad(calcObj);
				serviceProducer.sendMsg(producer, mqMsg);
				
			}
		}

		executing = false;
		
		logInfo = "End Calcultion Object";
		Log.info(logInfo);
		Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "Calculation", logInfo);
	}
}
