<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ReportMapper">

    <select id="getReportTemplateList" resultType="AssetReportTemplate"  parameterType="Request">
        select
        *
        from
        asset_report_template
        where
        1 = 1
        <if test="entity">
            <if test="entity.folderId != null and entity.folderId != ''">
                and folder_id = #{entity.folderId}
            </if>

            <if test="entity.reportType != null and entity.reportType != ''">
                and report_type = #{entity.reportType}
            </if>
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <select id="getDictDataitemGroupList" resultType="DictDataitemGroup"  parameterType="Request">
        select
        *
        from
        dict_dataitem_group
        where
        sort_id > 0
        <if test="entity">
            <if test="entity.name != null and entity.name != ''">
                and name like concat(concat('%', #{entity.name}),'%')
            </if>
            <if test="entity.protocolId != null and entity.protocolId != ''">
                and protocol_id = #{entity.protocolId}
            </if>
            <if test="entity.appType != null and entity.appType != ''">
                and app_type = #{entity.appType}
            </if>
             <if test="entity.assetMeterGroupId != null and entity.assetMeterGroupId != ''">
                and ( asset_meter_group_id = #{entity.assetMeterGroupId}  or asset_meter_group_id is null )
            </if>
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <select id="getDataGroupList" resultType="DictDataitemGroup"  parameterType="Request">
        select
        *
        from
        dict_dataitem_group,
        where
        sort_id > 0
        <if test="entity">
            <if test="entity.name != null and entity.name != ''">
                and name like concat(concat('%', #{entity.name}),'%')
            </if>
            <if test="entity.protocolId != null and entity.protocolId != ''">
                and protocol_id = #{entity.protocolId}
            </if>
            <if test="entity.appType != null and entity.appType != ''">
                and app_type = #{entity.appType}
            </if>
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <select id="getDictDataitemGroupMapList" resultType="DictDataitemGroupMap"  parameterType="Request">
        select
        ddgm.*,
        dd.name
        from
        dict_dataitem dd,
        dict_dataitem_group_map ddgm
        where
        ddgm.dataitem_id = dd.id
        and ddgm.sort_id > 0
        <if test="entity">
            <if test="entity.groupId != null and entity.groupId != ''">
                and ddgm.group_id = #{entity.groupId}
            </if>
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <select id="getDictDataitemList" resultType="DictDataitem"  parameterType="Request">
        select
        dd.*
        from
        dict_dataitem dd
        left join dict_dataitem_group_map ddgm on dd.id = ddgm.dataitem_id
        where
        1 = 1
        <if test="entity">
            <if test="entity.name != null and entity.name != ''">
                and dd.name like concat(concat('%', #{entity.name}),'%')
            </if>
            <if test="entity.protocolCode != null and entity.protocolCode != ''">
                and dd.protocol_code like concat(concat('%', #{entity.protocolCode}),'%')
            </if>
            <if test="entity.protocolId != null and entity.protocolId != ''">
                and dd.protocol_id = #{entity.protocolId}
            </if>
            <if test="entity.groupId != null and entity.groupId != ''">
                and ddgm.group_id = #{entity.groupId}
                and ddgm.sort_id > 0
            </if>
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <select id="getDictDeviceModelList" resultType="DictDeviceModel"  parameterType="Request">
        select
        *
        from
        dict_device_model
        where
        1 = 1
        <if test="entity">
            <if test="entity.name != null and entity.name != ''">
                and name like concat(concat('%', #{entity.name}),'%')
            </if>
            <if test="entity.protocolId != null and entity.protocolId != ''">
                and protocol_id = #{entity.protocolId}
            </if>
            <if test="entity.manufacturerId != null and entity.manufacturerId != ''">
                and manufacturer_id = #{entity.manufacturerId}
            </if>
            <if test="entity.deviceType != null">
                and device_type = #{entity.deviceType}
            </if>
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

</mapper>