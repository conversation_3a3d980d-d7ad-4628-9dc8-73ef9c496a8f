import clouesp.hes.common.CommonUtils.ObjectUtils
import clouesp.hes.common.DataEntity.Data.DataReg
import clouesp.hes.common.DataEntity.Data.DataVEEEvent

def regAlwaysZero(Map<String, Object> ruleInfo) {
    Integer result = 0;
    DataVEEEvent event = null;
    if (ruleInfo == null ||
            !ruleInfo.containsKey("datas") ||
            !ruleInfo.containsKey("params") ||
            !ruleInfo.containsKey("schemeType")) {
        return null;
    }

    List<Map<String, Object>> ruleDatas = ruleInfo.get("datas");
    Map<String, Object> ruleParams = ruleInfo.get("params");
    double resultValue = 0;
    if(ruleParams != null && ruleParams.containsKey("resultValue")) {
        resultValue = ruleParams.get("resultValue");
    }
    for(Map<String, Object> ruleData : ruleDatas) {
        String targetClass = ruleData.get("targetClass");
        //获取数据，根据数据周期取值
        String dataKey = "dataListDay";
        Integer schemeType = new Integer(ruleInfo.get("schemeType").toString());
        if(schemeType == 1)
        {
            dataKey = "dataListMinute";
        }
        else if(schemeType == 2)
        {
            dataKey = "dataListDay";
        }
        else if(schemeType == 3)
        {
            dataKey = "dataListMonth";
        }
        List<Map<String, Object>> dataList = ruleData.get(dataKey);
        if(dataList == null) {
            return null;
        }
        if("REG" == targetClass) {
            if(dataList.size() > 0) {
                boolean isNotZero = false;
                for(Map<String, Object> data : dataList) {
                    DataReg dataReg = ObjectUtils.convertMapToObject(data, DataReg.class);
                    Double r0p1 = dataReg.getR0P1();
                    if(r0p1 != null) {
                        if(r0p1.doubleValue() > resultValue) {
                            isNotZero = true;
                            break;
                        }
                    }
                }
                if(!isNotZero) {
                    result = 1;
                }
            }
        }
    }

    if(result.intValue() == 1) {
        event = (DataVEEEvent) ruleInfo.get("event");
    }
    return event;
}