package clouesp.hes.common.DataEntity.Asset;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MdmAssetMeterReplacementPK implements Serializable {
    private String sdpId;
    private Date tv;
    private Integer operationType;

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((sdpId == null) ? 0 : sdpId.hashCode());
        result = prime * result
                + ((tv == null) ? 0 : tv.hashCode());
        result = prime * result
                + ((operationType == null) ? 0 : operationType.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        MdmAssetMeterReplacementPK other = (MdmAssetMeterReplacementPK) obj;
        if (sdpId == null) {
            if (other.sdpId != null)
                return false;
        } else if (!sdpId.equals(other.sdpId))
            return false;
        if (tv == null) {
            if (other.tv != null)
                return false;
        } else if (!tv.equals(other.tv))
            return false;
        if (operationType == null) {
            if (other.operationType != null)
                return false;
        } else if (!operationType.equals(other.operationType))
            return false;
        return true;
    }
}
