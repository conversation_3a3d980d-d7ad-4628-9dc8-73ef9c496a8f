package clouesp.hes.tools.datagenerator.service;


import clouesp.hes.common.CommonUtils.StringUtils;
import clouesp.hes.common.DataEntity.Data.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.List;

@Slf4j
@Service("dataAccessService")
public class DataAccessService {

    @Resource(name = "persistenceJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    //@Autowired
    //private ServerConfig serverConfig;

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private boolean putInStorage(String sql, Object[][] params) {
        Connection con = null;
        PreparedStatement pst = null;

        try {
            con = jdbcTemplate.getDataSource().getConnection();
            pst = con.prepareStatement(sql);
            int col = 0;
            for (Object[] param : params) {
                col = 0;
                for (Object obj : param) {

                    pst.setObject(col + 1, obj);
                    col++ ;
                }
                pst.addBatch();
            }
            con.setAutoCommit(false);
            pst.executeBatch();
            con.commit();
        }
        catch (Exception e) {
            if(e.getMessage() != null
                    && !e.getMessage().startsWith("ORA-00001")
                    && !e.getMessage().startsWith("Duplicate entry")) {
                log.info(sql);
                log.error("DataCalc Error : " , e);
                e.printStackTrace();
            }
            return false;
        } finally {
            try {
                if (pst != null) {
                    pst.close();
                }
            } catch (Exception e) {

                return false;
            }
            try {
                if (con != null) {
                    con.close();
                }
            } catch (Exception e) {

                return false;
            }
        }

        return true;
    }

    public boolean batchUpdateCalcProgressSaveOracle(String tableName, List<DataCalcProgress> dataCalcProgresss) throws Exception {
        int rowCount = 	dataCalcProgresss.size();
        if(rowCount == 0) {
            return true;
        }
        int columnCount = 5;

        StringBuffer sql = new StringBuffer();
        StringBuffer notSql = new StringBuffer();
        StringBuffer notVal = new StringBuffer();

        sql.append("merge into ");
        sql.append(tableName + " T1 ");
        sql.append("using(select ? as sdp_id, ? as scheme_id, ? as load_type from ");
        sql.append("dual) T2 ");
        sql.append("on (T1.sdp_id = T2.sdp_id and T1.scheme_id = T2.scheme_id and T1.load_type = T2.load_type ) ");
        sql.append("when matched then ");
        sql.append("update set tv = ?, update_tv = ? ");

        notSql.append("when not matched then ");
        notSql.append("insert (sdp_id, scheme_id, load_type, tv, update_tv) ");
        notSql.append("values (?, ? , ?, ?, ?)");

        sql.append(notSql).append(notVal);

        int row = 0;
        int col = 0;
        Object[][] params = new Object[rowCount][columnCount * 2];
        for (DataCalcProgress dataCalcProgress : dataCalcProgresss) {
            col = 0;
            for (int r = 0; r < 2; r++ ) {
                params[row][col++] = dataCalcProgress.getSdpId();
                params[row][col++] = dataCalcProgress.getSchemeId();
                params[row][col++] = dataCalcProgress.getLoadType();
                params[row][col++] = Timestamp.valueOf(sdf.format(dataCalcProgress.getTv()));
                params[row][col++] = Timestamp.valueOf(sdf.format(dataCalcProgress.getUpdateTv()));
            }
            row++;
        }

        String strSql = sql.toString();
        return putInStorage(strSql, params);
    }

    public boolean batchUpdateCalcProgressSaveMysql(String tableName, List<DataCalcProgress> dataCalcProgresss) throws Exception {
        int rowCount = 	dataCalcProgresss.size();
        if(rowCount == 0) {
            return true;
        }
        int columnCount = 5;

        StringBuffer sql = new StringBuffer();
        StringBuffer notSql = new StringBuffer();
        StringBuffer notVal = new StringBuffer();

        sql.append("replace into ");
        sql.append(tableName);
        notSql.append(" (sdp_id, scheme_id, load_type, tv, update_tv) ");
        notSql.append("values (?, ? , ?, ?, ?)");

        sql.append(notSql).append(notVal);

        int row = 0;
        int col = 0;
        Object[][] params = new Object[rowCount][columnCount];
        for (DataCalcProgress dataCalcProgress : dataCalcProgresss) {
            col = 0;
            params[row][col++] = dataCalcProgress.getSdpId();
            params[row][col++] = dataCalcProgress.getSchemeId();
            params[row][col++] = dataCalcProgress.getLoadType();
            params[row][col++] = Timestamp.valueOf(sdf.format(dataCalcProgress.getTv()));
            params[row][col++] = Timestamp.valueOf(sdf.format(dataCalcProgress.getUpdateTv()));
            row++;
        }

        String strSql = sql.toString();
        return putInStorage(strSql, params);
    }

    public boolean batchUpdateCalcProgressSave(String tableName, List<DataCalcProgress> dataCalcProgresss) throws Exception {
        //if(serverConfig.isOracleDb()) {
        if(true) {
            return batchUpdateCalcProgressSaveOracle(tableName, dataCalcProgresss);
        } else {
            return batchUpdateCalcProgressSaveMysql(tableName, dataCalcProgresss);
        }
    }

    public boolean batchSave(String tableName, List<DataReg> dataRegs) throws Exception{

        int rowCount = 	dataRegs.size();
        if(rowCount == 0) {
            return true;
        }

        Class clsPK = DataRegPK.class;
        Field[] fieldPKs = clsPK.getDeclaredFields();

        Class cls = DataReg.class;
        Field[] fields = cls.getDeclaredFields();

        int columnCount = fieldPKs.length + fields.length - 2;

        StringBuffer sql = new StringBuffer();
        StringBuffer val = new StringBuffer();

        sql.append("insert into ");
        sql.append(tableName);
        sql.append("( ");

        val.append("values (");

        for (Field field : fieldPKs) {
            if(Modifier.isFinal(field.getModifiers())) {
                continue;
            }
            if(field.getDeclaredAnnotations().length > 0
                    && field.getDeclaredAnnotations()[0].toString().indexOf("Transient") >= 0)
            {
                continue;
            }
            String name = field.getName();
            name = StringUtils.humpToLine(name);


            sql.append(name);
            sql.append(", ");

            val.append("?");
            val.append(", ");
        }
        int index = 0;
        for (Field field : fields) {
            String name = field.getName();
            if("dataRegPK".equalsIgnoreCase(name)) {
                continue;
            }
            if(field.getDeclaredAnnotations().length > 0
            && field.getDeclaredAnnotations()[0].toString().indexOf("Transient") >= 0)
            {
                //忽略掉当前列
                columnCount--;
                continue;
            }
            if(!name.startsWith("R")) {
                name = StringUtils.humpToLine(name);
            } else {
                name = name.toLowerCase();
            }

            sql.append(name);
            val.append("?");

                sql.append(", ");
                val.append(", ");

        }
        //删除多余的逗号
        if(sql.toString().substring(sql.toString().length()-2).equals(", "))
        {
            sql.delete(sql.toString().length()-2,sql.toString().length()-1);
            val.delete(val.toString().length()-2,val.toString().length()-1);
        }

        sql.append(") ");
        val.append(") ");

        sql.append(val);

        int row = 0;
        int col = 0;
        Object[][] params = new Object[rowCount][columnCount];
        for (DataReg dataReg : dataRegs) {
            col = 0;
            for (Field field : fieldPKs) {
                if(Modifier.isFinal(field.getModifiers())) {
                    continue;
                }
                if(field.getDeclaredAnnotations().length > 0
                        && field.getDeclaredAnnotations()[0].toString().indexOf("Transient") >= 0)
                {
                    continue;
                }
                field.setAccessible(true);
                Object obj = field.get(dataReg.getDataRegPK());
                if(obj != null && "java.util.Date".equals(field.getType().getCanonicalName())){
                    obj = Timestamp.valueOf(sdf.format(obj));
                }

                params[row][col++] = obj;
            }
            for (Field field : fields) {
                String name = field.getName();
                if("dataRegPK".equalsIgnoreCase(name)) {
                    continue;
                }
                if(field.getDeclaredAnnotations().length > 0
                        && field.getDeclaredAnnotations()[0].toString().indexOf("Transient") >= 0)
                {
                    continue;
                }
                field.setAccessible(true);
                Object obj = field.get(dataReg);

                if(obj != null && "java.util.Date".equals(field.getType().getCanonicalName())){
                    obj = Timestamp.valueOf(sdf.format(obj));
                }

                params[row][col++] = obj;
            }
            row++ ;
        }
        String strSql = sql.toString();
        return putInStorage(strSql, params);
    }

    public boolean batchUpdateSaveOracle(String tableName, List<DataEnergy> dataEnergys) throws Exception{
        int rowCount = 	dataEnergys.size();
        if(rowCount == 0) {
            return true;
        }
        Class clsPK = DataEnergyPK.class;
        Field[] fieldPKs = clsPK.getDeclaredFields();

        Class cls = DataEnergy.class;
        Field[] fields = cls.getDeclaredFields();

        int columnCount = fieldPKs.length + fields.length - 2;

        StringBuffer sql = new StringBuffer();
        StringBuffer notSql = new StringBuffer();
        StringBuffer notVal = new StringBuffer();

        sql.append("merge into ");
        sql.append(tableName + " T1 ");

        sql.append("using(select ");
        int index = 0;
        for (Field field : fieldPKs) {
            if(Modifier.isFinal(field.getModifiers())) {
                continue;
            }
            String name = field.getName();
            name = StringUtils.humpToLine(name);

            sql.append("? as ");
            sql.append(name);
            if(index < fieldPKs.length - 2) {
                sql.append(", ");
            }
            index++;
        }
        sql.append(" from dual) T2 ");

        sql.append("on (");

        index = 0;
        for (Field field : fieldPKs) {
            if(Modifier.isFinal(field.getModifiers())) {
                continue;
            }
            String name = field.getName();
            name = StringUtils.humpToLine(name);

            sql.append("T1.");
            sql.append(name);
            sql.append(" = ");
            sql.append("T2.");
            sql.append(name);
            if(index < fieldPKs.length - 2) {
                sql.append(" and ");
            }

            index++;
        }
        sql.append(") ");
        sql.append("when matched then update set ");


        notSql.append("when not matched then ");
        notSql.append("insert (");
        for (Field field : fieldPKs) {
            if(Modifier.isFinal(field.getModifiers())) {
                continue;
            }
            String name = field.getName();
            name = StringUtils.humpToLine(name);

            notSql.append(name);
            notSql.append(", ");
        }
        index = 0;
        for (Field field : fields) {
            String name = field.getName();

            if("dataEnergyPK".equalsIgnoreCase(name)) {
                continue;
            }
            if(!name.startsWith("R")) {
                name = StringUtils.humpToLine(name);
            } else {
                name = name.toLowerCase();
            }

            sql.append(name);
            sql.append(" = ?");

            notSql.append(name);

            if(index < fields.length - 2) {
                sql.append(", ");
                notSql.append(", ");
            }
            index++;
        }
        sql.append(" ");
        notSql.append(") values (");

        for (Field field : fieldPKs) {
            if(Modifier.isFinal(field.getModifiers())) {
                continue;
            }
            notVal.append("?");
            notVal.append(", ");
        }
        index = 0;
        for (Field field : fields) {
            String name = field.getName();
            if("dataEnergyPK".equalsIgnoreCase(name)) {
                continue;
            }
            notVal.append("?");
            if(index < fields.length - 2) {
                notVal.append(", ");
            }
            else {
                notVal.append(")");
            }

            index++;
        }
        sql.append(notSql).append(notVal);

        int row = 0;
        int col = 0;
        Object[][] params = new Object[rowCount][columnCount * 2];
        for (DataEnergy dataEnergy : dataEnergys) {
            col = 0;
            for (int r = 0; r < 2; r++) {
                for (Field field : fieldPKs) {
                    if(Modifier.isFinal(field.getModifiers())) {
                        continue;
                    }
                    field.setAccessible(true);
                    Object obj = field.get(dataEnergy.getDataEnergyPK());
                    if(obj != null && "java.util.Date".equals(field.getType().getCanonicalName())){
                        obj = Timestamp.valueOf(sdf.format(obj));
                    }
                    params[row][col++] = obj;
                }

                for (Field field : fields) {
                    String name = field.getName();
                    if("dataEnergyPK".equalsIgnoreCase(name)) {
                        continue;
                    }
                    field.setAccessible(true);
                    Object obj = field.get(dataEnergy);
                    if(obj != null && "java.util.Date".equals(field.getType().getCanonicalName())){
                        obj = Timestamp.valueOf(sdf.format(obj));
                    }
                    params[row][col++] = obj;
                }
            }
            row++ ;
        }

        String strSql = sql.toString();
        return putInStorage(strSql, params);
    }

    public boolean batchUpdateSaveMysql(String tableName, List<DataEnergy> dataEnergys) throws Exception{
        int rowCount = 	dataEnergys.size();
        if(rowCount == 0) {
            return true;
        }
        Class clsPK = DataEnergyPK.class;
        Field[] fieldPKs = clsPK.getDeclaredFields();

        Class cls = DataEnergy.class;
        Field[] fields = cls.getDeclaredFields();

        int columnCount = fieldPKs.length + fields.length - 2;

        StringBuffer sql = new StringBuffer();

        sql.append("replace into ");
        sql.append(tableName);

        sql.append(" (");
        for (Field field : fieldPKs) {
            if(Modifier.isFinal(field.getModifiers())) {
                continue;
            }
            String name = field.getName();
            name = StringUtils.humpToLine(name);
            sql.append(name);
            sql.append(", ");
        }
        int index = 0;
        for (Field field : fields) {
            String name = field.getName();

            if("dataEnergyPK".equalsIgnoreCase(name)) {
                continue;
            }
            if(!name.startsWith("R")) {
                name = StringUtils.humpToLine(name);
            } else {
                name = name.toLowerCase();
            }
            sql.append(name);
            if(index < fields.length - 2) {
                sql.append(", ");

            }
            index++;
        }
        sql.append(") values (");

        for (Field field : fieldPKs) {
            if(Modifier.isFinal(field.getModifiers())) {
                continue;
            }
            sql.append("?");
            sql.append(", ");
        }
        index = 0;
        for (Field field : fields) {
            String name = field.getName();
            if("dataEnergyPK".equalsIgnoreCase(name)) {
                continue;
            }
            sql.append("?");
            if(index < fields.length - 2) {
                sql.append(", ");
            }
            else {
                sql.append(")");
            }

            index++;
        }

        int row = 0;
        int col = 0;
        Object[][] params = new Object[rowCount][columnCount];
        for (DataEnergy dataEnergy : dataEnergys) {
            col = 0;

            for (Field field : fieldPKs) {
                if (Modifier.isFinal(field.getModifiers())) {
                    continue;
                }
                field.setAccessible(true);
                Object obj = field.get(dataEnergy.getDataEnergyPK());
                if (obj != null && "java.util.Date".equals(field.getType().getCanonicalName())) {
                    obj = Timestamp.valueOf(sdf.format(obj));
                }
                params[row][col++] = obj;
            }

            for (Field field : fields) {
                String name = field.getName();
                if ("dataEnergyPK".equalsIgnoreCase(name)) {
                    continue;
                }
                field.setAccessible(true);
                Object obj = field.get(dataEnergy);
                if (obj != null && "java.util.Date".equals(field.getType().getCanonicalName())) {
                    obj = Timestamp.valueOf(sdf.format(obj));
                }
                params[row][col++] = obj;
            }

            row++;
        }

        String strSql = sql.toString();
        return putInStorage(strSql, params);
    }

    public boolean batchUpdateSave(String tableName, List<DataEnergy> dataEnergys) throws Exception{
        //if(serverConfig.isOracleDb()) {
        if(true) {
            return batchUpdateSaveOracle(tableName, dataEnergys);
        } else {
            return batchUpdateSaveMysql(tableName, dataEnergys);
        }
    }

}

