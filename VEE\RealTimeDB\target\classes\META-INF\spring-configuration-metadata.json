{"groups": [{"name": "spring.persistence.datasource", "type": "javax.sql.DataSource", "sourceType": "clouesp.hes.core.RealTimeDB.config.DataSourceConfig", "sourceMethod": "public javax.sql.DataSource persistenceDS() "}, {"name": "spring.realtime.datasource", "type": "javax.sql.DataSource", "sourceType": "clouesp.hes.core.RealTimeDB.config.DataSourceConfig", "sourceMethod": "public javax.sql.DataSource realtimeDS() "}], "properties": [], "hints": []}