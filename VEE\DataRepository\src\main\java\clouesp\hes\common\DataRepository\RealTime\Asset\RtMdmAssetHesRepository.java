package clouesp.hes.common.DataRepository.RealTime.Asset;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import clouesp.hes.common.DataEntity.Asset.MdmAssetHes;

/**
 * HES厂商信息表实时数据库访问接口
 */
@Repository
public interface RtMdmAssetHesRepository extends JpaRepository<MdmAssetHes, String>, JpaSpecificationExecutor<MdmAssetHes> {
    
    /**
     * 根据hesId查询HES厂商信息
     * 
     * @param hesId HES厂商ID
     * @return HES厂商信息
     */
    MdmAssetHes findByHesId(String hesId);
    
    /**
     * 根据厂商名称查询HES厂商信息
     * 
     * @param companyName 厂商名称
     * @return HES厂商信息
     */
    MdmAssetHes findByCompanyName(String companyName);
    
    /**
     * 根据用户名查询HES厂商信息
     * 
     * @param userName 用户名
     * @return HES厂商信息
     */
    MdmAssetHes findByUserName(String userName);
} 