<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring comment="Rename resource &apos;Rtdb/src/main/java/com/photon&apos; to &apos;clouesp&apos;" description="Rename resource &apos;photon&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/main/java/com/photon" name="clouesp" stamp="1590400920973" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;Rtdb/src/main/java/com/clouesp/core/servicertdb&apos; to &apos;Rtdb&apos;" description="Rename resource &apos;servicertdb&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/main/java/com/clouesp/core/servicertdb" name="Rtdb" stamp="1590400934808" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;Rtdb/src/test/java/com/photon&apos; to &apos;clouesp&apos;" description="Rename resource &apos;photon&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/java/com/photon" name="clouesp" stamp="1590474746682" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;Rtdb/src/test/java/com/clouesp/core/servicertdb&apos; to &apos;Rtdb&apos;" description="Rename resource &apos;servicertdb&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/java/com/clouesp/core/servicertdb" name="Rtdb" stamp="1590474763963" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;Rtdb/src/main/java/com&apos; to &apos;clouesp&apos;" description="Rename resource &apos;com&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/main/java/com" name="clouesp" stamp="1590478026057" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;Rtdb/src/main/java/clouesp/clouesp&apos; to &apos;hes&apos;" description="Rename resource &apos;clouesp&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/main/java/clouesp/clouesp" name="hes" stamp="1590478041057" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;Rtdb/src/test/java/com&apos; to &apos;clouesp&apos;" description="Rename resource &apos;com&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/java/com" name="clouesp" stamp="1590478065568" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;Rtdb/src/test/java/clouesp/clouesp&apos; to &apos;hes&apos;" description="Rename resource &apos;clouesp&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/java/clouesp/clouesp" name="hes" stamp="1590478084133" updateReferences="true"/>
</session>