package clouesp.hes.common.DataEntity.Asset;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.IdClass;
import javax.persistence.Table;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value="mdmAssetCalcObjMap",description="计算对象关联信息")
@Entity
@Table(name= "mdm_asset_calc_obj_map")
@IdClass(MdmAssetCalcObjMapPK.class)
public class MdmAssetCalcObjMap {
	@ApiModelProperty(value="计算对象ID", position=1)
	@Id
	@Column(name = "id", nullable = false, columnDefinition = "varchar(32)")
	private String id;
	@ApiModelProperty(value="1:供入 2:供出，当对象类型为总加对象时，默认为1-供入", position=2)
	@Id
	@Column(name = "type")
	private Integer type; 
	@ApiModelProperty(value="计量点ID", position=3)
	@Id
	@Column(name = "metering_id", columnDefinition = "varchar(32)")
	private String meteringId;
	@ApiModelProperty(value="1:计量点，2:计算对象", position=4)
	@Column(name = "metering_type")
	private Integer meteringType;
	@ApiModelProperty(value="计算公式，字典值1100，+正向有功等", position=5)
	@Id
	@Column(name = "calc_formula")
	private Integer calcFormula;
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	public String getMeteringId() {
		return meteringId;
	}
	public void setMeteringId(String meteringId) {
		this.meteringId = meteringId;
	}
	public Integer getMeteringType() {
		return meteringType;
	}
	public void setMeteringType(Integer meteringType) {
		this.meteringType = meteringType;
	}
	public Integer getCalcFormula() {
		return calcFormula;
	}
	public void setCalcFormula(Integer calcFormula) {
		this.calcFormula = calcFormula;
	}	
}
