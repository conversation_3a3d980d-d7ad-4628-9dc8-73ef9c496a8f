package clouesp.hes.core.DataCalc.service.Calculation;

import clouesp.hes.common.DataEntity.Asset.MdmAssetCalcObj;
import clouesp.hes.common.DataModel.api.ReqCalcMsg;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetCalcObjRepository;
import clouesp.hes.common.MqBus.MQMsg;
import clouesp.hes.common.MqBus.ServiceProducer;
import clouesp.hes.core.DataCalc.config.ServerConfig;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.LinkedBlockingQueue;

@Service("calculationService")
public class CalculationService {
	private LinkedBlockingQueue<ReqCalcMsg> reqCalcMsgQueue = new LinkedBlockingQueue<ReqCalcMsg>();
	private SendMsgProc sendMsgProc = null;
	private Thread sendMsgProcThread = null;
	
	@Autowired
	private ServiceProducer serviceProducer;
	private String producerGroup;
	private String producerTopic;
	private String producerTags;
	private DefaultMQProducer producer;
	@Autowired
	private ServerConfig serverConfig;
	
	@Autowired
	private RtMdmAssetCalcObjRepository rtMdmAssetCalcObjRepository;
	
	public void startService(String serviceId) {
		String prefix = "VEE";
		String producerInfo = "MANUCALCOBJ.CALCULATION.CALC";
		String[] infos = producerInfo.split("\\.");
		producerGroup = prefix + "_" + infos[0];
		producerTopic = prefix + "_" + infos[1];
		producerTags = prefix + "_" + infos[2];
		String namesrvAddr = serverConfig.getNamesrvAddr();
		producer = serviceProducer.start(namesrvAddr, producerGroup);		
		
		if (sendMsgProcThread == null) {
			sendMsgProc = new SendMsgProc();
			sendMsgProcThread = new Thread(sendMsgProc);
			sendMsgProcThread.start();				
		}
	}
	public void calculationObj(ReqCalcMsg request) {
		try {
			reqCalcMsgQueue.put(request);
		} catch (InterruptedException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	private class SendMsgProc implements Runnable {
		private int sendReqCalcMsg() {
			if (reqCalcMsgQueue.size() == 0)
				return 0;
			int count = reqCalcMsgQueue.size();
			List<ReqCalcMsg> datas = new ArrayList<ReqCalcMsg>();
			reqCalcMsgQueue.drainTo(datas, count);
			for (ReqCalcMsg data : datas) {
				for (String id : data.getIds()) {
					Optional<MdmAssetCalcObj> optionalCalcObj = rtMdmAssetCalcObjRepository.findById(id);
					if (!optionalCalcObj.isPresent()) {
						continue;
					}
					MdmAssetCalcObj calcObj = optionalCalcObj.get();
					calcObj.setCalcStartTv(data.getStartTv());
					calcObj.setCalcEndTv(data.getEndTv());
					calcObj.setSchemeIds(data.getSchemeId());
					calcObj.setManaual(true);
					MQMsg<MdmAssetCalcObj> mqMsg = new MQMsg<MdmAssetCalcObj>();
					mqMsg.setFromServiceId(serverConfig.getServiceId());
					mqMsg.setFromServiceType("CALC");
					mqMsg.setFromId(calcObj.getId());
					mqMsg.setFromType("CALCOBJ");
					mqMsg.setTopic(producerTopic);
					mqMsg.setTags(producerTags);
					mqMsg.setLoad(calcObj);
					serviceProducer.sendMsg(producer, mqMsg);					
					
				}
			}
			return count;
		}

		@Override
		public void run() {

			try {
				while (true) {
					int retCount = sendReqCalcMsg();
					if (retCount == 0) {
						Thread.sleep(1000);
					}
				}
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
	}
}
