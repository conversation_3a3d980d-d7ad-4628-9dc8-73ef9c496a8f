<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="DataScheduleProgressMapper">
    <select id="getDataScheduleProgressList" resultType="com.clou.common.DataEntity.Entity.Store.hes.Data.DataScheduleProgress"  parameterType="PagingRequest">
        select
        *
        from
        data_schedule_progress
        where
        1 = 1
        <if test="entity">
            <if test="entity.deviceId != null and entity.deviceId != ''">
                and device_id = #{entity.deviceId}
            </if>
            <if test="entity.profileId != null and entity.profileId != ''">
                and profile_id = #{entity.profileId}
            </if>
            <if test="entity.tv != null and entity.tv != ''">
                and tv = #{entity.tv}
            </if>

        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <update id="updateScheduleProgessTv"
            parameterType="com.clou.common.DataEntity.Entity.Store.hes.Data.DataScheduleProgress">
        update DATA_SCHEDULE_PROGRESS
        <set>

             TV = #{entity.tv}

        </set>
        where 1 = 1
        <if test="entity">
            <if test="entity.deviceId != null and entity.deviceId != ''">
                and device_id = #{entity.deviceId}
            </if>
            <if test="entity.profileId != null and entity.profileId != ''">
                and profile_id = #{entity.profileId}
            </if>


        </if>
    </update>

<!-- Miss Data Tracing ： Progress Delay Report   -->
    <select id="getDataScheduleProgressDelayList" resultType="com.clou.common.DataEntity.Model.Data.ProgressDelayResult"  parameterType="PagingRequest">
        select meter.id as meter_id , meter.sn as meter_sn ,  profile.id as profile_id , profile.name as profile_name  , commu.sn as commu_sn  ,
               factory.name as  factory_name ,  model.name as model_name , commutype.name as commu_type , progress.tv as progress_tv ,
               (case  when <![CDATA[ progress.tv < #{entity.deadLineTime}  ]]>  then 'Yes' else 'No' end ) as progress_delay ,
               progress.last_task_tv as last_task_tv , progress.task_state as task_state , progress.failed_info as task_failed_info  , statu.update_tv as statu_update_tv , statu.com_status as com_status , meter.create_time as  registration_time   from
        data_schedule_progress  progress INNER  join dict_profile profile on profile.id = progress.profile_id and profile.profile_type = 1
        <if test="entity">
            <if test="entity.profileId != null and entity.profileId != ''" >
                and  profile.id = #{entity.profileId}
            </if>

        </if>
        INNER  join asset_meter meter    on  meter.id = progress.device_id
        INNER  join asset_communicator commu  on  meter.communicator_id = commu.id
        INNER  join dict_manufacturer factory on factory.id = meter.manufacturer
        INNER  join dict_device_model model on meter.model = model.id
        INNER  join dict_communication_type commutype on meter.com_type = commutype.id
        left join data_comminication_status statu on commu.id = statu.communicator_id
        where
        1 = 1

        <if test="params.orgIdList != null and params.orgIdList.size > 0">
            and meter.org_id in
            <foreach collection="params.orgIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="entity">
            <if test="entity.meterSn != null and entity.meterSn != ''" >
                and meter.sn = #{entity.meterSn}
            </if>
            <if test="entity.communicatorSn != null and entity.communicatorSn != ''" >
                and commu.sn = #{entity.communicatorSn}
            </if>
            <if test="entity.progressDelay != null and entity.progressDelay != '' and entity.progressDelay == 'Yes' "  >
               and  <![CDATA[ progress.tv < #{entity.deadLineTime}  ]]>
            </if>
            <if test="entity.progressDelay != null and entity.progressDelay != '' and entity.progressDelay == 'No' "  >
                and  <![CDATA[ progress.tv >= #{entity.deadLineTime}  ]]>
            </if>
            <if test="entity.startRegistTime != null   " >
                and  <![CDATA[ meter.create_time >= #{entity.startRegistTime}  ]]>
            </if>
            <if test="entity.endRegistTime != null   " >
                and  <![CDATA[ meter.create_time <= #{entity.endRegistTime}  ]]>
            </if>

        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>
    </select>


<!--    <select id="getDataScheduleMissDataDetail" resultType="com.clou.common.DataEntity.Model.Data.DataScheduleMissResult"  parameterType="PagingRequest">-->


<!--        select  meter.sn as meterSn , miss.tv as tv  from data_schedule_miss_data miss-->
<!--        inner join  asset_meter meter on  meter.id = miss.device_id-->
<!--        <if test="entity">-->
<!--            <if test="entity.profileId != null and entity.profileId != ''" >-->
<!--                and  miss.profile_id = #{entity.profileId}-->
<!--            </if>-->

<!--            <if test="entity.meterSn != null and entity.meterSn != ''" >-->
<!--                and meter.sn like concat(concat('%', #{entity.meterSn}),'%')-->
<!--            </if>-->
<!--        </if>-->
<!--        inner join  dict_profile profile on miss.profile_id = profile.id-->


<!--        <if test="sortList != null and sortList.size > 0">-->
<!--            order by-->
<!--            <foreach collection="sortList" item="item" separator="," open=" " close=" ">-->
<!--                ${item.field} ${item.sort}-->
<!--            </foreach>-->
<!--        </if>-->
<!--    </select>-->


    <select id="getMeterProfileId" resultType="com.clou.common.DataEntity.Entity.Store.hes.Asset.AssetMeasurementProfile"  parameterType="com.clou.common.DataEntity.Model.Data.DataScheduleDelayDetailQuery">

        select profile.* from asset_meter_group_map map , asset_meter meter , asset_measurement_profile profile
        where profile.mg_id = map.group_id and profile.profile_id = #{entity.profileId} and  meter.id = map.id
          and  meter.sn  = #{entity.meterSn} and map.type = 1

    </select>


    <!-- Miss Data Tracing ： Missing Data Report    -->

    <select id="getDataMissDataList" resultType="com.clou.common.DataEntity.Model.Data.MissDataStatisResult"  parameterType="PagingRequest">
        select meter.id as meter_id , meter.sn as meter_sn , profile.id as profile_id , profile.name as profile_name , commu.sn as commu_sn , factory.name as factory_name , model.name as model_name , commutype.name as commu_type , progress.tv as progress_tv ,statis_result.miss_count as missData   from (
        select device_id , profile_id , count(device_id) as miss_count from data_schedule_miss_data where 1 = 1
        <if test="entity">
            <if test="entity.profileId != null and entity.profileId != ''" >
                and  profile_id = #{entity.profileId}
            </if>

            <if test="entity.startTime != null "  >
                and  <![CDATA[ tv >=  #{entity.startTime}   ]]>
            </if>

            <if test="entity.endTime != null  " >
                and  <![CDATA[ tv <=  #{entity.endTime}  ]]>
            </if>

        </if>  group by device_id , profile_id)
        statis_result  left join data_schedule_progress progress
        on statis_result.device_id = progress.device_id and statis_result.profile_id = progress.profile_id
        left  join dict_profile profile on profile.id = progress.profile_id and profile.profile_type = 1
        INNER join asset_meter meter on meter.id = progress.device_id
        INNER join asset_communicator commu on meter.communicator_id = commu.id
        INNER join dict_manufacturer factory on factory.id = meter.manufacturer
        INNER join dict_device_model model on meter.model = model.id
        INNER join dict_communication_type commutype on meter.com_type = commutype.id
        where
            1 = 1

        <if test="params.orgIdList != null and params.orgIdList.size > 0">
            and meter.org_id in
            <foreach collection="params.orgIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>


        <if test="entity">
            <if test="entity.meterSn != null and entity.meterSn != ''" >
                and meter.sn = #{entity.meterSn}
            </if>
            <if test="entity.communicatorSn != null and entity.communicatorSn != ''" >
                and commu.sn =  #{entity.communicatorSn}
            </if>

        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>
    </select>


    <select id="getDataMissDataDetail" resultType="com.clou.common.DataEntity.Model.Data.MissDataStatisDetailResult"  parameterType="PagingRequest">
        select meter.sn as meter_sn , missData.tv as tv , missData.task_tv as task_tv ,missData.task_state as task_state
        , missData.failed_info as faileuerCause , statu.update_tv as statu_update_tv , statu.com_status as com_status from
        data_schedule_miss_data missData inner join  asset_meter meter on  missData.device_id = meter.id
        <if test="entity">
            <if test="entity.meterSn != null and entity.meterSn != ''" >
                and meter.sn like concat(concat('%', #{entity.meterSn}),'%')
            </if>
            <if test="entity.profileId != null and entity.profileId != ''" >
                and missData.profile_id = #{entity.profileId}
            </if>

            <if test="entity.startTime != null "  >
                and  <![CDATA[ missData.tv >=  #{entity.startTime}   ]]>
            </if>

            <if test="entity.endTime != null  " >
                and  <![CDATA[ missData.tv <=  #{entity.endTime}  ]]>
            </if>

        </if>

        INNER  join asset_communicator commu  on  meter.communicator_id = commu.id
        left join data_comminication_status statu on commu.id = statu.communicator_id

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>
    </select>





</mapper>