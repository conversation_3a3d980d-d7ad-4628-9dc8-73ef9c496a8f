package clouesp.hes.common.DataEntity.Data;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;

public class DataStealEventPK implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 7633470473181504627L;

	@Column(name = "sdp_id", nullable = false, columnDefinition = "varchar(32)")
	private String sdpId;	
	
	@Column(name = "tv")
	private Date tv;
	
	@Column(name = "event_id", columnDefinition = "varchar(32)")
	private String eventId;
	
	public String getSdpId() {
		return sdpId;
	}

	public void setSdpId(String sdpId) {
		this.sdpId = sdpId;
	}

	public Date getTv() {
		return tv;
	}

	public void setTv(Date tv) {
		this.tv = tv;
	}

	public String getEventId() {
		return eventId;
	}

	public void setEventId(String eventId) {
		this.eventId = eventId;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((sdpId == null) ? 0 : sdpId.hashCode());
		result = prime * result
				+ ((tv == null) ? 0 : tv.hashCode());
		result = prime * result
				+ ((eventId == null) ? 0 : eventId.hashCode());		
		return result;
	}
	
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		DataStealEventPK other = (DataStealEventPK) obj;
		if (sdpId == null) {
			if (other.sdpId != null)
				return false;
		} else if (!sdpId.equals(other.sdpId))
			return false;
		if (tv == null) {
			if (other.tv != null)
				return false;
		} else if (!tv.equals(other.tv))
			return false;
		if (eventId == null) {
			if (other.eventId != null)
				return false;
		} else if (!eventId.equals(other.eventId))
			return false;		
		return true;
	}						
}
