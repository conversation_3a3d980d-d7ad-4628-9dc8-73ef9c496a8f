package clouesp.hes.common.DataRepository.Persistence.Data;

import clouesp.hes.common.DataEntity.Data.DataCalcObjMiss;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;

public interface DataCalcObjMissRepository extends JpaRepository<DataCalcObjMiss, DataCalcObjMiss> {
	@Modifying
	@Transactional
	@Query(value = "delete from mdm_data_calc_obj_miss "
			+ "where calc_obj_id = :calcObjId "
			+ "and tv = to_date(:tv, 'yyyy-mm-dd hh24:mi:ss') "
			+ "and scheme_id = :schemeId", 
			nativeQuery = true)
	void oracleClearMiss(
			@Param("calcObjId") String calcObjId,
			@Param("tv") String tv,
			@Param("schemeId") String schemeId);


	@Modifying
	@Transactional
	@Query(value = "delete from mdm_data_calc_obj_miss "
			+ "where calc_obj_id = :calcObjId "
			+ "and tv  = :tv  "
			+ "and scheme_id = :schemeId",
			nativeQuery = true)
	void mysqlClearMiss(
			@Param("calcObjId") String calcObjId,
			@Param("tv") String tv,
			@Param("schemeId") String schemeId);
}
