package clouesp.hes.common.DataRepository.Persistence.Asset;

import org.springframework.data.jpa.repository.JpaRepository;

import clouesp.hes.common.DataEntity.Asset.MdmAssetCalcObjMap;
import clouesp.hes.common.DataEntity.Asset.MdmAssetCalcObjMapPK;

import java.util.List;

public interface MdmAssetCalcObjMapRepository extends JpaRepository<MdmAssetCalcObjMap, MdmAssetCalcObjMapPK>{

    List<MdmAssetCalcObjMap> findAllById(String id);

    List<MdmAssetCalcObjMap> findAllByIdAndType(String id, Integer type);

}
