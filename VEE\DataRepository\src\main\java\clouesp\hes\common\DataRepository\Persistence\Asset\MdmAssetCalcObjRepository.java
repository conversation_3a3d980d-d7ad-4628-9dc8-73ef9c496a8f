package clouesp.hes.common.DataRepository.Persistence.Asset;

import clouesp.hes.common.DataEntity.Asset.MdmAssetCalcObjMap;
import org.springframework.data.jpa.repository.JpaRepository;

import clouesp.hes.common.DataEntity.Asset.MdmAssetCalcObj;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;

public interface MdmAssetCalcObjRepository extends JpaRepository<MdmAssetCalcObj, String>{

    List<MdmAssetCalcObj> findAllByVeeValidationGroupId(String name);

    @Query(value = "select * from mdm_asset_calc_obj where "
            + "id in (select metering_id from mdm_asset_calc_obj_map "
            + "where id = :calcObjId and metering_type = 2) "
            , nativeQuery = true)
    List<MdmAssetCalcObj> findSubObjs(
            @Param("calcObjId") String calcObjId);
}
