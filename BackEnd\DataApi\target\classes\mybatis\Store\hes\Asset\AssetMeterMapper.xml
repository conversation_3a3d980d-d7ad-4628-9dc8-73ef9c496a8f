<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="AssetMeterMapper">

    <select id="getMeterSnByMacAddrList" resultType="MeterMacSnMap" parameterType="Request">
        select mac , sn from asset_meter where
        mac in
        <foreach collection="entity.macList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>

    </select>


    <select id="getMeterSimpleInfoList" resultType="MeterSimpleInfo" parameterType="Request">
        select am.id , am.sn ,ac.device_type
        from asset_meter am , asset_communicator ac where am.COMMUNICATOR_ID = ac.id


        <if test="params.orgIdList != null and params.orgIdList.size > 0">
            and am.org_id in
            <foreach collection="params.orgIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>


        <if test="entity">
            <if test="entity.id != null and entity.id != ''">
                and am.id = #{entity.id}
            </if>

            <if test="entity.sn != null and entity.sn != ''">
                and am.sn like concat(concat('%', #{entity.sn}),'%')
            </if>
            <if test="entity.listDeviceType != null and entity.listDeviceType.size > 0">
                and ac.device_type in
                <foreach collection="entity.listDeviceType" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="entity.isOnlyQueryNoCustomerMeter != null and entity.isOnlyQueryNoCustomerMeter  ">
                and am.id not in (select meter_id from mdm_asset_service_point where
                customer_id is not null and meter_id is not null)
            </if>
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <select id="getMeterListEx" resultType="AssetMeter" parameterType="PagingRequest">
        select ddm.NAME as modelName,dct.NAME as comTypeName,ac.SN as communicatorSn,am.ID,am.SN,am.NAME from
        asset_meter am,dict_device_model ddm,dict_communication_type dct,asset_communicator ac where am.MODEL = ddm.ID
        AND am.COM_TYPE = dct.ID and ac.ID = am.COMMUNICATOR_ID

        <if test="entity">
            <if test="entity.communicatorId != null and entity.communicatorId != ''">
                and am.COMMUNICATOR_ID = #{entity.communicatorId}
            </if>
            <if test="entity.communicatorSn != null and entity.communicatorSn != ''">
                and ac.SN = #{entity.communicatorSn}
            </if>
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <select id="getMeterList_COUNT_OLD" resultType="java.lang.Long" parameterType="PagingRequest">
        select
        count(*)
        from
        asset_meter am
        left join asset_communicator ac on am.communicator_id = ac.id
        left join asset_favorite af
        on am.id = af.id and af.id_type = 1 and af.user_id = #{params.userId}

        left join data_comminication_status dcs
        <if test="params.onlineMode == 'Communicator'">
            on am.communicator_id = dcs.communicator_id
        </if>
        <if test="params.onlineMode == 'Meter'">
            on am.id = dcs.communicator_id
        </if>


        where
        1 = 1
        <if test="params.isInactiveVisible == 'DisVisible'">
            and am.meter_status > 1
        </if>
        <if test="params.orgIdList != null and params.orgIdList.size > 0">
            and am.org_id in
            <foreach collection="params.orgIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="params.queryOrgIdList != null and params.queryOrgIdList.size > 0">
            and am.org_id in
            <foreach collection="params.queryOrgIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>


        <if test="entity">
            <if test="entity.id != null and entity.id != ''">
                and am.id = #{entity.id}
            </if>

            <if test="entity.excludeCommunicatorId != null and entity.excludeCommunicatorId != ''">
                and am.communicator_id != #{entity.excludeCommunicatorId}
            </if>

            <if test="entity.sn != null and entity.sn != ''">
                and am.sn like concat(concat('%', #{entity.sn}),'%')
            </if>

            <if test="entity.mac != null and entity.mac != ''">
                and am.mac like concat(concat('%', #{entity.mac}),'%')
            </if>

            <if test="entity.name != null and entity.name != ''">
                and am.name like concat(concat('%', #{entity.name}),'%')
            </if>
            <if test="entity.communicatorId != null and entity.communicatorId != ''">
                and am.communicator_id = #{entity.communicatorId}
            </if>

            <if test="entity.communicatorSn != null and entity.communicatorSn != ''">
                and ac.sn like concat(concat('%', #{entity.communicatorSn}),'%')
            </if>

            <if test="entity.meterStatus != null">
                and am.meter_status = #{entity.meterStatus}
            </if>


            <if test="entity.fwVersion != null and entity.fwVersion != ''">
                and am.fw_version like concat(concat('%', #{entity.fwVersion}),'%')
            </if>

            <if test="entity.fwVersionList != null and entity.fwVersionList.size > 0">
                and am.fw_version in
                <foreach collection="entity.fwVersionList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="entity.keyFlag != null">
                and am.key_flag = #{entity.keyFlag}
            </if>

            <if test="entity.deviceTypeList != null and entity.deviceTypeList.size > 0">
                and ac.device_type in
                <foreach collection="entity.deviceTypeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="entity.manufacturerList != null and entity.manufacturerList.size > 0">
                and am.manufacturer in
                <foreach collection="entity.manufacturerList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="entity.comTypeList != null and entity.comTypeList.size > 0">
                and am.com_type in
                <foreach collection="entity.comTypeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="entity.modelList != null and entity.modelList.size > 0">
                and am.model in
                <foreach collection="entity.modelList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="entity.measurementGroupList != null and entity.measurementGroupList.size > 0">
                and am.id in ( select id from asset_meter_group_map where type = 1 and group_id in
                <foreach collection="entity.measurementGroupList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>)
            </if>

            <if test="entity.touGroupList != null and entity.touGroupList.size > 0">
                and am.id in ( select id from asset_meter_group_map where type = 2 and group_id in
                <foreach collection="entity.touGroupList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>)
            </if>

            <if test="entity.limiterGroupList != null and entity.limiterGroupList.size > 0">
                and am.id in ( select id from asset_meter_group_map where type = 3 and group_id in
                <foreach collection="entity.limiterGroupList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>)
            </if>

            <if test="entity.stepTariffGroupList != null and entity.stepTariffGroupList.size > 0">
                and am.id in ( select id from asset_meter_group_map where type = 4 and group_id in
                <foreach collection="entity.stepTariffGroupList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>)
            </if>

            <if test="entity.scheduleSchemeList != null and entity.scheduleSchemeList.size > 0">
                and am.id in ( select id from asset_meter_group_map where type = 0 and group_id in
                <foreach collection="entity.scheduleSchemeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>)
            </if>

            <if test="entity.favorite != null">

                <choose>
                    <when test="entity.favorite == 1">
                        and af.id is not null
                    </when>
                    <otherwise>
                        and af.id is null
                    </otherwise>
                </choose>

            </if>

            <if test="entity.onlineState != null">

                <choose>
                    <when test="entity.onlineState == -1">
                        and dcs.communicator_id is null
                    </when>
                    <when test="entity.onlineState == 1">
                        and dcs.com_status = 1
                    </when>
                    <otherwise>
                        and dcs.com_status = 0
                    </otherwise>
                </choose>

            </if>

        </if>


    </select>

    <select id="getMeterList_COUNT" resultType="java.lang.Long" parameterType="PagingRequest">
        select
        count(*)
        from
        asset_meter am
        where
        1 = 1
        <if test="entity.onlineState != null">
            and EXISTS (select 1 from data_comminication_status dcs where 1=1
            <if test="params.onlineMode == 'Communicator'">
                and am.communicator_id = dcs.communicator_id
            </if>
            <if test="params.onlineMode == 'Meter'">
                and am.id = dcs.communicator_id
            </if>
            <choose>
                <when test="entity.onlineState == -1">
                    and dcs.communicator_id is null
                </when>
                <when test="entity.onlineState == 1">
                    and dcs.com_status = 1
                </when>
                <otherwise>
                    and dcs.com_status = 0
                </otherwise>
            </choose>
            )


        </if>

        <if test="entity.favorite != null">

            and EXISTS (select 1 from asset_favorite af where 1=1
            and am.id = af.id and af.id_type = 1 and af.user_id = #{params.userId}

            <choose>
                <when test="entity.favorite == 1">
                    and af.id is not null
                </when>
                <otherwise>
                    and af.id is null
                </otherwise>
            </choose>
            )
        </if>

        <if test="(entity.communicatorSn != null and entity.communicatorSn != '')
                 or (entity.deviceTypeList != null and entity.deviceTypeList.size > 0)">
            and EXISTS (select 1 from asset_communicator ac where am.communicator_id = ac.id
            <if test="(entity.communicatorSn != null and entity.communicatorSn != '') ">
                and ac.sn like concat(concat('%', #{entity.communicatorSn}),'%')
            </if>
            <if test="entity.deviceTypeList != null and entity.deviceTypeList.size > 0">
                and ac.device_type in
                <foreach collection="entity.deviceTypeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            )

        </if>

        <if test="params.isInactiveVisible == 'DisVisible'">
            and am.meter_status > 1
        </if>
        <if test="params.orgIdList != null and params.orgIdList.size > 0">
            and am.org_id in
            <foreach collection="params.orgIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="params.queryOrgIdList != null and params.queryOrgIdList.size > 0">
            and am.org_id in
            <foreach collection="params.queryOrgIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>


        <if test="entity">
            <if test="entity.id != null and entity.id != ''">
                and am.id = #{entity.id}
            </if>

            <if test="entity.excludeCommunicatorId != null and entity.excludeCommunicatorId != ''">
                and am.communicator_id != #{entity.excludeCommunicatorId}
            </if>

            <if test="entity.sn != null and entity.sn != ''">
                and am.sn like concat(concat('%', #{entity.sn}),'%')
            </if>

            <if test="entity.mac != null and entity.mac != ''">
                and am.mac like concat(concat('%', #{entity.mac}),'%')
            </if>

            <if test="entity.name != null and entity.name != ''">
                and am.name like concat(concat('%', #{entity.name}),'%')
            </if>
            <if test="entity.communicatorId != null and entity.communicatorId != ''">
                and am.communicator_id = #{entity.communicatorId}
            </if>


            <if test="entity.meterStatus != null">
                and am.meter_status = #{entity.meterStatus}
            </if>


            <if test="entity.fwVersion != null and entity.fwVersion != ''">
                and am.fw_version like concat(concat('%', #{entity.fwVersion}),'%')
            </if>

            <if test="entity.fwVersionList != null and entity.fwVersionList.size > 0">
                and am.fw_version in
                <foreach collection="entity.fwVersionList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="entity.keyFlag != null">
                and am.key_flag = #{entity.keyFlag}
            </if>


            <if test="entity.manufacturerList != null and entity.manufacturerList.size > 0">
                and am.manufacturer in
                <foreach collection="entity.manufacturerList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="entity.comTypeList != null and entity.comTypeList.size > 0">
                and am.com_type in
                <foreach collection="entity.comTypeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="entity.modelList != null and entity.modelList.size > 0">
                and am.model in
                <foreach collection="entity.modelList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="entity.measurementGroupList != null and entity.measurementGroupList.size > 0">
                and am.id in ( select id from asset_meter_group_map where type = 1 and group_id in
                <foreach collection="entity.measurementGroupList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>)
            </if>

            <if test="entity.touGroupList != null and entity.touGroupList.size > 0">
                and am.id in ( select id from asset_meter_group_map where type = 2 and group_id in
                <foreach collection="entity.touGroupList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>)
            </if>

            <if test="entity.limiterGroupList != null and entity.limiterGroupList.size > 0">
                and am.id in ( select id from asset_meter_group_map where type = 3 and group_id in
                <foreach collection="entity.limiterGroupList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>)
            </if>

            <if test="entity.stepTariffGroupList != null and entity.stepTariffGroupList.size > 0">
                and am.id in ( select id from asset_meter_group_map where type = 4 and group_id in
                <foreach collection="entity.stepTariffGroupList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>)
            </if>

            <if test="entity.scheduleSchemeList != null and entity.scheduleSchemeList.size > 0">
                and am.id in ( select id from asset_meter_group_map where type = 0 and group_id in
                <foreach collection="entity.scheduleSchemeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>)
            </if>


        </if>


    </select>

    <select id="getMeterList" resultType="AssetMeter" parameterType="PagingRequest">
        select
        #{params.isInactiveVisible} as isInactiveVisible ,
        am.*,
        dm.name as manufacturerName,
        ddm.name as modelName,
        ddm.protocol_id as protocolId,
        dct.name as comTypeName,
        so.name as orgName,
        so.org_code as orgCode,
        ac.sn as communicatorSn,
        ddt.id as deviceType,
        ddt.name as deviceTypeName ,
        ac.sim_num as simNum ,
        ac.listen_mode as listenMode ,
        ac.network_ip as networkIp ,
        ac.network_port as networkPort ,
        <if test="(entity.customerId != null and entity.customerId != '') or
         (entity.sdpId != null and entity.sdpId != '')">
            maspmdd.sn as sdpSn,
            maspmdd.name as sdpName,
            maspmdd.ct as sdpCt,
            maspmdd.pt as sdpPt,
            maspmdd.sdpCt as sdpCtName,
            maspmdd.sdpPt as sdpPtName,
        </if>

        <if test="entity == null or entity.queryMode != 1">
            mag.group_id as measurementGroupId,
            mag.name as measurementGroupName,

            tag.group_id as touGroupId,
            tag.name as touGroupName,

            liag.group_id as limiterGroupId,
            liag.name as limiterGroupName,

            sag.group_id as stepTariffGroupId,
            sag.name as stepTariffGroupName,

            oag.group_id as otherGroupId,
            oag.name as otherGroupName,

            ag.group_id as scheduleSchemeId,
            ag.name as scheduleSchemeName,

        </if>
        case when af.id is not null
        then 1
        else
        0
        end as
        favorite,

        dcs.ip_addr as ipAddr,
        dcs.ip_port as ipPort,
        dcs.update_tv as statusUpdateTime,

        case when dcs.communicator_id is null
        then -1
        when dcs.com_status = 1
        then 1
        else
        0
        end as onlineState

        from
        asset_meter am
        left join asset_communicator ac on am.communicator_id = ac.id
        <if test="(entity.customerId != null and entity.customerId != '') or
         (entity.sdpId != null and entity.sdpId != '')">
            left join asset_customer actm on am.id = actm.meter_id
            left join
            (select masp.*, mddct.gui_display_name as sdpCt, mddpt.gui_display_name as sdpPt from
            mdm_asset_service_point masp
            left join mdm_dict_detail mddct on mddct.dict_id = '1101' and masp.ct = mddct.inner_value
            left join mdm_dict_detail mddpt on mddpt.dict_id = '1102' and masp.pt = mddpt.inner_value
            ) maspmdd
            on am.id = maspmdd.meter_id
        </if>
        left join asset_favorite af
        on am.id = af.id and af.id_type = 1 and af.user_id = #{params.userId}

        left join dict_manufacturer dm on am.manufacturer = dm.id

        left join dict_device_model ddm on am.model = ddm.id

        left join dict_communication_type dct on am.com_type = dct.id

        left join sys_org so on am.org_id = so.id

        left join dict_device_type ddt on ac.device_type = ddt.id

        left join data_comminication_status dcs
        <if test="params.onlineMode == 'Communicator'">
            on am.communicator_id = dcs.communicator_id
        </if>
        <if test="params.onlineMode == 'Meter'">
            on am.id = dcs.communicator_id
        </if>

        <if test="entity == null or entity.queryMode != 1">
            left join (select amgm.id, amg.name, amgm.group_id from asset_meter_group_map amgm,
            asset_meter_group amg where amgm.group_id = amg.id and amgm.type = 1) mag on am.id = mag.id
            left join (select amgm.id, amg.name, amgm.group_id from asset_meter_group_map amgm,
            asset_meter_group amg where amgm.group_id = amg.id and amgm.type = 2) tag on am.id = tag.id
            left join (select amgm.id, amg.name, amgm.group_id from asset_meter_group_map amgm,
            asset_meter_group amg where amgm.group_id = amg.id and amgm.type = 3) liag on am.id = liag.id
            left join (select amgm.id, amg.name, amgm.group_id from asset_meter_group_map amgm,
            asset_meter_group amg where amgm.group_id = amg.id and amgm.type = 4) sag on am.id = sag.id
            left join (select amgm.id, amg.name, amgm.group_id from asset_meter_group_map amgm,
            asset_meter_group amg where amgm.group_id = amg.id and amgm.type = 5) oag on am.id = oag.id
            left join (select amgm.id, ass.name, amgm.group_id from asset_schedule_scheme ass,
            asset_meter_group_map amgm where ass.id = amgm.group_id and amgm.type = 0) ag on am.id = ag.id
        </if>
        where
        1 = 1
        <if test="params.isInactiveVisible == 'DisVisible'">
            and am.meter_status > 1
        </if>
        <if test="params.orgIdList != null and params.orgIdList.size > 0">
            and am.org_id in
            <foreach collection="params.orgIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="params.queryOrgIdList != null and params.queryOrgIdList.size > 0">
            and am.org_id in
            <foreach collection="params.queryOrgIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>


        <if test="entity">
            <if test="entity.id != null and entity.id != ''">
                and am.id = #{entity.id}
            </if>

            <if test="entity.excludeCommunicatorId != null and entity.excludeCommunicatorId != ''">
                and am.communicator_id != #{entity.excludeCommunicatorId}
            </if>

            <if test="entity.sn != null and entity.sn != ''">
                and am.sn like concat(concat('%', #{entity.sn}),'%')
            </if>

            <if test="entity.mac != null and entity.mac != ''">
                and am.mac like concat(concat('%', #{entity.mac}),'%')
            </if>

            <if test="entity.name != null and entity.name != ''">
                and am.name like concat(concat('%', #{entity.name}),'%')
            </if>
            <if test="entity.communicatorId != null and entity.communicatorId != ''">
                and am.communicator_id = #{entity.communicatorId}
            </if>

            <if test="entity.communicatorSn != null and entity.communicatorSn != ''">
                and ac.sn like concat(concat('%', #{entity.communicatorSn}),'%')
            </if>

            <if test="entity.meterStatus != null">
                and am.meter_status = #{entity.meterStatus}
            </if>

            <if test="entity.fwVersion != null and entity.fwVersion != ''">
                and am.fw_version like concat(concat('%', #{entity.fwVersion}),'%')
            </if>

            <if test="entity.customerId != null and entity.customerId != ''">
                and maspmdd.customer_id = #{entity.customerId}
            </if>
            <if test="entity.sdpId != null and entity.sdpId != ''">
                and maspmdd.id = #{entity.sdpId}
            </if>

            <if test="entity.fwVersionList != null and entity.fwVersionList.size > 0">
                and am.fw_version in
                <foreach collection="entity.fwVersionList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="entity.keyFlag != null">
                and am.key_flag = #{entity.keyFlag}
            </if>

            <if test="entity.deviceTypeList != null and entity.deviceTypeList.size > 0">
                and ac.device_type in
                <foreach collection="entity.deviceTypeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="entity.manufacturerList != null and entity.manufacturerList.size > 0">
                and am.manufacturer in
                <foreach collection="entity.manufacturerList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="entity.comTypeList != null and entity.comTypeList.size > 0">
                and am.com_type in
                <foreach collection="entity.comTypeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="entity.modelList != null and entity.modelList.size > 0">
                and am.model in
                <foreach collection="entity.modelList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="entity.measurementGroupList != null and entity.measurementGroupList.size > 0">
                and am.id in ( select id from asset_meter_group_map where type = 1 and group_id in
                <foreach collection="entity.measurementGroupList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>)
            </if>

            <if test="entity.touGroupList != null and entity.touGroupList.size > 0">
                and am.id in ( select id from asset_meter_group_map where type = 2 and group_id in
                <foreach collection="entity.touGroupList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>)
            </if>

            <if test="entity.limiterGroupList != null and entity.limiterGroupList.size > 0">
                and am.id in ( select id from asset_meter_group_map where type = 3 and group_id in
                <foreach collection="entity.limiterGroupList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>)
            </if>

            <if test="entity.stepTariffGroupList != null and entity.stepTariffGroupList.size > 0">
                and am.id in ( select id from asset_meter_group_map where type = 4 and group_id in
                <foreach collection="entity.stepTariffGroupList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>)
            </if>

            <if test="entity.scheduleSchemeList != null and entity.scheduleSchemeList.size > 0">
                and am.id in ( select id from asset_meter_group_map where type = 0 and group_id in
                <foreach collection="entity.scheduleSchemeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>)
            </if>

            <if test="entity.favorite != null">

                <choose>
                    <when test="entity.favorite == 1">
                        and af.id is not null
                    </when>
                    <otherwise>
                        and af.id is null
                    </otherwise>
                </choose>

            </if>

            <if test="entity.onlineState != null">

                <choose>
                    <when test="entity.onlineState == -1">
                        and dcs.communicator_id is null
                    </when>
                    <when test="entity.onlineState == 1">
                        and dcs.com_status = 1
                    </when>
                    <otherwise>
                        and dcs.com_status = 0
                    </otherwise>
                </choose>

            </if>

        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>


    </select>


    <select id="getAssetMeterPrepareList" resultType="AssetMeterPrepare" parameterType="Request">
        select am.* ,
        ddm.name as modelName,
        dct.name as comTypeName
        from ASSET_METER_PREPARE am
        left join dict_device_model ddm on am.model = ddm.id
        left join dict_communication_type dct on am.com_type = dct.id
        where
        1 = 1

        <if test="params.orgIdList != null and params.orgIdList.size > 0">
            and am.org_id in
            <foreach collection="params.orgIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>


        <if test="entity">
            <if test="entity.meterId != null and entity.meterId != ''">
                and am.id = #{entity.meterId}
            </if>

            <if test="entity.meterSn != null and entity.meterSn != ''">
                and am.sn like concat(concat('%', #{entity.meterSn}),'%')
            </if>
            <if test="entity.deviceTypeList != null and entity.deviceTypeList.size > 0">
                and dct.id in
                <foreach collection="entity.deviceTypeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>


    <select id="getMeterCustomerList" resultType="AssetMeterCustomer" parameterType="PagingRequest">
        select
        #{params.isInactiveVisible} as isInactiveVisible ,
        am.*,
        dm.name as manufacturerName,
        ddm.name as modelName,
        ddm.protocol_id as protocolId,
        dct.name as comTypeName,
        so.name as orgName,
        so.org_code as orgCode,
        ac.sn as communicatorSn,
        ddt.id as deviceType,
        ddt.name as deviceTypeName ,
        ac.sim_num as simNum ,
        ac.listen_mode as listenMode ,
        ac.network_ip as networkIp ,
        ac.network_port as networkPort ,

        maspmdd.sn as sdpSn,
        maspmdd.name as sdpName,
        maspmdd.ct as sdpCt,
        maspmdd.pt as sdpPt,
        maspmdd.sdpCt as sdpCtName,
        maspmdd.sdpPt as sdpPtName,

        actm.sn as customeSn,
        actm.name as customerName,
        actm.telephone_num as telephoneNum,
        actm.addr as customerAddr,
        actm.longituge as customerLongituge,
        actm.latitude as customerLatitude,
        actm.email as email,
        actm.create_date as createDate,
        actm.geo_code as geoCode,
        actm.zip_code as zipCode,
        actm.zip_code as zipCode,
        dctc.name as customerTypeName,
        dcic.name as industryTypeName,


        <if test="entity == null or entity.queryMode != 1">
            mag.group_id as measurementGroupId,
            mag.name as measurementGroupName,

            tag.group_id as touGroupId,
            tag.name as touGroupName,

            liag.group_id as limiterGroupId,
            liag.name as limiterGroupName,

            sag.group_id as stepTariffGroupId,
            sag.name as stepTariffGroupName,

            oag.group_id as otherGroupId,
            oag.name as otherGroupName,

            ag.group_id as scheduleSchemeId,
            ag.name as scheduleSchemeName,

        </if>


        dcs.ip_addr as ipAddr,
        dcs.ip_port as ipPort,
        dcs.update_tv as statusUpdateTime,

        case when dcs.communicator_id is null
        then -1
        when dcs.com_status = 1
        then 1
        else
        0
        end as onlineState

        from
        asset_meter am
        left join asset_communicator ac on am.communicator_id = ac.id

        left join (
        select maspmeter.meter_id as mid, acmeter.*
        from asset_customer acmeter,  mdm_asset_service_point maspmeter
        where acmeter.id = maspmeter.customer_id ) actm on actm.mid = am.id

        left join dict_customer_type dctc on actm.customer_type = dctc.id
        left join dict_customer_industry dcic on actm.industry_type = dcic.id

        left join
        (select masp.*, mddct.gui_display_name as sdpCt, mddpt.gui_display_name as sdpPt from mdm_asset_service_point
        masp
        left join mdm_dict_detail mddct on mddct.dict_id = '1101' and masp.ct = mddct.inner_value
        left join mdm_dict_detail mddpt on mddpt.dict_id = '1102' and masp.pt = mddpt.inner_value
        ) maspmdd
        on am.id = maspmdd.meter_id


        left join dict_manufacturer dm on am.manufacturer = dm.id

        left join dict_device_model ddm on am.model = ddm.id

        left join dict_communication_type dct on am.com_type = dct.id

        left join sys_org so on am.org_id = so.id

        left join dict_device_type ddt on ac.device_type = ddt.id

        left join data_comminication_status dcs
        <if test="params.onlineMode == 'Communicator'">
            on am.communicator_id = dcs.communicator_id
        </if>
        <if test="params.onlineMode == 'Meter'">
            on am.id = dcs.communicator_id
        </if>

        <if test="entity == null or entity.queryMode != 1">
            left join (select amgm.id, amg.name, amgm.group_id from asset_meter_group_map amgm,
            asset_meter_group amg where amgm.group_id = amg.id and amgm.type = 1) mag on am.id = mag.id
            left join (select amgm.id, amg.name, amgm.group_id from asset_meter_group_map amgm,
            asset_meter_group amg where amgm.group_id = amg.id and amgm.type = 2) tag on am.id = tag.id
            left join (select amgm.id, amg.name, amgm.group_id from asset_meter_group_map amgm,
            asset_meter_group amg where amgm.group_id = amg.id and amgm.type = 3) liag on am.id = liag.id
            left join (select amgm.id, amg.name, amgm.group_id from asset_meter_group_map amgm,
            asset_meter_group amg where amgm.group_id = amg.id and amgm.type = 4) sag on am.id = sag.id
            left join (select amgm.id, amg.name, amgm.group_id from asset_meter_group_map amgm,
            asset_meter_group amg where amgm.group_id = amg.id and amgm.type = 5) oag on am.id = oag.id
            left join (select amgm.id, ass.name, amgm.group_id from asset_schedule_scheme ass,
            asset_meter_group_map amgm where ass.id = amgm.group_id and amgm.type = 0) ag on am.id = ag.id
        </if>
        where
        1 = 1
        <if test="params.isInactiveVisible == 'DisVisible'">
            and am.meter_status > 1
        </if>
        <if test="params.orgIdList != null and params.orgIdList.size > 0">
            and am.org_id in
            <foreach collection="params.orgIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>


        <if test="params.queryOrgIdList != null and params.queryOrgIdList.size > 0">
            and am.org_id in
            <foreach collection="params.queryOrgIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>


        <if test="entity">
            <if test="entity.id != null and entity.id != ''">
                and am.id = #{entity.id}
            </if>

            <if test="entity.excludeCommunicatorId != null and entity.excludeCommunicatorId != ''">
                and am.communicator_id != #{entity.excludeCommunicatorId}
            </if>

            <if test="entity.mac != null and entity.mac != ''">
                and am.mac like concat(concat('%', #{entity.mac}),'%')
            </if>

            <if test="entity.name != null and entity.name != ''">
                and am.name like concat(concat('%', #{entity.name}),'%')
            </if>
            <if test="entity.communicatorId != null and entity.communicatorId != ''">
                and am.communicator_id = #{entity.communicatorId}
            </if>

            <if test="entity.communicatorSn != null and entity.communicatorSn != ''">
                and ac.sn like concat(concat('%', #{entity.communicatorSn}),'%')
            </if>

            <if test="entity.meterStatus != null">
                and am.meter_status = #{entity.meterStatus}
            </if>

            <if test="entity.fwVersion != null and entity.fwVersion != ''">
                and am.fw_version like concat(concat('%', #{entity.fwVersion}),'%')
            </if>

            <if test="entity.customerId != null and entity.customerId != ''">
                and maspmdd.customer_id = #{entity.customerId}
            </if>
            <if test="entity.sdpId != null and entity.sdpId != ''">
                and maspmdd.id = #{entity.sdpId}
            </if>

            <if test="entity.fwVersionList != null and entity.fwVersionList.size > 0">
                and am.fw_version in
                <foreach collection="entity.fwVersionList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="entity.keyFlag != null">
                and am.key_flag = #{entity.keyFlag}
            </if>

            <if test="entity.deviceTypeList != null and entity.deviceTypeList.size > 0">
                and ac.device_type in
                <foreach collection="entity.deviceTypeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>


            <if test="entity.comTypeList != null and entity.comTypeList.size > 0">
                and am.com_type in
                <foreach collection="entity.comTypeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="entity.measurementGroupList != null and entity.measurementGroupList.size > 0">
                and am.id in ( select id from asset_meter_group_map where type = 1 and group_id in
                <foreach collection="entity.measurementGroupList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>)
            </if>

            <if test="entity.touGroupList != null and entity.touGroupList.size > 0">
                and am.id in ( select id from asset_meter_group_map where type = 2 and group_id in
                <foreach collection="entity.touGroupList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>)
            </if>

            <if test="entity.limiterGroupList != null and entity.limiterGroupList.size > 0">
                and am.id in ( select id from asset_meter_group_map where type = 3 and group_id in
                <foreach collection="entity.limiterGroupList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>)
            </if>

            <if test="entity.stepTariffGroupList != null and entity.stepTariffGroupList.size > 0">
                and am.id in ( select id from asset_meter_group_map where type = 4 and group_id in
                <foreach collection="entity.stepTariffGroupList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>)
            </if>

            <if test="entity.scheduleSchemeList != null and entity.scheduleSchemeList.size > 0">
                and am.id in ( select id from asset_meter_group_map where type = 0 and group_id in
                <foreach collection="entity.scheduleSchemeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>)
            </if>

            <if test="entity.favorite != null">

                <choose>
                    <when test="entity.favorite == 1">
                        and af.id is not null
                    </when>
                    <otherwise>
                        and af.id is null
                    </otherwise>
                </choose>

            </if>

            <if test="entity.onlineState != null">

                <choose>
                    <when test="entity.onlineState == -1">
                        and dcs.communicator_id is null
                    </when>
                    <when test="entity.onlineState == 1">
                        and dcs.com_status = 1
                    </when>
                    <otherwise>
                        and dcs.com_status = 0
                    </otherwise>
                </choose>

            </if>

            <if test="entity.deviceType == 1">

                <if test="entity.sn != null and entity.sn != ''">
                    and am.sn like concat(concat('%', #{entity.sn}),'%')
                </if>

                <if test="entity.manufacturerList != null and entity.manufacturerList.size > 0">
                    and am.manufacturer in
                    <foreach collection="entity.manufacturerList" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>

                <if test="entity.modelList != null and entity.modelList.size > 0">
                    and am.model in
                    <foreach collection="entity.modelList" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>

            </if>

            <if test="entity.deviceType == 2">
                and (ac.device_type = 202 or ac.device_type = 203)
                <if test="entity.sn != null and entity.sn != ''">
                    and ac.sn like concat(concat('%', #{entity.sn}),'%')
                </if>
                <if test="entity.manufacturerList != null and entity.manufacturerList.size > 0">
                    and ac.manufacturer in
                    <foreach collection="entity.manufacturerList" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="entity.modelList != null and entity.modelList.size > 0">
                    and ac.model in
                    <foreach collection="entity.modelList" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>

            </if>

            <if test="entity.deviceType == 3">
                and am.id in ( select meter_id from mdm_asset_service_point where id in (select id from
                asset_entity_relationship where type = 7 and parent_type = 3))
                <if test="entity.sn != null and entity.sn != ''">
                    and am.id in (select meter_id from mdm_asset_service_point where id in (select id from
                    asset_entity_relationship where type = 7 and parent_type = 3 and
                    parent_id in (select id from asset_line where sn like concat(concat('%', #{entity.sn}),'%'))))
                </if>

            </if>

            <if test="entity.deviceType == 4">
                and am.id in ( select meter_id from mdm_asset_service_point where id in (select id from
                asset_entity_relationship where type = 7 and parent_type = 4))
                <if test="entity.sn != null and entity.sn != ''">
                    and am.id in (select meter_id from mdm_asset_service_point where id in (select id from
                    asset_entity_relationship where type = 7 and parent_type = 4 and
                    parent_id in (select id from asset_transformer where sn like concat(concat('%',
                    #{entity.sn}),'%'))))
                </if>

            </if>

            <if test="entity.deviceType == 6">
                and actm.sn is not null
                <if test="entity.sn != null and entity.sn != ''">
                    and actm.sn like concat(concat('%', #{entity.sn}),'%')
                </if>

            </if>

            <if test="entity.deviceType == 7">
                and am.id in ( select meter_id from mdm_asset_service_point)
                <if test="entity.sn != null and entity.sn != ''">
                    and am.id in ( select meter_id from mdm_asset_service_point where sn like concat(concat('%',
                    #{entity.sn}),'%'))
                </if>
            </if>

            <if test="entity.deviceType == 8">
                and am.id in ( select meter_id from mdm_asset_service_point where id in ( select metering_id from
                mdm_asset_calc_obj_map where metering_type = 1))
                <if test="entity.sn != null and entity.sn != ''">
                    and am.id in (select meter_id from mdm_asset_service_point where id in (select metering_id from
                    mdm_asset_calc_obj_map where metering_type = 1 and id in ( select id from mdm_asset_calc_obj where
                    name like concat(concat('%', #{entity.sn}),'%') )))
                </if>
            </if>

        </if>

        <if test="_databaseId == 'oracle'">
            <if test="pageSize > 0">
                and rownum &lt;= #{pageSize}
            </if>
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

        <if test="_databaseId == 'mysql'">
            <if test="pageSize > 0">
                limit #{pageSize}
            </if>
        </if>

    </select>

    <select id="getMeterProfileByQueryProfile" resultType="AssetMeasurementProfile" parameterType="Request">

        select
        amp.*
        from ASSET_MEASUREMENT_PROFILE amp
        where amp.mg_id in (select group_id from ASSET_METER_GROUP_MAP amgm
        where
        amgm.type=1
        and amgm.id =
        (select id from Asset_Meter where sn=#{entity.sn})
        and amp.profile_id in (select profile_id from ASSET_MEASUREMENT_PROFILE_DI ampi
                                                 where ampi.DATAITEM_ID in
        <foreach collection="entity.dataItemList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        ))


    </select>

</mapper>