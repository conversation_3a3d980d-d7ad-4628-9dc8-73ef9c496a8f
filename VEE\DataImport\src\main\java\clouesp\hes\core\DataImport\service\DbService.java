package clouesp.hes.core.DataImport.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Connection;

@Slf4j
@Service("dbService")
public class DbService {

    private boolean isOracleDb = false; //目前只两种数据库，不是oracle就是 mysql ,所以用布尔型判断更容易

    @Resource(name = "persistenceJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    public boolean isOracleDb() {
        return isOracleDb;
    }

    public void loadCfg() {
        Connection con = null;
        try {
            con = jdbcTemplate.getDataSource().getConnection();
            String url = con.getMetaData().getURL();
            if ( url == null || url.isEmpty()  ){
                String logInfo = "Not find Db URL ";
                System.out.println(logInfo);
            }else {
                url = url.toLowerCase();
                if ( url.contains("oracle")){
                    isOracleDb = true ;
                }else if ( url.contains("mysql")){
                    isOracleDb = false ;
                }else {
                    String logInfo = "Db URL error :  " + url ;
                    System.out.println(logInfo);

                }
            }

        }catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (con != null) {
                    con.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }

}
