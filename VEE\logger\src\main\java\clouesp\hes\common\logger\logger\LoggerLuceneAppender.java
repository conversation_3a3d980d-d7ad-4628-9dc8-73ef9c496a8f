/*
 * 文件名：LoggerAppenderLucene.java 版权：Copyright by Power7000 Team 描述： 修改人：jybai 修改时间：2017年10月31日
 * 跟踪单号： 修改单号： 修改内容：
 */

package clouesp.hes.common.logger.logger;


import java.util.Date;

import org.apache.logging.log4j.Logger;


class LoggerLuceneAppender implements ILoggerAppender {

    private String key = "Lucene";

    private Logger logger = null;

    private LoggerAppenderController loggerController = new LoggerAppenderController();

    LuceneAppender luceneAppender = null;
    
    @Override
    public void start() {
        logger = loggerController.getLogger(key);
        loggerController.addLuceneAppender(key);
        luceneAppender = (LuceneAppender)loggerController.getAppender(key);
        loggerController.start(key);
    }

    @Override
    public void stop() {
        loggerController.stop(key);
    }

    @Override
    public void setLevel(LoggerLevel level) {
        loggerController.setLevel(level);
    }

    @Override
    public void writeLogInfo(LoggerLevel level, String serviceId, String deviceId,
                             String infoType, String info) {

        logger.log(LoggerUtils.getInstance().LoggerLevel2Level(level), info, new Date(), serviceId,
            deviceId, infoType);
    }
    
    @Override
    public void appendLogger(){
        if(luceneAppender == null)
            return;
        luceneAppender.appendLogger();
    }
    
    @Override
    public void directAppendLogger(){
        if(luceneAppender == null)
            return;
        luceneAppender.directAppendLogger();
    }    
}
