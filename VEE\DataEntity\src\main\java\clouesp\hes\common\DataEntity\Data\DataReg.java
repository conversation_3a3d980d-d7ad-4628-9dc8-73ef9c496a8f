package clouesp.hes.common.DataEntity.Data;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.MappedSuperclass;
import javax.persistence.Transient;

@MappedSuperclass
public class DataReg {
	@EmbeddedId
	private DataRegPK dataRegPK = new DataRegPK();
	@Column(name = "update_tv")
	private Date updateTv;
	@Column(name = "R0P1")
	private Double R0P1;
	@Column(name = "R0P2")
	private Double R0P2;
	@Column(name = "R0P3")
	private Double R0P3;
	@Column(name = "R0P4")
	private Double R0P4;
	
	@Column(name = "R0P5")
	private Double R0P5;
	@Column(name = "R0P6")
	private Double R0P6;
	@Column(name = "R0P7")
	private Double R0P7;
	@Column(name = "R0P8")
	private Double R0P8;
	
	@Column(name = "R1P1")
	private Double R1P1;
	@Column(name = "R1P2")
	private Double R1P2;
	@Column(name = "R1P3")
	private Double R1P3;
	@Column(name = "R1P4")
	private Double R1P4;

	@Column(name = "R1P5")
	private Double R1P5;
	@Column(name = "R1P6")
	private Double R1P6;
	@Column(name = "R1P7")
	private Double R1P7;
	@Column(name = "R1P8")
	private Double R1P8;
	
	@Column(name = "R2P1")
	private Double R2P1;
	@Column(name = "R2P2")
	private Double R2P2;
	@Column(name = "R2P3")
	private Double R2P3;
	@Column(name = "R2P4")
	private Double R2P4;

	@Column(name = "R2P5")
	private Double R2P5;
	@Column(name = "R2P6")
	private Double R2P6;
	@Column(name = "R2P7")
	private Double R2P7;
	@Column(name = "R2P8")
	private Double R2P8;
	
	@Column(name = "R3P1")
	private Double R3P1;
	@Column(name = "R3P2")
	private Double R3P2;
	@Column(name = "R3P3")
	private Double R3P3;
	@Column(name = "R3P4")
	private Double R3P4;

	@Column(name = "R3P5")
	private Double R3P5;
	@Column(name = "R3P6")
	private Double R3P6;
	@Column(name = "R3P7")
	private Double R3P7;
	@Column(name = "R3P8")
	private Double R3P8;

	@Column(name = "R4P1")
	private Double R4P1;
	@Column(name = "R4P2")
	private Double R4P2;
	@Column(name = "R4P3")
	private Double R4P3;
	@Column(name = "R4P4")
	private Double R4P4;

	@Column(name = "R4P5")
	private Double R4P5;
	@Column(name = "R4P6")
	private Double R4P6;
	@Column(name = "R4P7")
	private Double R4P7;
	@Column(name = "R4P8")
	private Double R4P8;

	@Column(name = "R0P1A1", columnDefinition = "decimal(21,6)")
	private Double R0P1A1;
	@Column(name = "R0P1A2", columnDefinition = "decimal(21,6)")
	private Double R0P1A2;
	@Column(name = "R0P1A3", columnDefinition = "decimal(21,6)")
	private Double R0P1A3;
	@Column(name = "R0P1A4", columnDefinition = "decimal(21,6)")
	private Double R0P1A4;
	
	@Column(name = "R1P1A1", columnDefinition = "decimal(21,6)")
	private Double R1P1A1;
	@Column(name = "R1P1A2", columnDefinition = "decimal(21,6)")
	private Double R1P1A2;
	@Column(name = "R1P1A3", columnDefinition = "decimal(21,6)")
	private Double R1P1A3;
	@Column(name = "R1P1A4", columnDefinition = "decimal(21,6)")
	private Double R1P1A4;
	
	
	@Column(name = "R2P1A1", columnDefinition = "decimal(21,6)")
	private Double R2P1A1;
	@Column(name = "R2P1A2", columnDefinition = "decimal(21,6)")
	private Double R2P1A2;
	@Column(name = "R2P1A3", columnDefinition = "decimal(21,6)")
	private Double R2P1A3;
	@Column(name = "R2P1A4", columnDefinition = "decimal(21,6)")
	private Double R2P1A4;
	
	@Column(name = "R3P1A1", columnDefinition = "decimal(21,6)")
	private Double R3P1A1;
	@Column(name = "R3P1A2", columnDefinition = "decimal(21,6)")
	private Double R3P1A2;
	@Column(name = "R3P1A3", columnDefinition = "decimal(21,6)")
	private Double R3P1A3;
	@Column(name = "R3P1A4", columnDefinition = "decimal(21,6)")
	private Double R3P1A4;
	
	@Column(name = "R4P1A1", columnDefinition = "decimal(21,6)")
	private Double R4P1A1;
	@Column(name = "R4P1A2", columnDefinition = "decimal(21,6)")
	private Double R4P1A2;
	@Column(name = "R4P1A3", columnDefinition = "decimal(21,6)")
	private Double R4P1A3;
	@Column(name = "R4P1A4", columnDefinition = "decimal(21,6)")
	private Double R4P1A4;
	

	@Column(name = "data_source")
	private Integer dataSource = 0;
	
	@Column(name = "data_version")
	private Integer dataVersion = 0;
	
	@Transient 
	private String timeType;
		
	public DataRegPK getDataRegPK() {
		return dataRegPK;
	}
	public void setDataRegPK(DataRegPK dataRegPK) {
		this.dataRegPK = dataRegPK;
	}
	public Date getUpdateTv() {
		return updateTv;
	}
	public void setUpdateTv(Date updateTv) {
		this.updateTv = updateTv;
	}	
	public Double getR0P1() {
		return R0P1;
	}
	public void setR0P1(Double r0p1) {
		R0P1 = r0p1;
	}
	public Double getR0P2() {
		return R0P2;
	}
	public void setR0P2(Double r0p2) {
		R0P2 = r0p2;
	}
	public Double getR0P3() {
		return R0P3;
	}
	public void setR0P3(Double r0p3) {
		R0P3 = r0p3;
	}
	public Double getR0P4() {
		return R0P4;
	}
	public void setR0P4(Double r0p4) {
		R0P4 = r0p4;
	}
	
	public Double getR0P5() {
		return R0P5;
	}
	public void setR0P5(Double r0p5) {
		R0P5 = r0p5;
	}
	public Double getR0P6() {
		return R0P6;
	}
	public void setR0P6(Double r0p6) {
		R0P6 = r0p6;
	}
	public Double getR0P7() {
		return R0P7;
	}
	public void setR0P7(Double r0p7) {
		R0P7 = r0p7;
	}
	public Double getR0P8() {
		return R0P8;
	}
	public void setR0P8(Double r0p8) {
		R0P8 = r0p8;
	}
	public Double getR1P1() {
		return R1P1;
	}
	public void setR1P1(Double r1p1) {
		R1P1 = r1p1;
	}
	public Double getR1P2() {
		return R1P2;
	}
	public void setR1P2(Double r1p2) {
		R1P2 = r1p2;
	}
	public Double getR1P3() {
		return R1P3;
	}
	public void setR1P3(Double r1p3) {
		R1P3 = r1p3;
	}
	public Double getR1P4() {
		return R1P4;
	}
	public void setR1P4(Double r1p4) {
		R1P4 = r1p4;
	}

	public Double getR1P5() {
		return R1P5;
	}

	public void setR1P5(Double r1P5) {
		R1P5 = r1P5;
	}

	public Double getR1P6() {
		return R1P6;
	}

	public void setR1P6(Double r1P6) {
		R1P6 = r1P6;
	}

	public Double getR1P7() {
		return R1P7;
	}

	public void setR1P7(Double r1P7) {
		R1P7 = r1P7;
	}

	public Double getR1P8() {
		return R1P8;
	}

	public void setR1P8(Double r1P8) {
		R1P8 = r1P8;
	}

	public Double getR2P1() {
		return R2P1;
	}
	public void setR2P1(Double r2p1) {
		R2P1 = r2p1;
	}
	public Double getR2P2() {
		return R2P2;
	}
	public void setR2P2(Double r2p2) {
		R2P2 = r2p2;
	}
	public Double getR2P3() {
		return R2P3;
	}
	public void setR2P3(Double r2p3) {
		R2P3 = r2p3;
	}
	public Double getR2P4() {
		return R2P4;
	}
	public void setR2P4(Double r2p4) {
		R2P4 = r2p4;
	}

	public Double getR2P5() {
		return R2P5;
	}

	public void setR2P5(Double r2P5) {
		R2P5 = r2P5;
	}

	public Double getR2P6() {
		return R2P6;
	}

	public void setR2P6(Double r2P6) {
		R2P6 = r2P6;
	}

	public Double getR2P7() {
		return R2P7;
	}

	public void setR2P7(Double r2P7) {
		R2P7 = r2P7;
	}

	public Double getR2P8() {
		return R2P8;
	}

	public void setR2P8(Double r2P8) {
		R2P8 = r2P8;
	}

	public Double getR3P1() {
		return R3P1;
	}
	public void setR3P1(Double r3p1) {
		R3P1 = r3p1;
	}
	public Double getR3P2() {
		return R3P2;
	}
	public void setR3P2(Double r3p2) {
		R3P2 = r3p2;
	}
	public Double getR3P3() {
		return R3P3;
	}
	public void setR3P3(Double r3p3) {
		R3P3 = r3p3;
	}
	public Double getR3P4() {
		return R3P4;
	}
	public void setR3P4(Double r3p4) {
		R3P4 = r3p4;
	}

	public Double getR3P5() {
		return R3P5;
	}

	public void setR3P5(Double r3P5) {
		R3P5 = r3P5;
	}

	public Double getR3P6() {
		return R3P6;
	}

	public void setR3P6(Double r3P6) {
		R3P6 = r3P6;
	}

	public Double getR3P7() {
		return R3P7;
	}

	public void setR3P7(Double r3P7) {
		R3P7 = r3P7;
	}

	public Double getR3P8() {
		return R3P8;
	}

	public void setR3P8(Double r3P8) {
		R3P8 = r3P8;
	}

	public Double getR4P1() {
		return R4P1;
	}
	public void setR4P1(Double r4p1) {
		R4P1 = r4p1;
	}
	public Double getR4P2() {
		return R4P2;
	}
	public void setR4P2(Double r4p2) {
		R4P2 = r4p2;
	}
	public Double getR4P3() {
		return R4P3;
	}
	public void setR4P3(Double r4p3) {
		R4P3 = r4p3;
	}
	public Double getR4P4() {
		return R4P4;
	}
	public void setR4P4(Double r4p4) {
		R4P4 = r4p4;
	}

	public Double getR4P5() {
		return R4P5;
	}

	public void setR4P5(Double r4P5) {
		R4P5 = r4P5;
	}

	public Double getR4P6() {
		return R4P6;
	}

	public void setR4P6(Double r4P6) {
		R4P6 = r4P6;
	}

	public Double getR4P7() {
		return R4P7;
	}

	public void setR4P7(Double r4P7) {
		R4P7 = r4P7;
	}

	public Double getR4P8() {
		return R4P8;
	}

	public void setR4P8(Double r4P8) {
		R4P8 = r4P8;
	}

	public Double getR0P1A1() {
		return R0P1A1;
	}
	public void setR0P1A1(Double r0p1a1) {
		R0P1A1 = r0p1a1;
	}
	public Double getR0P1A2() {
		return R0P1A2;
	}
	public void setR0P1A2(Double r0p1a2) {
		R0P1A2 = r0p1a2;
	}
	public Double getR0P1A3() {
		return R0P1A3;
	}
	public void setR0P1A3(Double r0p1a3) {
		R0P1A3 = r0p1a3;
	}
	public Double getR0P1A4() {
		return R0P1A4;
	}
	public void setR0P1A4(Double r0p1a4) {
		R0P1A4 = r0p1a4;
	}
	public Double getR1P1A1() {
		return R1P1A1;
	}
	public void setR1P1A1(Double r1p1a1) {
		R1P1A1 = r1p1a1;
	}
	public Double getR1P1A2() {
		return R1P1A2;
	}
	public void setR1P1A2(Double r1p1a2) {
		R1P1A2 = r1p1a2;
	}
	public Double getR1P1A3() {
		return R1P1A3;
	}
	public void setR1P1A3(Double r1p1a3) {
		R1P1A3 = r1p1a3;
	}
	public Double getR1P1A4() {
		return R1P1A4;
	}
	public void setR1P1A4(Double r1p1a4) {
		R1P1A4 = r1p1a4;
	}
	public Double getR2P1A1() {
		return R2P1A1;
	}
	public void setR2P1A1(Double r2p1a1) {
		R2P1A1 = r2p1a1;
	}
	public Double getR2P1A2() {
		return R2P1A2;
	}
	public void setR2P1A2(Double r2p1a2) {
		R2P1A2 = r2p1a2;
	}
	public Double getR2P1A3() {
		return R2P1A3;
	}
	public void setR2P1A3(Double r2p1a3) {
		R2P1A3 = r2p1a3;
	}
	public Double getR2P1A4() {
		return R2P1A4;
	}
	public void setR2P1A4(Double r2p1a4) {
		R2P1A4 = r2p1a4;
	}
	public Double getR3P1A1() {
		return R3P1A1;
	}
	public void setR3P1A1(Double r3p1a1) {
		R3P1A1 = r3p1a1;
	}
	public Double getR3P1A2() {
		return R3P1A2;
	}
	public void setR3P1A2(Double r3p1a2) {
		R3P1A2 = r3p1a2;
	}
	public Double getR3P1A3() {
		return R3P1A3;
	}
	public void setR3P1A3(Double r3p1a3) {
		R3P1A3 = r3p1a3;
	}
	public Double getR3P1A4() {
		return R3P1A4;
	}
	public void setR3P1A4(Double r3p1a4) {
		R3P1A4 = r3p1a4;
	}
	public Double getR4P1A1() {
		return R4P1A1;
	}
	public void setR4P1A1(Double r4p1a1) {
		R4P1A1 = r4p1a1;
	}
	public Double getR4P1A2() {
		return R4P1A2;
	}
	public void setR4P1A2(Double r4p1a2) {
		R4P1A2 = r4p1a2;
	}
	public Double getR4P1A3() {
		return R4P1A3;
	}
	public void setR4P1A3(Double r4p1a3) {
		R4P1A3 = r4p1a3;
	}
	public Double getR4P1A4() {
		return R4P1A4;
	}
	public void setR4P1A4(Double r4p1a4) {
		R4P1A4 = r4p1a4;
	}
	public Integer getDataSource() {
		return dataSource;
	}
	public void setDataSource(Integer dataSource) {
		this.dataSource = dataSource;
	}
	public Integer getDataVersion() {
		return dataVersion;
	}
	public void setDataVersion(Integer dataVersion) {
		this.dataVersion = dataVersion;
	}
	public String getTimeType() {
		return timeType;
	}
	public void setTimeType(String timeType) {
		this.timeType = timeType;
	}
}
