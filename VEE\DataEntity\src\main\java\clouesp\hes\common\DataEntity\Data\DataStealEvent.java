package clouesp.hes.common.DataEntity.Data;

import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name= "mdm_data_steal_event")
public class DataStealEvent {
	@EmbeddedId
	private DataStealEventPK dataStealEventPK = new DataStealEventPK();

	public DataStealEventPK getDataStealEventPK() {
		return dataStealEventPK;
	}

	public void setDataStealEventPK(DataStealEventPK dataStealEventPK) {
		this.dataStealEventPK = dataStealEventPK;
	}
}
