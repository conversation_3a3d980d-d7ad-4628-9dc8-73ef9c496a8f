package clouesp.hes.core.RealTimeDB;

import clouesp.hes.common.DataEntity.System.MdmSysService;
import clouesp.hes.common.DataRepository.Persistence.System.*;
import clouesp.hes.common.DataRepository.RealTime.System.*;
import clouesp.hes.common.logger.logger.Logger;
import clouesp.hes.common.logger.logger.LoggerAppenderType;
import clouesp.hes.common.logger.logger.LoggerLevel;
import clouesp.hes.core.RealTimeDB.config.ServerConfig;
import clouesp.hes.core.RealTimeDB.service.ReloadRTDBService;
import org.h2.tools.Server;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Component
public class InitCommandLineRunner implements CommandLineRunner {
	
	@Value("${spring.h2.tcpPort}")
	private String tcpPort; 
	
	@Autowired
    private ServerConfig serverConfig;	
	
	@Autowired
	private MdmSysServerRepository mdmSysServerRepository;
	
	@Autowired
	private RtMdmSysServerRepository rtMdmSysServerRepository;
	
	@Autowired
	private MdmSysServiceRepository mdmSysServiceRepository;
	
	@Autowired
	private RtMdmSysServiceRepository rtMdmSysServiceRepository;	
	
	@Autowired
	private MdmSysServiceAttributeRepository mdmSysServiceAttributeRepository;
	
	@Autowired
	private RtMdmSysServiceAttributeRepository rtMdmSysServiceAttributeRepository;

	@Resource(name="reloadRTDBService")
	private ReloadRTDBService reloadRTDBService;

	private int updateServiceCount = 0;

	@Override
	public void run(String... args) throws Exception {

		System.out.println("");
		System.out.println("******************************************************************************************");
		String logInfo = "       ReaTimeDb  Ver 2025041001 , Server IP: " + serverConfig.getServerAddress() +" , Server Port: " + serverConfig.getServerPort();
		System.out.println(logInfo);
		System.out.println("******************************************************************************************");
		System.out.println("");
		Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "System", logInfo);
		rtMdmSysServerRepository.saveAll(mdmSysServerRepository.findAll());
		rtMdmSysServiceRepository.saveAll(mdmSysServiceRepository.findAll());
		rtMdmSysServiceAttributeRepository.saveAll(mdmSysServiceAttributeRepository.findAll());
		
		startLogger();
		int initServiceRet = initService(1);
		serverConfig.setInitServiceRet(initServiceRet);
		if (initServiceRet == -1) {
			return;
		}
		reloadRTDBService.loadRealTimeDB();
		updateServiceState();
		startH2Server();
	}
	
	private int initService(int serviceType) {
		String serviceId = rtMdmSysServiceRepository.findServiceId(serviceType, serverConfig.getServerAddress());
 		serviceId = "50010001";
		
		if (serviceId == null) {
			 System.out.println("Failed to get the service");
	         Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, null, "0", "System", "Failed to get the service");
			return -1;
		}
		serverConfig.setServiceId(serviceId);
		serverConfig.loadCfg();
		String logInfo;
		logInfo = "Startup Param[Service id: " + serverConfig.getServiceId() + "]";
		System.out.println(logInfo);
		Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "System", logInfo); 
		return 0;
	}
	
    private int startLogger(){
    	
    	try {
	    	File directory = new File("");// 参数为空
	        String courseFile;
			
			courseFile = directory.getCanonicalPath();
			File parent = new File(courseFile);
			String parentPath = parent.getParent() + "//log//";
			 //设置写日志路径
	        Logger.getInstance().setIndexPath(parentPath);
	        //输出源固定为LoggerAppenderType.Lucene
	        Logger.getInstance().setAppenderType(LoggerAppenderType.Lucene);
	        //启动日志
	        Logger.getInstance().start();
	        //设置日志级别
	        Logger.getInstance().setLevel(LoggerLevel.INFO);       
			
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
        return 0;
    }
		
    private void startH2Server() {
        try {
            Server h2Server = Server.createTcpServer("-tcpPort", tcpPort, "-tcpAllowOthers").start();
            if (h2Server.isRunning(true)) {
            	
            } else {
                throw new RuntimeException("Could not start H2 server.");
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to start H2 server: ", e);
        }
    }	
	

	@Scheduled(fixedDelay = 60000)
	private void scheduledServiceState() {
		if (serverConfig.getServiceId() == null) {
			return;
		}

		int cycle = 10;
		try {
			List<MdmSysService> mdmSysServices = rtMdmSysServiceRepository.findByServiceType(6);
			String appServiceId = null;
			if (mdmSysServices != null && mdmSysServices.size() > 0) {
				appServiceId = mdmSysServices.get(0).getId();
			}
			if (appServiceId != null) {
				String strCycle = mdmSysServiceAttributeRepository.findValue(appServiceId,
						"Application.UpdateModuleStatus.Cycle");

				try {
					cycle = Integer.parseInt(strCycle);
				} catch (Exception e) {

				}
			}

			if (updateServiceCount < cycle) {
				updateServiceCount++;
				return;
			}
			updateServiceCount = 0;
			updateServiceState();
		} 
		catch (Exception e) {
			System.out.println("scheduledServiceState: " + e.getMessage());
		}
	}
		
	private void updateServiceState() {
		Optional<MdmSysService> optionalMdmSysService = rtMdmSysServiceRepository.findById(serverConfig.getServiceId());
		if (!optionalMdmSysService.isPresent()) {
			return;
		}
		MdmSysService mdmSysService = optionalMdmSysService.get();
		mdmSysService.setOnlineTime(new Date());
		mdmSysServiceRepository.save(mdmSysService);
	}
}
