package clouesp.hes.tools.datagenerator.Utils;

import clouesp.hes.common.DataEntity.Data.DataReg;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.List;

public class DataUtils {

    public static <T>List<T> convertDataReg(List<DataReg> dataRegList, Class<T> c) throws InstantiationException, IllegalAccessException {

        List<T> resultList = new ArrayList<>();



        for (DataReg data:dataRegList
             ) {

            T object = c.newInstance();

            Field[] fields = data.getClass().getDeclaredFields();

            for (Field field : fields) {
                int mod = field.getModifiers();
                if (Modifier.isStatic(mod) || Modifier.isFinal(mod)) {
                    continue;
                }
                field.setAccessible(true);

                field.set(object, field.get(data));

            }




            resultList.add(object);
        }

        return resultList;

    }
}
