package clouesp.hes.common.DataEntity.System;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name= "mdm_sys_service")
public class MdmSysService {
	@Id
	@Column(name = "id", nullable = false, columnDefinition = "varchar(32)")
	private String id;
	
	@Column(name = "host_id", columnDefinition = "varchar(32)")
	private String hostId;
	
	@Column(name = "service_type")
	private Integer serviceType;
	
	@Column(name = "is_online")
	private Integer isOnline;
	
	@Column(name = "server_id", columnDefinition = "varchar(32)")
	private String serverId;
	
	@Column(name = "online_time")
	private Date onlineTime;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getHostId() {
		return hostId;
	}

	public void setHostId(String hostId) {
		this.hostId = hostId;
	}

	public Integer getServiceType() {
		return serviceType;
	}

	public void setServiceType(Integer serviceType) {
		this.serviceType = serviceType;
	}

	public Integer getIsOnline() {
		return isOnline;
	}

	public void setIsOnline(Integer isOnline) {
		this.isOnline = isOnline;
	}

	public String getServerId() {
		return serverId;
	}

	public void setServerId(String serverId) {
		this.serverId = serverId;
	}

	public Date getOnlineTime() {
		return onlineTime;
	}

	public void setOnlineTime(Date onlineTime) {
		this.onlineTime = onlineTime;
	}
}
