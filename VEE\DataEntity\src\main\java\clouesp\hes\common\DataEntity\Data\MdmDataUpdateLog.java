package clouesp.hes.common.DataEntity.Data;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name= "mdm_data_update_log")
public class MdmDataUpdateLog {
	@EmbeddedId
	private MdmDataUpdateLogPK mdmDataUpdateLogPK = new MdmDataUpdateLogPK();
	
	@Column(name = "last_update_time")
	private Date lastUpdateTime;
	
	@Column(name = "last_data_time")
	private Date lastDataTime;

	public MdmDataUpdateLogPK getMdmDataUpdateLogPK() {
		return mdmDataUpdateLogPK;
	}

	public void setMdmDataUpdateLogPK(MdmDataUpdateLogPK mdmDataUpdateLogPK) {
		this.mdmDataUpdateLogPK = mdmDataUpdateLogPK;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	public Date getLastDataTime() {
		return lastDataTime;
	}

	public void setLastDataTime(Date lastDataTime) {
		this.lastDataTime = lastDataTime;
	}
}
