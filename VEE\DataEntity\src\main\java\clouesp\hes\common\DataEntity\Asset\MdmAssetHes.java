package clouesp.hes.common.DataEntity.Asset;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

/**
 * HES厂商信息实体类s
 */
@Data
@Entity
@Table(name = "MDM_ASSET_HES")
public class MdmAssetHes {

    /**
     * 由mdm厂商统一分配给hes厂商的HesID
     */
    @Id
    @Column(name = "HES_ID", nullable = false, length = 32)
    private String hesId;

    /**
     * HES的厂商名称
     */
    @Column(name = "COMPANY_NAME", length = 32)
    private String companyName;

    /**
     * 由mdm厂商统一分配给hes厂商认证用户名
     */
    @Column(name = "USER_NAME", length = 32)
    private String userName;

    /**
     * 由mdm厂商统一分配给hes厂商认证密码
     */
    @Column(name = "PWD", length = 32)
    private String pwd;

      /**
     * HES提供的Restful访问主机地址
     */
	 @Column(name = "REST_HOST")
     private String restHost;
 
    /**
     * HES提供的HTTP端口号
     */
    @Column(name = "REST_HTTP_PORT")
     private Integer restHttpPort; 
 
  /**
     * HES提供的HTTPS端口号
     */
    @Column(name = "REST_HTTPS_PORT")
    private Integer restHttpsPort;

    /**
     * hes厂商上传sftp数据文件存放主目录
     */
    @Column(name = "FTP_FILE_DIR", length = 255)
    private String ftpFileDir;

    /**
     * hes厂商本地数据文件存放主目录
     */
    @Column(name = "LOCAL_FILE_DIR", length = 255)
    private String localFileDir;

    /**
     * HES厂商相关描述
     */
    @Column(name = "INTRODUCTION", length = 255)
    private String introduction;
} 