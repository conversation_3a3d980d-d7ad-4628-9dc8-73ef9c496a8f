package clouesp.hes.common.DataRepository.RealTime.System;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import clouesp.hes.common.DataEntity.System.MdmSysService;

public interface RtMdmSysServiceRepository extends JpaRepository<MdmSysService, String>{
	@Query(value = "select a.id from mdm_sys_service a, mdm_sys_server b where "
			+ "a.server_id = b.id and a.service_type = :serviceType and b.ip = :ip"
			, nativeQuery = true)
	String findServiceId(@Param("serviceType") Integer serviceType, @Param("ip") String ip);
	
	List<MdmSysService>  findByServiceType(Integer ServiceType);
}
