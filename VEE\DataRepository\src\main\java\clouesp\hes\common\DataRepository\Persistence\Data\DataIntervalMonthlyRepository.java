package clouesp.hes.common.DataRepository.Persistence.Data;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import clouesp.hes.common.DataEntity.Data.DataIntervalMonthly;
import clouesp.hes.common.DataEntity.Data.DataRegPK;

public interface DataIntervalMonthlyRepository extends JpaRepository<DataIntervalMonthly, DataRegPK>{
	List<DataIntervalMonthly> findByDataRegPKSdpIdAndDataRegPKTvIn(
			String sdpId, 
			List<Date> tvs
			);	
	List<DataIntervalMonthly> findByDataRegPKSdpIdAndDataRegPKTvBetween(
			String sdpId, 
			Date startTv,
			Date endTv
			);		
}
