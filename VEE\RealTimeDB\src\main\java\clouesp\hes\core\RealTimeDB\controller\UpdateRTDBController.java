package clouesp.hes.core.RealTimeDB.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import clouesp.hes.common.DataEntity.Asset.AssetMeter;
import clouesp.hes.common.DataEntity.Asset.MdmAssetCalcObj;
import clouesp.hes.common.DataEntity.Asset.MdmAssetServicePoint;
import clouesp.hes.common.DataModel.api.AjaxJson;
import clouesp.hes.common.DataRepository.Persistence.Asset.MdmAssetCalcObjRepository;
import clouesp.hes.common.DataRepository.Persistence.Asset.MdmAssetCalcSchemeRepository;
import clouesp.hes.common.DataRepository.Persistence.Asset.MdmAssetVEERuleDataSourceRepository;
import clouesp.hes.common.DataRepository.Persistence.Asset.MdmAssetVEERuleParamRepository;
import clouesp.hes.common.DataRepository.Persistence.Asset.MdmAssetVEERuleRepository;
import clouesp.hes.common.DataRepository.Persistence.Dict.MeterDataStorageTableRepository;
import clouesp.hes.common.DataRepository.Persistence.Dict.VEEMethodRepository;
import clouesp.hes.common.DataRepository.Persistence.System.MdmSysServerRepository;
import clouesp.hes.common.DataRepository.Persistence.System.MdmSysServiceAttributeRepository;
import clouesp.hes.common.DataRepository.Persistence.System.MdmSysServiceRepository;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtAssetMeterRepository;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetCalcObjRepository;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetCalcSchemeRepository;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetServicePointRepository;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetVEERuleDataSourceRepository;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetVEERuleParamRepository;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetVEERuleRepository;
import clouesp.hes.common.DataRepository.RealTime.Dict.RtMeterDataStorageTableRepository;
import clouesp.hes.common.DataRepository.RealTime.Dict.RtVEEMethodRepository;
import clouesp.hes.common.DataRepository.RealTime.System.RtMdmSysServerRepository;
import clouesp.hes.common.DataRepository.RealTime.System.RtMdmSysServiceAttributeRepository;
import clouesp.hes.common.DataRepository.RealTime.System.RtMdmSysServiceRepository;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

@Api(tags={"Update RTDB API"})
@RestController
@RequestMapping("updateRTDB")
public class UpdateRTDBController {
	
	@Autowired
	private RtAssetMeterRepository rtAssetMeterRepository;
	
	@Autowired
	private RtMdmAssetServicePointRepository rtMdmAssetServicePointRepository;
		
	@Autowired
	private MdmSysServerRepository mdmSysServerRepository;
	
	@Autowired
	private RtMdmSysServerRepository rtMdmSysServerRepository;
	
	@Autowired
	private MdmSysServiceRepository mdmSysServiceRepository;
	
	@Autowired
	private RtMdmSysServiceRepository rtMdmSysServiceRepository;	
	
	@Autowired
	private MdmSysServiceAttributeRepository mdmSysServiceAttributeRepository;
	
	@Autowired
	private RtMdmSysServiceAttributeRepository rtMdmSysServiceAttributeRepository;
		
	@Autowired
	private MdmAssetVEERuleRepository mdmAssetVEERuleRepository;
	
	@Autowired
	private RtMdmAssetVEERuleRepository rtMdmAssetVEERuleRepository;
	
	@Autowired
	private MdmAssetVEERuleDataSourceRepository mdmAssetVEERuleDataSourceRepository;
	
	@Autowired
	private RtMdmAssetVEERuleDataSourceRepository rtMdmAssetVEERuleDataSourceRepository;
	
	@Autowired
	private MdmAssetVEERuleParamRepository mdmAssetVEERuleParamRepository;
	
	@Autowired
	private RtMdmAssetVEERuleParamRepository rtMdmAssetVEERuleParamRepository;
	
	@Autowired
	private VEEMethodRepository veeMethodRepository;
	
	@Autowired
	private RtVEEMethodRepository rtVEEMethodRepository;
	
	@Autowired
	private MeterDataStorageTableRepository meterDataStorageTableRepository;
	
	@Autowired
	private RtMeterDataStorageTableRepository rtMeterDataStorageTableRepository;
		
	
	@Autowired
	private MdmAssetCalcSchemeRepository mdmAssetCalcSchemeRepository;
	
	@Autowired
	private RtMdmAssetCalcSchemeRepository rtMdmAssetCalcSchemeRepository;
	
	@Autowired
	private MdmAssetCalcObjRepository mdmAssetCalcObjRepository;
	
	@Autowired
	private RtMdmAssetCalcObjRepository rtMdmAssetCalcObjRepository;
	
			
	@ApiOperation(value = "Update Asset Meter", notes = "", produces = "application/json")
	@RequestMapping(value = "/updateAssetMeter", method = RequestMethod.POST)
	public AjaxJson updateAssetMeter(
			@ApiParam(name = "access_token", value = "访问令牌", required = true) @RequestHeader("access_token") String accessToken,
			@ApiParam(name = "request", value = "更新的电表信息集", required = true) @RequestBody  List<AssetMeter> meters) {
		AjaxJson rsp = new AjaxJson();
		try {
			rtAssetMeterRepository.saveAll(meters);
			rsp.setSuccess(true);
		}
		catch(Exception e) {
			rsp.setSuccess(false);
			rsp.setErrorMsg("Failed Update [" + e.getMessage() + "]");
		}
		return rsp;

	}
	
	@ApiOperation(value = "Delete Asset Meter", notes = "", produces = "application/json")
	@RequestMapping(value = "/delAssetMeter", method = RequestMethod.POST)
	public AjaxJson delAssetMeter(
			@ApiParam(name = "access_token", value = "访问令牌", required = true) @RequestHeader("access_token") String accessToken,
			@ApiParam(name = "request", value = "需删除电表的ID", required = true) @RequestBody  String id) {
		AjaxJson rsp = new AjaxJson();
		try {
			rtAssetMeterRepository.deleteById(id);
			rsp.setSuccess(true);
		}
		catch(Exception e) {
			rsp.setSuccess(false);
			rsp.setErrorMsg("Failed Delete [" + e.getMessage() + "]");
		}
		return rsp;

	}	
	
	@ApiOperation(value = "Update Asset Service Point", notes = "", produces = "application/json")
	@RequestMapping(value = "/updateAssetServicePoint", method = RequestMethod.POST)
	public AjaxJson updateAssetServicePoint(
			@ApiParam(name = "access_token", value = "访问令牌", required = true) @RequestHeader("access_token") String accessToken,
			@ApiParam(name = "request", value = "更新的计量点信息集", required = true) @RequestBody  List<MdmAssetServicePoint> sdps) {
		AjaxJson rsp = new AjaxJson();
		try {
			rtMdmAssetServicePointRepository.saveAll(sdps);
			rsp.setSuccess(true);
		}
		catch(Exception e) {
			rsp.setSuccess(false);
			rsp.setErrorMsg("Failed Update [" + e.getMessage() + "]");
		}
		return rsp;

	}
	
	@ApiOperation(value = "Delete Asset Service Point", notes = "", produces = "application/json")
	@RequestMapping(value = "/delAssetServicePoint", method = RequestMethod.POST)
	public AjaxJson delAssetServicePoint(
			@ApiParam(name = "access_token", value = "访问令牌", required = true) @RequestHeader("access_token") String accessToken,
			@ApiParam(name = "request", value = "需删除计量点的ID", required = true) @RequestBody  String id) {
		AjaxJson rsp = new AjaxJson();
		try {
			rtMdmAssetServicePointRepository.deleteById(id);;
			rsp.setSuccess(true);
		}
		catch(Exception e) {
			rsp.setSuccess(false);
			rsp.setErrorMsg("Failed Delete [" + e.getMessage() + "]");
		}
		return rsp;

	}	
	
	@ApiOperation(value = "Update Asset Calc Obj", notes = "", produces = "application/json")
	@RequestMapping(value = "/updateAssetCalcObj", method = RequestMethod.POST)
	public AjaxJson updateAssetCalcObjMap(
			@ApiParam(name = "access_token", value = "访问令牌", required = true) @RequestHeader("access_token") String accessToken,
			@ApiParam(name = "request", value = "更新的计算对象信息集", required = true) @RequestBody  List<MdmAssetCalcObj> calcObjMaps) {
		AjaxJson rsp = new AjaxJson();
		try {
			rtMdmAssetCalcObjRepository.saveAll(calcObjMaps);
		}
		catch(Exception e) {
			rsp.setSuccess(false);
			rsp.setErrorMsg("Failed Update [" + e.getMessage() + "]");
		}
		return rsp;
	}
	
	@ApiOperation(value = "Delete Asset Calc Obj", notes = "", produces = "application/json")
	@RequestMapping(value = "/delAssetCalcObj", method = RequestMethod.POST)
	public AjaxJson delAssetCalcObj(
			@ApiParam(name = "access_token", value = "访问令牌", required = true) @RequestHeader("access_token") String accessToken,
			@ApiParam(name = "request", value = "需删除计算对象的ID", required = true) @RequestBody  String id) {
		AjaxJson rsp = new AjaxJson();
		try {
			rtMdmAssetCalcObjRepository.deleteById(id);
			rsp.setSuccess(true);
		}
		catch(Exception e) {
			rsp.setSuccess(false);
			rsp.setErrorMsg("Failed Delete [" + e.getMessage() + "]");
		}
		return rsp;
	}		

	@ApiOperation(value = "Update Asset Other", notes = "", produces = "application/json")
	@RequestMapping(value = "/updateAssetOther", method = RequestMethod.POST)
	public AjaxJson updateAssetOther(
			@ApiParam(name = "access_token", value = "访问令牌", required = true) @RequestHeader("access_token") String accessToken) {
		AjaxJson rsp = new AjaxJson();
		try {
			
			rtMdmSysServerRepository.saveAll(mdmSysServerRepository.findAll());
			rtMdmSysServiceRepository.saveAll(mdmSysServiceRepository.findAll());
			rtMdmSysServiceAttributeRepository.saveAll(mdmSysServiceAttributeRepository.findAll());			
			
			rtMdmAssetVEERuleRepository.saveAll(mdmAssetVEERuleRepository.findAll());
			rtMdmAssetVEERuleDataSourceRepository.saveAll(mdmAssetVEERuleDataSourceRepository.findAll());
			rtMdmAssetVEERuleParamRepository.saveAll(mdmAssetVEERuleParamRepository.findAll());
			
			rtVEEMethodRepository.saveAll(veeMethodRepository.findAll());
			
			rtMeterDataStorageTableRepository.saveAll(meterDataStorageTableRepository.findAll());
	
			rtMdmAssetCalcSchemeRepository.saveAll(mdmAssetCalcSchemeRepository.findAll());
			rtMdmAssetCalcObjRepository.saveAll(mdmAssetCalcObjRepository.findAll());


		
			rsp.setSuccess(true);
		}
		catch(Exception e) {
			rsp.setSuccess(false);
			rsp.setErrorMsg("Failed Update [" + e.getMessage() + "]");
		}
		return rsp;
	}		
}
