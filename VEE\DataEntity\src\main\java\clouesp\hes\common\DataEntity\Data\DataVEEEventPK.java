package clouesp.hes.common.DataEntity.Data;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;
import java.util.Date;
@Data
@Embeddable
public class DataVEEEventPK  implements Serializable {

    @Column(name = "object_id", columnDefinition = "varchar(32)")
    private String objectId;

    @Column(name = "tv")
    private Date tv;

    @Column(name = "event_id", columnDefinition = "varchar(32)")
    private String eventId;

    @Column(name = "scheme_type", columnDefinition = "varchar(32)")
    private Integer schemeType;

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((objectId == null) ? 0 : objectId.hashCode());
        result = prime * result
                + ((tv == null) ? 0 : tv.hashCode());
        result = prime * result
                + ((eventId == null) ? 0 : eventId.hashCode());
        result = prime * result
                + ((schemeType == null) ? 0 : schemeType.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        DataVEEEventPK other = (DataVEEEventPK) obj;
        if (objectId == null) {
            if (other.objectId != null)
                return false;
        } else if (!objectId.equals(other.objectId))
            return false;
        if (tv == null) {
            if (other.tv != null)
                return false;
        } else if (!tv.equals(other.tv))
            return false;
        if (eventId == null) {
            if (other.eventId != null)
                return false;
        } else if (!eventId.equals(other.eventId))
            return false;
        if (schemeType == null) {
            if (other.schemeType != null)
                return false;
        } else if (!schemeType.equals(other.schemeType))
            return false;
        return true;
    }
}
