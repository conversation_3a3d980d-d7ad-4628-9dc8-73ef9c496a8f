package clouesp.hes.core.DataCalc.Utils;

import java.io.File;
import java.io.IOException;

/** 
* <AUTHOR>
* @Time 2021-1-22 22:00:34 
* @Version 1.0
* <p>Description:文件类方法</p>
*/
public class FileUtils {	
	/**
	 * 获取上级路径
	 * @return
	 */
	public static String getParentPath() {
		String parentPath = null;

		try {
			File directory = new File("");// 参数为空
			String courseFile;
			courseFile = directory.getCanonicalPath();
	 		File parent = new File(courseFile);
	   		parentPath = parent.getParent();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
  
   		return parentPath;
	}
}
