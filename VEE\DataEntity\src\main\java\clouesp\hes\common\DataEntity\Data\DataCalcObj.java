package clouesp.hes.common.DataEntity.Data;

import javax.persistence.*;
import java.text.SimpleDateFormat;
import java.util.Date;

@Entity
@Table(name= "mdm_data_calc_obj")
public class DataCalcObj {
	@EmbeddedId
	private DataCalcObjPK dataCalcObjPK = new DataCalcObjPK();
	
	@Column(name = "out_total")
	private Double outTotal = 0d;
	
	@Column(name = "in_total")
	private Double inTotal = 0d;	
	
	@Column(name = "calc_value")
	private Double calcValue;
	
	@Column(name = "miss_data")
	private Integer missData = 0;
	
	@Column(name = "update_tv")
	private Date updateTv;
	
	@Transient 
	private Integer totalNumber = 0;

	public DataCalcObjPK getDataCalcObjPK() {
		return dataCalcObjPK;
	}

	public void setDataCalcObjPK(DataCalcObjPK dataCalcObjPK) {
		this.dataCalcObjPK = dataCalcObjPK;
	}

	public Double getOutTotal() {
		return outTotal;
	}

	public void setOutTotal(Double outTotal) {
		this.outTotal = outTotal;
	}

	public Double getInTotal() {
		return inTotal;
	}

	public void setInTotal(Double inTotal) {
		this.inTotal = inTotal;
	}

	public Double getCalcValue() {
		return calcValue;
	}

	public void setCalcValue(Double calcValue) {
		this.calcValue = calcValue;
	}

	public Integer getMissData() {
		return missData;
	}

	public void setMissData(Integer missData) {
		this.missData = missData;
	}

	public Date getUpdateTv() {
		return updateTv;
	}

	public void setUpdateTv(Date updateTv) {
		this.updateTv = updateTv;
	}

	public Integer getTotalNumber() {
		return totalNumber;
	}

	public void setTotalNumber(Integer totalNumber) {
		this.totalNumber = totalNumber;
	}

	@Override
	public String toString(){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		 String  value = dataCalcObjPK.toString() +  " , outTotal = " + outTotal + " , inTotal = " + inTotal  + " , calcValue = " + calcValue +
				" , missData = " + missData  ;

		 if (updateTv != null ) value += ", updateTv = "  + sdf.format(updateTv) ;
		value +=   " , totalNumber = " + totalNumber ;


		 return value ;
	}
}
