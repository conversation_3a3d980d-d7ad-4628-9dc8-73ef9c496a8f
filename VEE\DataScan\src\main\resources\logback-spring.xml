<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="10 seconds" debug="false">
    <contextName>logback</contextName>
    <property name="log.path" value="./DataScanLog"/>
    <!--输出到控制台-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <!-- <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
             <level>ERROR</level>
         </filter>-->
        <encoder>
            <!-- <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %contextName [%thread] %-5level %logger{36} - %msg%n</pattern> -->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!--输出到文件-->
    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
            <fileNamePattern>${log.path}/DataScan.%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>
        <encoder>
            <!--   <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %contextName [%thread] %-5level %logger{36} - %msg%n</pattern> -->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <logger name="RocketmqClient" level="error" >

    </logger>


    <root level="info">
        <appender-ref ref="console"/>
        <appender-ref ref="file"/>
    </root>

</configuration>



<!--<?xml version="1.0" encoding="UTF-8"?>-->
<!--<configuration  scan="true" scanPeriod="10 seconds">-->
<!--	<property name="LOG_DIR" value="./RocketMQlogs"/>-->
<!--&lt;!&ndash; rocketmq日志 &ndash;&gt;-->
<!--    <appender name="RocketmqClientAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">-->
<!--        <file>${LOG_DIR}/rocketmq_client.log</file>-->
<!--        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">-->
<!--            <fileNamePattern>${LOG_DIR}/history/rocketmq_client.%d{yyyyMMdd}.%i.log</fileNamePattern>-->
<!--            <maxFileSize>5120MB</maxFileSize>-->
<!--            &lt;!&ndash;保留时间,单位:天&ndash;&gt;-->
<!--            <maxHistory>3</maxHistory>-->
<!--        </rollingPolicy>-->
<!--        <encoder charset="UTF-8">-->
<!--            <pattern>%d{yy-MM-dd.HH:mm:ss.SSS} [%-16t] %-5p %-22c{0} %X{ServiceId} - %m%n</pattern>-->
<!--        </encoder>-->
<!--    </appender>-->
<!--    <logger name="RocketmqClient" level="error" additivity="false">-->
<!--        <level value="error" />-->
<!--        <appender-ref ref="RocketmqClientAppender"/>-->
<!--    </logger>-->
<!--    <root level="error">-->
<!--        <appender-ref ref="RocketmqClientAppender" />-->
<!--    </root>-->
<!--</configuration>-->