package clouesp.hes.common.DataEntity.Data;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Embeddable;

@Embeddable
public class DataEnergyPK implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 3126714621649311943L;
	@Column(name = "sdp_id", nullable = false, columnDefinition = "varchar(32)")
	private String sdpId;
	@Column(name = "tv")
	private Date tv;
	@Column(name = "scheme_id", columnDefinition = "varchar(32)")
	private String schemeId;
	public String getSdpId() {
		return sdpId;
	}
	public void setSdpId(String sdpId) {
		this.sdpId = sdpId;
	}
	public Date getTv() {
		return tv;
	}
	public void setTv(Date tv) {
		this.tv = tv;
	}
	
	public String getSchemeId() {
		return schemeId;
	}
	public void setSchemeId(String schemeId) {
		this.schemeId = schemeId;
	}
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((sdpId == null) ? 0 : sdpId.hashCode());
		result = prime * result
				+ ((tv == null) ? 0 : tv.hashCode());
		result = prime * result
				+ ((schemeId == null) ? 0 : schemeId.hashCode());
		return result;
	}
	
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		DataEnergyPK other = (DataEnergyPK) obj;
		if (sdpId == null) {
			if (other.sdpId != null)
				return false;
		} else if (!sdpId.equals(other.sdpId))
			return false;
		if (tv == null) {
			if (other.tv != null)
				return false;
		} else if (!tv.equals(other.tv))
			return false;
		if (schemeId == null) {
			if (other.schemeId != null)
				return false;
		} else if (!schemeId.equals(other.schemeId))
			return false;		
		return true;
	}						
}
