package clouesp.hes.core.RealTimeDB.config;

import java.net.InetAddress;


import org.springframework.boot.web.context.WebServerInitializedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Component
public class ServerConfig implements ApplicationListener<WebServerInitializedEvent>{
	private int serverPort;
	private String serviceId;
	private int initServiceRet;
	
	@Override
	public void onApplicationEvent(WebServerInitializedEvent event) {
		// TODO Auto-generated method stub
		serverPort = event.getWebServer().getPort();
	}
	
	public void loadCfg() {

	}	

	public String getServerAddress() {
		InetAddress address = null;
        try {
            address = InetAddress.getLocalHost();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return address.getHostAddress();
	}

	public int getServerPort() {
		return serverPort;
	}

	public String getServiceId() {
		return serviceId;
	}

	public void setServiceId(String serviceId) {
		this.serviceId = serviceId;
	}

	public int getInitServiceRet() {
		return initServiceRet;
	}

	public void setInitServiceRet(int initServiceRet) {
		this.initServiceRet = initServiceRet;
	}
}
