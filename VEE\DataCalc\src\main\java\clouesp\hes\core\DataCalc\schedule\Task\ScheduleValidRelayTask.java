package clouesp.hes.core.DataCalc.schedule.Task;

import clouesp.hes.common.DataEntity.Asset.MdmAssetServicePoint;
import clouesp.hes.common.DataEntity.Asset.MdmAssetVEERule;
import clouesp.hes.common.DataRepository.Persistence.Data.DataVEEEventRepository;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetServicePointRepository;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetVEERuleRepository;
import clouesp.hes.common.DataRepository.RealTime.Data.RtDataVEEEventRepository;
import clouesp.hes.common.DataRepository.RealTime.Data.RtMdmDataUpdateLogRepository;
import clouesp.hes.common.logger.logger.Logger;
import clouesp.hes.common.logger.logger.LoggerLevel;
import clouesp.hes.core.DataCalc.config.ServerConfig;
import clouesp.hes.core.DataCalc.service.Validation.ValidRelayService;
import jline.internal.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 用于定时刷新mdm中接收的hes数据，是否有延迟情况，按照配置节点配置的扫描周期进行定期扫描
 */
@Component("scheduleValidRelayTask")
public class ScheduleValidRelayTask {


    @Autowired
    private ServerConfig serverConfig;
    @Autowired
    private ValidRelayService validRelayService;
    @Autowired
    private RtDataVEEEventRepository rtDataVEEEventRepository;
    @Autowired
    private DataVEEEventRepository dataVEEEventRepository;
    private boolean executing = false;

    public void init(String serviceId) {

        //加载event数据到缓存

        rtDataVEEEventRepository.saveAll(dataVEEEventRepository.findAll());
        Log.info("ScheduleValidRelayTask init finish ");
    }

    public void dispatch() {
        String logInfo = null;
        if (executing) {
            return;
        }
        executing = true;

        logInfo = "Start ValidRelayTask";
        Log.info(logInfo);
        Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "ValidRelay", logInfo);

        //开始扫描数据,并保存
        try
        {
            validRelayService.execute();
        }
        catch (Exception ex)
        {
            Log.info(ex);
        }

        executing = false;
        logInfo = "End ValidRelayTask";
        Log.info(logInfo);
        Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "ValidRelay", logInfo);
    }






}
