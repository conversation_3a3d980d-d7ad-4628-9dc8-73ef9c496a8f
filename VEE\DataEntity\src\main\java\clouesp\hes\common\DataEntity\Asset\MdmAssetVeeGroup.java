package clouesp.hes.common.DataEntity.Asset;

import javax.persistence.*;
import java.util.Objects;

@Entity
@Table(name = "MDM_ASSET_VEE_GROUP")
public class MdmAssetVeeGroup {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "ID", nullable = false, length = 32)
    private String id;
    @Basic
    @Column(name = "NAME", nullable = true, length = 64)
    private String name;
    @Basic
    @Column(name = "TYPE", nullable = true, precision = 0)
    private Integer type;
    @Basic
    @Column(name = "INTRODUCTION", nullable = true, length = 256)
    private String introduction;
    @Basic
    @Column(name = "OBJECT_TYPE", nullable = true, precision = 0)
    private Integer objectType;
    @Basic
    @Column(name = "SDP_NUMBER", nullable = true, precision = 0)
    private Long sdpNumber;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getIntroduction() {
        return introduction;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }

    public Integer getObjectType() {
        return objectType;
    }

    public void setObjectType(Integer objectType) {
        this.objectType = objectType;
    }

    public Long getSdpNumber() {
        return sdpNumber;
    }

    public void setSdpNumber(Long sdpNumber) {
        this.sdpNumber = sdpNumber;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MdmAssetVeeGroup that = (MdmAssetVeeGroup) o;
        return Objects.equals(id, that.id) && Objects.equals(name, that.name) && Objects.equals(type, that.type) && Objects.equals(introduction, that.introduction) && Objects.equals(objectType, that.objectType) && Objects.equals(sdpNumber, that.sdpNumber);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name, type, introduction, objectType, sdpNumber);
    }
}
