package clouesp.hes.common.DataEntity.Data;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name= "mdm_data_interval_history")
public class DataIntervalHistory{
	@EmbeddedId
	private DataRegHistoryPK dataRegHistoryPK = new DataRegHistoryPK();
	@Column(name = "update_tv")
	private Date updateTv;
	@Column(name = "R0P1")
	private Double R0P1;
	@Column(name = "R0P2")
	private Double R0P2;
	@Column(name = "R0P3")
	private Double R0P3;
	@Column(name = "R0P4")
	private Double R0P4;
	
	@Column(name = "R0P5")
	private Double R0P5;
	@Column(name = "R0P6")
	private Double R0P6;
	@Column(name = "R0P7")
	private Double R0P7;
	@Column(name = "R0P8")
	private Double R0P8;
	
	@Column(name = "R1P1")
	private Double R1P1;
	@Column(name = "R1P2")
	private Double R1P2;
	@Column(name = "R1P3")
	private Double R1P3;
	@Column(name = "R1P4")
	private Double R1P4;
	
	@Column(name = "R2P1")
	private Double R2P1;
	@Column(name = "R2P2")
	private Double R2P2;
	@Column(name = "R2P3")
	private Double R2P3;
	@Column(name = "R2P4")
	private Double R2P4;	
	
	@Column(name = "R3P1")
	private Double R3P1;
	@Column(name = "R3P2")
	private Double R3P2;
	@Column(name = "R3P3")
	private Double R3P3;
	@Column(name = "R3P4")
	private Double R3P4;	
	
	@Column(name = "R4P1")
	private Double R4P1;
	@Column(name = "R4P2")
	private Double R4P2;
	@Column(name = "R4P3")
	private Double R4P3;
	@Column(name = "R4P4")
	private Double R4P4;	
	
	@Column(name = "R0P1A1", columnDefinition = "decimal(21,6)")
	private Double R0P1A1;
	@Column(name = "R0P1A2", columnDefinition = "decimal(21,6)")
	private Double R0P1A2;
	@Column(name = "R0P1A3", columnDefinition = "decimal(21,6)")
	private Double R0P1A3;
	@Column(name = "R0P1A4", columnDefinition = "decimal(21,6)")
	private Double R0P1A4;
		
	@Column(name = "R1P1A1", columnDefinition = "decimal(21,6)")
	private Double R1P1A1;
	@Column(name = "R1P1A2", columnDefinition = "decimal(21,6)")
	private Double R1P1A2;
	@Column(name = "R1P1A3", columnDefinition = "decimal(21,6)")
	private Double R1P1A3;
	@Column(name = "R1P1A4", columnDefinition = "decimal(21,6)")
	private Double R1P1A4;
	
	@Column(name = "R2P1A1", columnDefinition = "decimal(21,6)")
	private Double R2P1A1;
	@Column(name = "R2P1A2", columnDefinition = "decimal(21,6)")
	private Double R2P1A2;
	@Column(name = "R2P1A3", columnDefinition = "decimal(21,6)")
	private Double R2P1A3;
	@Column(name = "R2P1A4", columnDefinition = "decimal(21,6)")
	private Double R2P1A4;
	
	@Column(name = "R3P1A1", columnDefinition = "decimal(21,6)")
	private Double R3P1A1;
	@Column(name = "R3P1A2", columnDefinition = "decimal(21,6)")
	private Double R3P1A2;
	@Column(name = "R3P1A3", columnDefinition = "decimal(21,6)")
	private Double R3P1A3;
	@Column(name = "R3P1A4", columnDefinition = "decimal(21,6)")
	private Double R3P1A4;
	
	@Column(name = "R4P1A1", columnDefinition = "decimal(21,6)")
	private Double R4P1A1;
	@Column(name = "R4P1A2", columnDefinition = "decimal(21,6)")
	private Double R4P1A2;
	@Column(name = "R4P1A3", columnDefinition = "decimal(21,6)")
	private Double R4P1A3;
	@Column(name = "R4P1A4", columnDefinition = "decimal(21,6)")
	private Double R4P1A4;
	
	@Column(name = "operator_id", nullable = false, columnDefinition = "varchar(32)")
	private String operatorId;
	
	@Column(name = "operator_name", nullable = false, columnDefinition = "varchar(64)")
	private String operatorName;
	
	@Column(name = "operator_ip", nullable = false, columnDefinition = "varchar(32)")
	private String operatorIp;
	
	@Column(name = "operator_reason", nullable = false, columnDefinition = "varchar(256)")
	private String operatorReason;

	public DataRegHistoryPK getDataRegHistoryPK() {
		return dataRegHistoryPK;
	}

	public void setDataRegHistoryPK(DataRegHistoryPK dataRegHistoryPK) {
		this.dataRegHistoryPK = dataRegHistoryPK;
	}

	public Date getUpdateTv() {
		return updateTv;
	}

	public void setUpdateTv(Date updateTv) {
		this.updateTv = updateTv;
	}

	public Double getR0P1() {
		return R0P1;
	}

	public void setR0P1(Double r0p1) {
		R0P1 = r0p1;
	}

	public Double getR0P2() {
		return R0P2;
	}

	public void setR0P2(Double r0p2) {
		R0P2 = r0p2;
	}

	public Double getR0P3() {
		return R0P3;
	}

	public void setR0P3(Double r0p3) {
		R0P3 = r0p3;
	}

	public Double getR0P4() {
		return R0P4;
	}

	public void setR0P4(Double r0p4) {
		R0P4 = r0p4;
	}

	public Double getR0P5() {
		return R0P5;
	}

	public void setR0P5(Double r0p5) {
		R0P5 = r0p5;
	}

	public Double getR0P6() {
		return R0P6;
	}

	public void setR0P6(Double r0p6) {
		R0P6 = r0p6;
	}

	public Double getR0P7() {
		return R0P7;
	}

	public void setR0P7(Double r0p7) {
		R0P7 = r0p7;
	}

	public Double getR0P8() {
		return R0P8;
	}

	public void setR0P8(Double r0p8) {
		R0P8 = r0p8;
	}

	public Double getR1P1() {
		return R1P1;
	}

	public void setR1P1(Double r1p1) {
		R1P1 = r1p1;
	}

	public Double getR1P2() {
		return R1P2;
	}

	public void setR1P2(Double r1p2) {
		R1P2 = r1p2;
	}

	public Double getR1P3() {
		return R1P3;
	}

	public void setR1P3(Double r1p3) {
		R1P3 = r1p3;
	}

	public Double getR1P4() {
		return R1P4;
	}

	public void setR1P4(Double r1p4) {
		R1P4 = r1p4;
	}

	public Double getR2P1() {
		return R2P1;
	}

	public void setR2P1(Double r2p1) {
		R2P1 = r2p1;
	}

	public Double getR2P2() {
		return R2P2;
	}

	public void setR2P2(Double r2p2) {
		R2P2 = r2p2;
	}

	public Double getR2P3() {
		return R2P3;
	}

	public void setR2P3(Double r2p3) {
		R2P3 = r2p3;
	}

	public Double getR2P4() {
		return R2P4;
	}

	public void setR2P4(Double r2p4) {
		R2P4 = r2p4;
	}

	public Double getR3P1() {
		return R3P1;
	}

	public void setR3P1(Double r3p1) {
		R3P1 = r3p1;
	}

	public Double getR3P2() {
		return R3P2;
	}

	public void setR3P2(Double r3p2) {
		R3P2 = r3p2;
	}

	public Double getR3P3() {
		return R3P3;
	}

	public void setR3P3(Double r3p3) {
		R3P3 = r3p3;
	}

	public Double getR3P4() {
		return R3P4;
	}

	public void setR3P4(Double r3p4) {
		R3P4 = r3p4;
	}

	public Double getR4P1() {
		return R4P1;
	}

	public void setR4P1(Double r4p1) {
		R4P1 = r4p1;
	}

	public Double getR4P2() {
		return R4P2;
	}

	public void setR4P2(Double r4p2) {
		R4P2 = r4p2;
	}

	public Double getR4P3() {
		return R4P3;
	}

	public void setR4P3(Double r4p3) {
		R4P3 = r4p3;
	}

	public Double getR4P4() {
		return R4P4;
	}

	public void setR4P4(Double r4p4) {
		R4P4 = r4p4;
	}

	public Double getR0P1A1() {
		return R0P1A1;
	}

	public void setR0P1A1(Double r0p1a1) {
		R0P1A1 = r0p1a1;
	}

	public Double getR0P1A2() {
		return R0P1A2;
	}

	public void setR0P1A2(Double r0p1a2) {
		R0P1A2 = r0p1a2;
	}

	public Double getR0P1A3() {
		return R0P1A3;
	}

	public void setR0P1A3(Double r0p1a3) {
		R0P1A3 = r0p1a3;
	}

	public Double getR0P1A4() {
		return R0P1A4;
	}

	public void setR0P1A4(Double r0p1a4) {
		R0P1A4 = r0p1a4;
	}

	public Double getR1P1A1() {
		return R1P1A1;
	}

	public void setR1P1A1(Double r1p1a1) {
		R1P1A1 = r1p1a1;
	}

	public Double getR1P1A2() {
		return R1P1A2;
	}

	public void setR1P1A2(Double r1p1a2) {
		R1P1A2 = r1p1a2;
	}

	public Double getR1P1A3() {
		return R1P1A3;
	}

	public void setR1P1A3(Double r1p1a3) {
		R1P1A3 = r1p1a3;
	}

	public Double getR1P1A4() {
		return R1P1A4;
	}

	public void setR1P1A4(Double r1p1a4) {
		R1P1A4 = r1p1a4;
	}

	public Double getR2P1A1() {
		return R2P1A1;
	}

	public void setR2P1A1(Double r2p1a1) {
		R2P1A1 = r2p1a1;
	}

	public Double getR2P1A2() {
		return R2P1A2;
	}

	public void setR2P1A2(Double r2p1a2) {
		R2P1A2 = r2p1a2;
	}

	public Double getR2P1A3() {
		return R2P1A3;
	}

	public void setR2P1A3(Double r2p1a3) {
		R2P1A3 = r2p1a3;
	}

	public Double getR2P1A4() {
		return R2P1A4;
	}

	public void setR2P1A4(Double r2p1a4) {
		R2P1A4 = r2p1a4;
	}

	public Double getR3P1A1() {
		return R3P1A1;
	}

	public void setR3P1A1(Double r3p1a1) {
		R3P1A1 = r3p1a1;
	}

	public Double getR3P1A2() {
		return R3P1A2;
	}

	public void setR3P1A2(Double r3p1a2) {
		R3P1A2 = r3p1a2;
	}

	public Double getR3P1A3() {
		return R3P1A3;
	}

	public void setR3P1A3(Double r3p1a3) {
		R3P1A3 = r3p1a3;
	}

	public Double getR3P1A4() {
		return R3P1A4;
	}

	public void setR3P1A4(Double r3p1a4) {
		R3P1A4 = r3p1a4;
	}

	public Double getR4P1A1() {
		return R4P1A1;
	}

	public void setR4P1A1(Double r4p1a1) {
		R4P1A1 = r4p1a1;
	}

	public Double getR4P1A2() {
		return R4P1A2;
	}

	public void setR4P1A2(Double r4p1a2) {
		R4P1A2 = r4p1a2;
	}

	public Double getR4P1A3() {
		return R4P1A3;
	}

	public void setR4P1A3(Double r4p1a3) {
		R4P1A3 = r4p1a3;
	}

	public Double getR4P1A4() {
		return R4P1A4;
	}

	public void setR4P1A4(Double r4p1a4) {
		R4P1A4 = r4p1a4;
	}

	public String getOperatorId() {
		return operatorId;
	}

	public void setOperatorId(String operatorId) {
		this.operatorId = operatorId;
	}

	public String getOperatorName() {
		return operatorName;
	}

	public void setOperatorName(String operatorName) {
		this.operatorName = operatorName;
	}

	public String getOperatorIp() {
		return operatorIp;
	}

	public void setOperatorIp(String operatorIp) {
		this.operatorIp = operatorIp;
	}

	public String getOperatorReason() {
		return operatorReason;
	}

	public void setOperatorReason(String operatorReason) {
		this.operatorReason = operatorReason;
	}	
}
