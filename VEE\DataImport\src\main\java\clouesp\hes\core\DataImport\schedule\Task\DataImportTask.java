package clouesp.hes.core.DataImport.schedule.Task;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import clouesp.hes.core.DataImport.service.DataImportService;
import clouesp.hes.core.DataImport.service.EventImportService;

@Component("dataImportTask")
public class DataImportTask {
	
	@Resource(name="dataImportService")
	private DataImportService dataImportService;
	
	@Resource(name="eventImportService")
	private EventImportService eventImportService;	
	
	public void dispatch() {
		dataImportService.execute();
		eventImportService.execute();
	}
}
