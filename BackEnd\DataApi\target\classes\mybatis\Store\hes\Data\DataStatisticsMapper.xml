<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="DataStatisticsMapper">
    <select id="getDeviceStatList" resultType="DeviceStatResult"  parameterType="Request">
        select
            dsd.id,
            dsd.id_type,
            dsd.tv,
            dsd.tv_type,
            dsd.count_current as devCount,
            dsd.percent as rate
            <if test="entity">

                <choose>
                    <when test="entity.idType == '2'.toString() or entity.idType == '3'.toString() or entity.idType == '4'.toString()">
                        ,dn.name as statName
                    </when>
                    <otherwise>
                        ,so.name as statName
                    </otherwise>
                </choose>
            </if>

        from
        sys_org so,
        data_statistics_device dsd
        <if test="entity">
            <if test="entity.idType == '2'.toString()">
            left join dict_manufacturer dn on dsd.id = dn.id
            </if>
            <if test="entity.idType == '3'.toString()">
                left join dict_device_model dn on dsd.id = dn.id
            </if>
            <if test="entity.idType == '4'.toString()">
                left join dict_communication_type dn on dsd.id = dn.id
            </if>

        </if>

        where
        1 = 1
        <if test="params.orgIdList != null and params.orgIdList.size > 0">
            and dsd.org_id in
            <foreach collection="params.orgIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        and dsd.org_id = so.id
        <if test="entity">
            <if test="entity.orgId != null and entity.orgId != ''">
                and so.id = #{entity.orgId}
            </if>
            <if test="entity.parentOrgTid != null and entity.parentOrgTid != ''">
                and so.parent_org_tid = #{entity.parentOrgTid}
            </if>
            <if test="entity.tvType != null and entity.tvType != ''">
                and dsd.tv_type = #{entity.tvType}
            </if>
            <if test="entity.idType != null and entity.idType != ''">
                and dsd.id_type = #{entity.idType}
            </if>
            <if test="entity.startTv != null">
                and dsd.tv &gt;= #{entity.startTv}
            </if>
            <if test="entity.endTv != null">
                and dsd.tv &lt; #{entity.endTv}
            </if>
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <select id="getIntegrityList" resultType="IntegrityResult"  parameterType="Request">
        select
            <choose>
                <when test="entity.idTypeList != null
                        and entity.idTypeList.size == 1
                        and (entity.idTypeList.get(0) ==  '2'.toString()
                        or entity.idTypeList.get(0) ==  '3'.toString()
                        or entity.idTypeList.get(0) ==  '4'.toString())">
                    ditgr.id,
                    dn.name as statName,
                </when>
                <otherwise>
                    so.id,
                    so.name as statName,
                </otherwise>
            </choose>
            ditgr.id_type,
            ditgr.tv,
            ditgr.tv_type,
            ditgr.profile_id,
            ditgr.integrity,
            ditgr.count_actual,
            ditgr.count_total
        from
            sys_org so,
            data_integrity ditgr

            <if test="entity.idTypeList != null and entity.idTypeList.size == 1">

                <if test="entity.idTypeList.get(0) == '2'.toString()">
                    left join dict_manufacturer dn on ditgr.id = dn.id
                </if>
                <if test="entity.idTypeList.get(0) == '3'.toString()">
                    left join dict_device_model dn on ditgr.id = dn.id
                </if>
                <if test="entity.idTypeList.get(0) == '4'.toString()">
                    left join dict_communication_type dn on ditgr.id = dn.id
                </if>
            </if>

        where
            1 = 1

            <if test="params.orgIdList != null and params.orgIdList.size > 0">
                and ditgr.org_id in
                <foreach collection="params.orgIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>


            and ditgr.org_id = so.id

            <if test="entity.orgId != null and entity.orgId != ''">
                 and so.id = #{entity.orgId}
            </if>
            <if test="entity.parentOrgTid != null and entity.parentOrgTid != ''">
                and so.parent_org_tid = #{entity.parentOrgTid}
            </if>
            <if test="entity.tvType != null and entity.tvType != ''">
                and ditgr.tv_type = #{entity.tvType}
            </if>

            <if test="entity.profileIdList != null and entity.profileIdList.size > 0">
                and ditgr.profile_id in
                <foreach collection="entity.profileIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>

            </if>

            <if test="entity.idTypeList != null and entity.idTypeList.size > 0">
                 and ditgr.id_type in
                 <foreach collection="entity.idTypeList" item="item" separator="," open="(" close=")">
                     #{item}
                 </foreach>
            </if>

            <if test="entity.startTv != null">
                  and ditgr.tv &gt;= #{entity.startTv}
            </if>
            <if test="entity.endTv != null">
                  and ditgr.tv &lt; #{entity.endTv}
            </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <select id="getEventStatList" resultType="EventStatResult"  parameterType="Request">
        select
            event_id,
            event_name,
            count_current,
            percent
        from
            data_statistics_event
        where
            1 = 1

        <if test="params.orgIdList != null and params.orgIdList.size > 0">
            and org_id in
            <foreach collection="params.orgIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>


        <if test="entity">
            <if test="entity.orgId != null and entity.orgId != ''">
                and org_id = #{entity.orgId}
            </if>
            <if test="entity.tvType != null and entity.tvType != ''">
                and tv_type = #{entity.tvType}
            </if>
            <if test="entity.startTv != null">
                and tv &gt;= #{entity.startTv}
            </if>
            <if test="entity.endTv != null">
                and tv &lt; #{entity.endTv}
            </if>
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

</mapper>