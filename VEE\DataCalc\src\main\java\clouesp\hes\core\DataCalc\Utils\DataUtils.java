package clouesp.hes.core.DataCalc.Utils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Date;

import clouesp.hes.common.CommonUtils.ObjectUtils;
import clouesp.hes.common.DataEntity.Data.DataReg;
import clouesp.hes.common.DataEntity.Data.DataRegPK;
import clouesp.hes.common.DataModel.api.MdmDataVeeBase;

public class DataUtils {
	public static DataReg mdmDataVeeBase2DataReg(MdmDataVeeBase data) {
		try {
			DataReg dataReg = new DataReg();
			DataRegPK dataRegPK = dataReg.getDataRegPK();
			dataReg.setUpdateTv(new Date());
			dataReg.setDataSource(1);

			Class cls = MdmDataVeeBase.class;
			Field[] fields = cls.getDeclaredFields();
			for (Field field : fields) {
				field.setAccessible(true);
				Object srcObj = field.get(data);
				if(srcObj == null) {
					continue;
				}
				String name = field.getName();
				Field dstField = ObjectUtils.getField(dataReg, name);
				if(dstField == null) {
					dstField = ObjectUtils.getField(dataRegPK, name);
					if(dstField != null) {
						dstField.setAccessible(true);
						dstField.set(dataRegPK, srcObj);
					}

				} else {
					dstField.setAccessible(true);
					if(srcObj instanceof BigDecimal) {
						BigDecimal srcValue = (BigDecimal) srcObj;
						dstField.set(dataReg, srcValue.doubleValue());
					} else {
						dstField.set(dataReg, srcObj);
					}

				}
			}
			return dataReg;
		} catch (Exception e) {
			return  null;
		}
	}

}
