package clouesp.hes.common.DataEntity.Asset;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@ApiModel(value="mdmAssetMeterReplacement",description="换表、换CTPT记录表")
@Entity
@Table(name= "mdm_asset_meter_replacement")
@IdClass(MdmAssetMeterReplacementPK.class)
public class MdmAssetMeterReplacement {
    @Id
    @Column(name = "sdp_id", nullable = false, columnDefinition = "varchar(32)")
    private String sdpId;

    @Id
    @Column(name = "tv")
    private Date tv;

    @Id
    @Column(name = "operation_type")
    private Integer operationType;


    @Column(name = "old_meter_id", columnDefinition = "varchar(32)")
    private String oldMeterId;

    @Column(name = "old_ct")
    private Integer oldCt;
    @Column(name = "oldPt")
    private Integer oldPt;

    @Column(name = "old_r0p1")
    private Double oldR0p1;
    @Column(name = "old_r0p2")
    private Double oldR0p2;
    @Column(name = "old_r0p3")
    private Double oldR0p3;
    @Column(name = "old_r0p4")
    private Double oldR0p4;
    @Column(name = "old_r0p5")
    private Double oldR0p5;
    @Column(name = "old_r0p6")
    private Double oldR0p6;
    @Column(name = "old_r0p7")
    private Double oldR0p7;
    @Column(name = "old_r0p8")
    private Double oldR0p8;

    @Column(name = "old_r1p1")
    private Double oldR1p1;
    @Column(name = "old_r1p2")
    private Double oldR1p2;
    @Column(name = "old_r1p3")
    private Double oldR1p3;
    @Column(name = "old_r1p4")
    private Double oldR1p4;

    @Column(name = "old_r2p1")
    private Double oldR2p1;
    @Column(name = "old_r2p2")
    private Double oldR2p2;
    @Column(name = "old_r2p3")
    private Double oldR2p3;
    @Column(name = "old_r2p4")
    private Double oldR2p4;

    @Column(name = "old_r3p1")
    private Double oldR3p1;
    @Column(name = "old_r3p2")
    private Double oldR3p2;
    @Column(name = "old_r3p3")
    private Double oldR3p3;
    @Column(name = "old_r3p4")
    private Double oldR3p4;

    @Column(name = "old_r4p1")
    private Double oldR4p1;
    @Column(name = "old_r4p2")
    private Double oldR4p2;
    @Column(name = "old_r4p3")
    private Double oldR4p3;
    @Column(name = "old_r4p4")
    private Double oldR4p4;

    @Column(name = "new_meter_id", columnDefinition = "varchar(32)")
    private String newMeterId;

    @Column(name = "new_ct")
    private Integer newCt;
    @Column(name = "newPt")
    private Integer newPt;

    @Column(name = "new_r0p1")
    private Double newR0p1;
    @Column(name = "new_r0p2")
    private Double newR0p2;
    @Column(name = "new_r0p3")
    private Double newR0p3;
    @Column(name = "new_r0p4")
    private Double newR0p4;
    @Column(name = "new_r0p5")
    private Double newR0p5;
    @Column(name = "new_r0p6")
    private Double newR0p6;
    @Column(name = "new_r0p7")
    private Double newR0p7;
    @Column(name = "new_r0p8")
    private Double newR0p8;

    @Column(name = "new_r1p1")
    private Double newR1p1;
    @Column(name = "new_r1p2")
    private Double newR1p2;
    @Column(name = "new_r1p3")
    private Double newR1p3;
    @Column(name = "new_r1p4")
    private Double newR1p4;

    @Column(name = "new_r2p1")
    private Double newR2p1;
    @Column(name = "new_r2p2")
    private Double newR2p2;
    @Column(name = "new_r2p3")
    private Double newR2p3;
    @Column(name = "new_r2p4")
    private Double newR2p4;

    @Column(name = "new_r3p1")
    private Double newR3p1;
    @Column(name = "new_r3p2")
    private Double newR3p2;
    @Column(name = "new_r3p3")
    private Double newR3p3;
    @Column(name = "new_r3p4")
    private Double newR3p4;

    @Column(name = "new_r4p1")
    private Double newR4p1;
    @Column(name = "new_r4p2")
    private Double newR4p2;
    @Column(name = "new_r4p3")
    private Double newR4p3;
    @Column(name = "new_r4p4")
    private Double newR4p4;

    @Column(name = "data_source")
    private Integer dataSource;

    @Column(name = "create_tv")
    private Date createTv;

    @Column(name = "operator_id", columnDefinition = "varchar(32)")
    private String operatorId;

    @Column(name = "operator_name")
    private String operatorName;

    @Column(name = "operator_tv")
    private Date operatorTv;

    @Column(name = "processing_type")
    private Integer processingType;
}
