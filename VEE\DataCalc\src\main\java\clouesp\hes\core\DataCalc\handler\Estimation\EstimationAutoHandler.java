package clouesp.hes.core.DataCalc.handler.Estimation;

import clouesp.hes.common.CommonUtils.ObjectUtils;
import clouesp.hes.common.DataEntity.Data.DataVEEEvent;
import clouesp.hes.common.MqBus.MQMsg;
import clouesp.hes.core.DataCalc.handler.BaseHandler;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import jline.internal.Log;

import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;

public class EstimationAutoHandler implements BaseHandler{
private LinkedBlockingQueue<DataVEEEvent> eventQueue = new LinkedBlockingQueue<DataVEEEvent>();
	
	private EventEstimationProc eventEstimationProc = null;
	private Thread eventEstimationProcThread = null;	

	@Override
	public void init(String serviceId) {
		// TODO Auto-generated method stub
		if(eventEstimationProcThread == null) {
			eventEstimationProc = new EventEstimationProc();
			eventEstimationProcThread = new Thread(eventEstimationProc);
			eventEstimationProcThread.start();				
		}		
	}

	@Override
	public <T> void handler(MQMsg<T> mqMsg) {
		// TODO Auto-generated method stub
		if(mqMsg == null)
			return;		
		if(mqMsg.getLoad() == null)
			return;
		String jsonStr = mqMsg.getLoad().toString();
		Map<String, Object> mapEvent = JSONObject.parseObject(jsonStr, new TypeReference<Map<String, Object>>(){});
		try {
			DataVEEEvent event = (DataVEEEvent) ObjectUtils.mapToObject(mapEvent, DataVEEEvent.class);
			eventQueue.put(event);
			
		} catch (Exception e) {
			// TODO Auto-generated catch block
			Log.info("2 jsonStr = " + jsonStr);
			e.printStackTrace();
		}		
	}


	public static void main(String[] args) {
		Log.info("test");
		String jsonStr ="{\"eventId\":\"1010001\",\"veeEventClass\":1,\"tv\":1690819200000,\"dataVEEEventPK\":{\"eventId\":\"1010001\",\"tv\":1690819200000,\"schemeType\":2,\"objectId\":\"ffb55ea7fbb2423fa9f6fb5584678bc8\"	},\"schemeType\":2,	\"dataType\":107,\"dataSource\":3,\"estimationStatus\":0,\"objectId\":\"ffb55ea7fbb2423fa9f6fb5584678bc8\",\"objectType\":8}" ;
		Map<String, Object> mapEvent = JSONObject.parseObject(jsonStr, new TypeReference<Map<String, Object>>(){});
		try {
			DataVEEEvent event = (DataVEEEvent) ObjectUtils.mapToObject(mapEvent, DataVEEEvent.class);
			Log.info(event);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	private class EventEstimationProc implements Runnable{

		@Override
		public void run() {
			// TODO Auto-generated method stub
			while(true) {
				try {
					
					Thread.sleep(1000);
				} catch (InterruptedException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
			
		}
		
	}

}
