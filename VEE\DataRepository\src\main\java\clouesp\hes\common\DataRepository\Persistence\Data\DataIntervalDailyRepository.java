package clouesp.hes.common.DataRepository.Persistence.Data;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;

import clouesp.hes.common.DataEntity.Data.DataIntervalDaily;
import clouesp.hes.common.DataEntity.Data.DataRegPK;

public interface DataIntervalDailyRepository extends JpaRepository<DataIntervalDaily, DataRegPK>{
	List<DataIntervalDaily> findByDataRegPKSdpIdAndDataRegPKTvIn(
			String sdpId, 
			List<Date> tvs
			);	
	
	List<DataIntervalDaily> findByDataRegPKSdpIdAndDataRegPKTvBetween(
			String sdpId, 
			Date startTv,
			Date endTv
			);		
}
