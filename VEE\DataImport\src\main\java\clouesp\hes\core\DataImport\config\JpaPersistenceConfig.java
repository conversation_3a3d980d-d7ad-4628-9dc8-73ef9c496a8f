package clouesp.hes.core.DataImport.config;

import java.util.HashMap;
import java.util.Map;

import javax.persistence.EntityManager;
import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateSettings;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "entityManagerFactoryPersistence",//配置连接工厂 entityManagerFactory
        transactionManagerRef = "transactionManagerPersistence", //配置 事物管理器  transactionManager		
		basePackages={
		"clouesp.hes.common.DataRepository.Persistence"
		})
public class JpaPersistenceConfig {
	@Autowired
	@Qualifier("persistenceDS")
	private DataSource dataSource;
	
    @Autowired
    private JpaProperties jpaProperties;
    
    @Value("${spring.jpa.hibernate.persistence-dialect}")
    private String persistenceDialect;
    
    @Autowired
    private HibernateProperties hibernateProperties;
    
    @Bean(name = "entityManagerPersistence")
    public EntityManager entityManager(EntityManagerFactoryBuilder builder) {
        return entityManagerFactoryPersistence(builder).getObject().createEntityManager();
    }
    
    @Bean(name = "entityManagerFactoryPersistence")
    @Primary
    public LocalContainerEntityManagerFactoryBean entityManagerFactoryPersistence(EntityManagerFactoryBuilder builder) {
        Map<String,String> map = new HashMap<>();
        map.put("hibernate.dialect", persistenceDialect);
        jpaProperties.setProperties(map);
    	Map<String, Object> properties = hibernateProperties.determineHibernateProperties(
    		       jpaProperties.getProperties(), new HibernateSettings());
    	 
        return builder
                .dataSource(dataSource)
                .properties(properties)
                .packages("clouesp.hes.common.DataEntity")
                .persistenceUnit("veePersistenceUnit")
                .build();
    }

    @Bean(name = "transactionManagerPersistence")
    @Primary
    PlatformTransactionManager transactionManagerPersistence(EntityManagerFactoryBuilder builder) {
        return new JpaTransactionManager(entityManagerFactoryPersistence(builder).getObject());
    }   
}
