<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="AssetCommunicatorMapper">
    <select id="getCommunicatorList" resultType="AssetCommunicator"  parameterType="PagingRequest">
        select
        ac.*,
        dm.name as manufacturerName,
        ddm.name as modelName,
        ddm.protocol_id as protocolId,
        dct.name as commTypeName,
        so.name as orgName,
        so.org_code as orgCode,

        case when af.id is not null
        then 1
        else
        0
        end as favorite,

        dcs.ip_addr as ipAddr,
        dcs.ip_port as ipPort,
        dcs.update_tv as statusUpdateTime,

        case when dcs.communicator_id is null
        then -1
        when dcs.com_status = 1
        then 1
        else
        0
        end as onlineState

        from
        asset_communicator ac
        left join asset_favorite af
        on ac.id = af.id and af.id_type = 2 and af.user_id = #{params.userId}

        left join dict_manufacturer dm on ac.manufacturer = dm.id

        left join dict_device_model ddm on ac.model = ddm.id

        left join dict_communication_type dct on ac.com_type = dct.id

        left join sys_org so on ac.org_id = so.id

        left join data_comminication_status dcs

        on ac.id = dcs.communicator_id

        where
        (ac.device_type = 202 or ac.device_type = 203)

        <if test="params.orgIdList != null and params.orgIdList.size > 0">
            and ac.org_id in
            <foreach collection="params.orgIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="params.queryOrgIdList != null and params.queryOrgIdList.size > 0">
            and ac.org_id in
            <foreach collection="params.queryOrgIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="entity">
            <if test="entity.id != null and entity.id != ''">
                and ac.id = #{entity.id}
            </if>

            <if test="entity.sn != null and entity.sn != ''">
                AND ac.SN LIKE CONCAT(CONCAT('%', #{entity.sn}),'%')
            </if>

            <if test="entity.mac != null and entity.mac != ''">
                and ac.mac like concat(concat('%', #{entity.mac}),'%')
            </if>

            <if test="entity.name != null and entity.name != ''">
                and ac.name like concat(concat('%', #{entity.name}),'%')
            </if>


            <if test="entity.fwVersion != null and entity.fwVersion != ''">
                and ac.fw_version like concat(concat('%', #{entity.fwVersion}),'%')
            </if>

            <if test="entity.fwVersionList != null and entity.fwVersionList.size > 0">
                and ac.fw_version in
                <foreach collection="entity.fwVersionList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="entity.manufacturerList != null and entity.manufacturerList.size > 0">
                and ac.manufacturer in
                <foreach collection="entity.manufacturerList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="entity.comTypeList != null and entity.comTypeList.size > 0">
                and ac.com_type in
                <foreach collection="entity.comTypeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="entity.modelList != null and entity.modelList.size > 0">
                and ac.model in
                <foreach collection="entity.modelList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="entity.deviceTypeList != null and entity.deviceTypeList.size > 0">
                and ac.device_type in
                <foreach collection="entity.deviceTypeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="entity.favorite != null">

                <choose>
                    <when test="entity.favorite == 1">
                        and af.id is not null
                    </when >
                    <otherwise>
                        and af.id is null
                    </otherwise>
                </choose>

            </if>

            <if test="entity.onlineState != null">

                <choose>
                    <when test="entity.onlineState == -1">
                        and dcs.communicator_id is null
                    </when >
                    <when test="entity.onlineState == 1">
                        and dcs.com_status = 1
                    </when >
                    <otherwise>
                        and dcs.com_status = 0
                    </otherwise>
                </choose>

            </if>

        </if>


        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <select id="getCommunicatorMeterAssetList" resultType="MeterOfDcuBaseInfo"  parameterType="Request">
        select am.id as id , am.sn as sn ,am.password as password , am.HLS_AK as hlsAk , am.HLS_EK as hlsEk , am.INDEX_DCU as indexDcu ,
               am.mac as mac , am.IS_ENCRYPT as isEncrypt , am.KEY_FLAG as keyFlag ,ddm.protocol_id  as protocolId ,
               dp.name as protocolName   , am.com_type as comType	, am.com_port as comPort  , am.auth_type as authType , ddm.id as modelId , ddm.CONNECTION_MODE as wiringMethod from asset_meter am
           left	join  dict_device_model ddm on ddm.id = am.model
           left join dict_protocol dp on dp.id = ddm.protocol_id
        where am.id in (select id from asset_meter where COMMUNICATOR_ID= #{entity.communicatorId})

    </select>

</mapper>