package clouesp.hes.common.DataMode.Statistics;

import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

@Data
public class MdmDataOutageStatisticsPK {

    private static final long serialVersionUID = -363062234371734353L;
    private String  orgId ;    //组织机构ID
    private String  lineId ;   //线路
    private Integer sdpType ;  //SDP类型,字典值1111,为0表示不分类
    private Integer timeType ; //统计时间类型，字典值1104，2-日，3-月
    private Date tv ; //统计时间

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public Integer getSdpType() {
        return sdpType;
    }

    public void setSdpType(Integer sdpType) {
        this.sdpType = sdpType;
    }

    public Integer getTimeType() {
        return timeType;
    }

    public void setTimeType(Integer timeType) {
        this.timeType = timeType;
    }

    public Date getTv() {
        return tv;
    }

    public void setTv(Date tv) {
        this.tv = tv;
    }

    public  void SetTvOnlyDate(Date date){
        Calendar calendar=Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        tv = calendar.getTime();
    }

    public String toString(){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return "orgId = " + orgId + " , lineId = " + (sdpType == null ? "null" : sdpType.intValue() ) + " , timeType = " +
                (timeType == null ? "null" : timeType.intValue() ) + " , tv = " +  (tv == null ? "null" : sdf.format(tv)) ;
    }
}
