package clouesp.hes.core.DataCalc.service.Data;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import clouesp.hes.common.DataEntity.Data.DataCalcObj;
import clouesp.hes.common.DataEntity.Data.DataCalcObjEnergy;
import clouesp.hes.common.DataEntity.Data.DataCalcObjMiss;
import clouesp.hes.core.DataCalc.dao.Persistence.DaoSupport;

@Service("calcAccessService")
public class CalcAccessService {
	@Resource(name = "daoSupport")
	private DaoSupport dao;
	
	@SuppressWarnings("unchecked")
	public Map<Object, Object> querySumValue(Map<Object, Object> params) throws Exception{	
		Map<Object, Object> data = (Map<Object, Object>)dao.findForObject("CalcAccessMapper.querySumValue", params);
		return data;
	}	
	
	public Integer querySdpCount(Map<Object, Object> params) throws Exception{	
		Integer count = (Integer)dao.findForObject("CalcAccessMapper.querySdpCount", params);
		return count;
	}
	
	@SuppressWarnings("unchecked")
	public List<DataCalcObjMiss> queryMissList(Map<Object, Object> params) throws Exception{	
		return (List<DataCalcObjMiss>)dao.findForList("CalcAccessMapper.queryMissList", params);
	}	
	
	public void batchSaveCalcObj(List<DataCalcObj> dataCalcObjs) throws Exception {
		dao.save("CalcAccessMapper.batchSaveCalcObj", dataCalcObjs);
	}
	
	public void batchSaveCalcObjEnergy(List<DataCalcObjEnergy> dataCalcObjs) throws Exception {
		dao.save("CalcAccessMapper.batchSaveCalcObjEnergy", dataCalcObjs);
	}
	
	
}
