package clouesp.hes.core.DataCalc.service.Data;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import clouesp.hes.common.DataEntity.Data.*;
import jline.internal.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import clouesp.hes.common.DataEntity.Asset.MdmAssetServicePoint;
import clouesp.hes.common.DataEntity.Asset.MdmAssetVEERuleDataSource;
import clouesp.hes.common.DataRepository.Persistence.Data.DataIntervalDailyRepository;
import clouesp.hes.common.DataRepository.Persistence.Data.DataIntervalMinutelyRepository;
import clouesp.hes.common.DataRepository.Persistence.Data.DataIntervalMonthlyRepository;
import clouesp.hes.common.DataRepository.Persistence.Data.DataRegDailyRepository;
import clouesp.hes.common.DataRepository.Persistence.Data.DataRegMinutelyRepository;
import clouesp.hes.common.DataRepository.Persistence.Data.DataRegMonthlyRepository;

@Service("dataSourceService")
public class DataSourceService {
	@Autowired
	private DataRegMinutelyRepository dataRegMinutelyRepository;
	
	@Autowired
	private DataRegDailyRepository dataRegDailyRepository;
	
	@Autowired
	private DataRegMonthlyRepository dataRegMonthlyRepository;
	
	@Autowired
	private DataIntervalMinutelyRepository dataIntervalMinutelyRepository;
	
	@Autowired
	private DataIntervalDailyRepository dataIntervalDailyRepository;
	
	@Autowired
	private DataIntervalMonthlyRepository dataIntervalMonthlyRepository;
		
	public List<DataReg> getEstimationDataList(
			MdmAssetVEERuleDataSource dataSource, 
			MdmAssetServicePoint sdp,
			Date refTv,
			List<Date> refTvs) {
		String objectId = sdp.getId();
		int field = Calendar.YEAR;
		if(dataSource.getCycleType() == 1) {
			field = Calendar.MINUTE;
		}
		else if(dataSource.getCycleType() == 2) {
			field = Calendar.DAY_OF_MONTH;
		}
		else if (dataSource.getCycleType() == 3) {
			field = Calendar.MONTH;
		}
		
		int dataType = dataSource.getDataType();
		
		int amount = dataSource.getStartCycle();
		int cycleCount = dataSource.getCycleCount();
		int onePointCycle = 1;

		if(dataSource.getCycleType() == 1) {
			int dataCycle = 0;
			//寄存器
			if (dataType == 101) {
				dataCycle = sdp.getRegisterScheme();

				//获得分钟的周期
				if(dataSource.getCycleType() == 1)
				{
					onePointCycle = dataCycle;
				}

			}
			if (dataType == 102) {
				dataCycle = sdp.getIntervalScheme();



			} else if (dataType == 103) {
				dataCycle = sdp.getInstScheme();
			}
			/*
			if (dataCycle > 0) {
				cycleCount = (cycleCount * dataCycle);
			}

			 */
		}

		Calendar cal = Calendar.getInstance();	
		cal.setTime(refTv);
		cal.add(field, amount * onePointCycle);
		
		List<Date> tvs = new ArrayList<Date>();
		tvs.add(cal.getTime());
		for(int i = 1; i < cycleCount; i++) {
			cal.add(field, onePointCycle);
			tvs.add(cal.getTime());
		}
		
		List<DataReg> dataList = new ArrayList<DataReg>();
				
		switch (dataType) {
		case 101:		
			if (dataSource.getSchemeType() == 1) {
				List<DataRegMinutely> datas = dataRegMinutelyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
				if (datas == null)
					return null;
				/*
				int estmationCount = 0;
				for (DataRegMinutely data : datas) {
					boolean find = false;
					for (Date estmationTv : refTvs) {
						if (data.getDataRegPK().getTv().getTime() == estmationTv.getTime()) {
							estmationCount++;
							find = true;
							break;
						}
					}
					if (!find) {
						dataList.add(data);
					}
				}
				if (dataList.size() < cycleCount - estmationCount)
					return null;

				 */

				for (DataRegMinutely data : datas) {
					dataList.add(data);

				}
				if (dataList.size() != cycleCount
						&& dataList.size() != (cycleCount - refTvs.size()))
					return null;
			}			
			else if (dataSource.getSchemeType() == 2) {
				List<DataRegDaily> datas = dataRegDailyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
				if (datas == null)
					return null;

				Log.info("datas size:" + datas.size());
				/*
				int estmationCount = 0;
				for (DataRegDaily data : datas) {
					boolean find = false;
					for (Date estmationTv : refTvs) {
						if (data.getDataRegPK().getTv().getTime() == estmationTv.getTime()) {
							estmationCount++;
							find = true;
							break;
						}
					}
					if (!find) {
						dataList.add(data);
					}
				}

				 */
				for (DataRegDaily data : datas) {
					dataList.add(data);

				}
				if (dataList.size() != cycleCount
				&& dataList.size() != (cycleCount - refTvs.size()))
					return null;
			} 
			else if (dataSource.getSchemeType() == 3) {
				List<DataRegMonthly> datas = dataRegMonthlyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
				if (datas == null)
					return null;
				/*
				int estmationCount = 0;
				for (DataRegMonthly data : datas) {
					boolean find = false;
					for (Date estmationTv : refTvs) {
						if (data.getDataRegPK().getTv().getTime() == estmationTv.getTime()) {
							estmationCount++;
							find = true;
							break;
						}
					}
					if (!find) {
						dataList.add(data);
					}
				}
				if (dataList.size() < cycleCount - estmationCount)
					return null;

				 */
				for (DataRegMonthly data : datas) {
					dataList.add(data);

				}
				if (dataList.size() != cycleCount
						&& dataList.size() != (cycleCount - refTvs.size()))
					return null;

			}
		break;
		
		case 102:		
			if (dataSource.getSchemeType() == 1) {
				List<DataIntervalMinutely> datas = dataIntervalMinutelyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
				if (datas == null)
					return null;
				int estmationCount = 0;
				for (DataIntervalMinutely data : datas) {
					boolean find = false;
					for (Date estmationTv : refTvs) {
						if (data.getDataRegPK().getTv().getTime() == estmationTv.getTime()) {
							estmationCount++;
							find = true;
							break;
						}
					}
					if (!find) {
						dataList.add(data);
					}
				}
				if (dataList.size() < cycleCount - estmationCount)
					return null;			
			}			
			else if (dataSource.getSchemeType() == 2) {
				List<DataIntervalDaily> datas = dataIntervalDailyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
				if (datas == null)
					return null;
				int estmationCount = 0;
				for (DataIntervalDaily data : datas) {
					boolean find = false;
					for (Date estmationTv : refTvs) {
						if (data.getDataRegPK().getTv().getTime() == estmationTv.getTime()) {
							estmationCount++;
							find = true;
							break;
						}
					}
					if (!find) {
						dataList.add(data);
					}
				}
				if (dataList.size() < cycleCount - estmationCount)
					return null;						
			} 
			else if (dataSource.getSchemeType() == 3) {
				List<DataIntervalMonthly> datas = dataIntervalMonthlyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
				if (datas == null)
					return null;
				int estmationCount = 0;
				for (DataIntervalMonthly data : datas) {
					boolean find = false;
					for (Date estmationTv : refTvs) {
						if (data.getDataRegPK().getTv().getTime() == estmationTv.getTime()) {
							estmationCount++;
							find = true;
							break;
						}
					}
					if (!find) {
						dataList.add(data);
					}
				}
				if (dataList.size() < cycleCount - estmationCount)
					return null;					
			}
		break;			
		}
		return dataList;
	}		
}
