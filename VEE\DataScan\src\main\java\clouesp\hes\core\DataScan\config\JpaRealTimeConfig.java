package clouesp.hes.core.DataScan.config;

import java.util.HashMap;
import java.util.Map;

import javax.persistence.EntityManager;
import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateSettings;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "entityManagerFactoryRealTime",//配置连接工厂 entityManagerFactory
        transactionManagerRef = "transactionManagerRealTime", //配置 事物管理器  transactionManager		
		basePackages={
		"clouesp.hes.common.DataRepository.RealTime"
		})
public class JpaRealTimeConfig {

	@Autowired
	@Qualifier("realtimeDS")
	private DataSource dataSource;
	
    @Autowired
    private JpaProperties jpaProperties;
    
    @Value("${spring.jpa.hibernate.realtime-dialect}")
    private String realtimeDialect;
    
    @Autowired
    private HibernateProperties hibernateProperties;
    
    @Bean(name = "entityManagerRealTime")
    public EntityManager entityManager(EntityManagerFactoryBuilder builder) {
        return entityManagerFactoryRealTime(builder).getObject().createEntityManager();
    }
    
    @Bean(name = "entityManagerFactoryRealTime")
    public LocalContainerEntityManagerFactoryBean entityManagerFactoryRealTime(EntityManagerFactoryBuilder builder) {
        Map<String,String> map = new HashMap<>();
        map.put("hibernate.dialect", realtimeDialect);
        jpaProperties.setProperties(map);
    	Map<String, Object> properties = hibernateProperties.determineHibernateProperties(
    		       jpaProperties.getProperties(), new HibernateSettings());
    	 
        return builder
                .dataSource(dataSource)
                .properties(properties)
                .packages("clouesp.hes.common.DataEntity")
                .persistenceUnit("veeRealTimeUnit")
                .build();
    }

    @Bean(name = "transactionManagerRealTime")
    PlatformTransactionManager transactionManagerRealTime(EntityManagerFactoryBuilder builder) {
        return new JpaTransactionManager(entityManagerFactoryRealTime(builder).getObject());
    }   
  
}
