<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ValidRelayAccessMapper">

	<insert id="batchSaveMdmDataVeeEvent" parameterType="java.util.List" databaseId="oracle">
		merge into MDM_DATA_VEE_EVENT T1 USING (
		<foreach collection="list" item="eventObj" index="index" separator="UNION">
			select
			#{eventObj.dataVEEEventPK.tv, jdbcType=DATE} tv,
			#{eventObj.dataVEEEventPK.eventId, jdbcType=VARCHAR} event_id,
			#{eventObj.dataVEEEventPK.schemeType, jdbcType=VARCHAR} scheme_type,
			#{eventObj.dataVEEEventPK.objectId, jdbcType=VARCHAR} object_id,
			#{eventObj.updateTv, jdbcType=TIMESTAMP} update_tv,
			#{eventObj.objectType, jdbcType=NUMERIC} object_type,
			#{eventObj.veeEventClass, jdbcType=NUMERIC} vee_event_class,
			#{eventObj.dataType, jdbcType=NUMERIC} data_type,
			#{eventObj.estimationStatus, jdbcType=NUMERIC} estimation_status,
			#{eventObj.dataSource, jdbcType=NUMERIC} data_source
			from dual
		</foreach>
		) T2 ON (
		T1.tv = T2.tv
		and T1.event_id = T2.event_id
		and T1.scheme_type = T2.scheme_type
		and T1.object_id = T2.object_id
		)
		when not matched then
		insert (
		tv,
		event_id,
		scheme_type,
		object_id,
		update_tv,
		object_type,
		vee_event_class,
		data_type,
		estimation_status,
		data_source
		)
		values (
		t2.tv,
		t2.event_id,
		t2.scheme_type,
		t2.object_id,
		t2.update_tv,
		t2.object_type,
		t2.vee_event_class,
		t2.data_type,
		t2.estimation_status,
		t2.data_source
		)
		when matched then
		update
		set
		T1.update_tv = T2.update_tv,
		T1.object_type = T2.object_type,
		T1.vee_event_class = T2.vee_event_class,
		T1.data_type= T2.data_type,
		T1.estimation_status = T2.estimation_status,
		T1.data_source = T2.data_source
	</insert>

	<insert id="batchSaveMdmDataVeeEvent" parameterType="java.util.List" databaseId="mysql">
		replace into mdm_data_calc_obj (
		tv,
		event_id,
		scheme_type,
		object_id,
		update_tv,
		object_type,
		vee_event_class,
		data_type,
		estimation_status,
		data_source
		)
		values
		<foreach collection="list" item="eventObj" index="index" separator=",">
			(
			#{eventObj.dataVEEEventPK.tv, jdbcType=DATE} tv,
			#{eventObj.dataVEEEventPK.eventId, jdbcType=VARCHAR} event_id,
			#{eventObj.dataVEEEventPK.schemeType, jdbcType=VARCHAR} scheme_type,
			#{eventObj.dataVEEEventPK.objectId, jdbcType=VARCHAR} object_id,
			#{eventObj.updateTv, jdbcType=TIMESTAMP} update_tv,
			#{eventObj.objectType, jdbcType=NUMERIC} object_type,
			#{eventObj.veeEventClass, jdbcType=NUMERIC} vee_event_class,
			#{eventObj.dataType, jdbcType=NUMERIC} data_type,
			#{eventObj.estimationStatus, jdbcType=NUMERIC} estimation_status,
			#{eventObj.dataSource, jdbcType=NUMERIC} data_source
			)
		</foreach>
	</insert>



</mapper>