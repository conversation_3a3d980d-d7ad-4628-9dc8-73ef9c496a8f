package clouesp.hes.core.RealTimeDB;

import java.io.File;
import java.io.IOException;

import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.EnableScheduling;
 
@PropertySource(value={"file:${user.dir}/config/application.yml"}) 
@SpringBootApplication
@EnableAutoConfiguration
@EnableScheduling
public class RealTimeDbApplication {

	public static void main(String[] args) {
		try {
			File directory = new File("");// 参数为空
			String courseFile;
			courseFile = directory.getCanonicalPath();
			File parent = new File(courseFile);
			String parentPath = parent.getParent();
			String addClassPath = "spring.config.additional-location:classpath:/";
			addClassPath += "," +parentPath + "/config/";
			new SpringApplicationBuilder(RealTimeDbApplication.class).properties("spring.config.name:application", addClassPath).build().run(args);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

}
