<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="MeterGroupUpgradeMapper">

    <select id="getMeterGroupPlanList" resultType="com.clou.common.DataEntity.Entity.Store.hes.Data.DataParameterPlan"  parameterType="Request">
        select DPP.*,   AMG.NAME AS groupName,
        (SELECT COUNT(*) done FROM DATA_PARAMETER_JOB j WHERE j.PLAN_ID = DPP.ID AND j.STATE='2') as doneCount,
        (SELECT COUNT(*) expired FROM DATA_PARAMETER_JOB j WHERE j.PLAN_ID = DPP.ID AND j.STATE='3') as cancelCount,
        (SELECT COUNT(*) running FROM DATA_PARAMETER_JOB j WHERE j.PLAN_ID = DPP.ID AND j.STATE='1') as runningCount,
        (SELECT COUNT(*) running FROM DATA_PARAMETER_JOB j WHERE j.PLAN_ID = DPP.ID AND j.STATE='4') as waitingCount
        from DATA_PARAMETER_PLAN DPP
        LEFT JOIN ASSET_METER_GROUP AMG ON DPP.GROUP_ID = AMG.ID
        where 1=1
        <if test="entity">
            <if test="entity.deviceType != null and entity.deviceType != ''">
                and DPP.device_type = #{entity.deviceType}
            </if>

            <if test="entity.groupTypeId != null and entity.groupTypeId != ''">
                and DPP.group_type = #{entity.groupTypeId}
            </if>
            <if test="entity.groupTypeId == null or entity.groupTypeId == ''">
                and (DPP.group_type is null or DPP.group_type in (1,2,3,4,5))
            </if>
            <if test="entity.groupId != null and entity.groupId != ''">
                and DPP.group_id = #{entity.groupId}
            </if>

            <if test="entity.expired == 0">
                and DPP.expiry_time &gt; #{params.curTime}
            </if>
            <if test="entity.expired == 1">
                and DPP.expiry_time &lt;= #{params.curTime}
            </if>

        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <select id="getMeterGroupJobList" resultType="DataParameterJob"  parameterType="Request">
        select DPJ.*, AM.ID AS deviceId, AM.SN AS deviceSn, DDM.NAME AS modelName, DM.NAME AS manufacturerName
        from DATA_PARAMETER_JOB DPJ
          INNER JOIN ASSET_METER AM ON AM.ID = DPJ.DEVICE_ID
             LEFT JOIN DICT_DEVICE_MODEL DDM ON AM.MODEL = DDM.ID
        LEFT JOIN DICT_MANUFACTURER DM ON AM.MANUFACTURER = DM.ID

        where 1=1
        <if test="entity.planId !=null and entity.planId != ''">
            AND DPJ.PLAN_ID = #{entity.planId}
        </if>
        <if test="entity.state !=null and entity.state != ''">
            AND DPJ.STATE = #{entity.state}
        </if>

        <if test="entity.deviceSn !=null and entity.deviceSn != ''">
            AND AM.SN LIKE CONCAT(CONCAT('%', #{entity.deviceSn}),'%')
        </if>


        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <select id="getDcuGroupJobList" resultType="DataParameterJob"  parameterType="Request">
        select DPJ.*, AM.ID AS deviceId, AM.SN AS deviceSn, DDM.NAME AS modelName, DM.NAME AS manufacturerName
        from DATA_PARAMETER_JOB DPJ
        INNER JOIN asset_communicator AM ON AM.ID = DPJ.DEVICE_ID
        LEFT JOIN DICT_DEVICE_MODEL DDM ON AM.MODEL = DDM.ID
        LEFT JOIN DICT_MANUFACTURER DM ON AM.MANUFACTURER = DM.ID

        where 1=1
        <if test="entity.planId !=null and entity.planId != ''">
            AND DPJ.PLAN_ID = #{entity.planId}
        </if>
        <if test="entity.state !=null and entity.state != ''">
            AND DPJ.STATE = #{entity.state}
        </if>

        <if test="entity.deviceSn !=null and entity.deviceSn != ''">
            AND AM.SN LIKE CONCAT(CONCAT('%', #{entity.deviceSn}),'%')
        </if>

        <if test="entity.deviceType !=null and entity.deviceType != ''">
            AND AM.device_type = #{entity.deviceType}
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <select id="getDcuSearchDeviceList" resultType="com.clou.common.DataEntity.Model.Upgrade.DcuGroupDeviceResult"  parameterType="Request">
        select ac.sn as deviceSn , ddm.name as modelName , dm.name as manufacturerName , dt.name as deviceTypeName from asset_communicator ac  LEFT JOIN DICT_DEVICE_MODEL DDM ON ac.MODEL = DDM.ID
        LEFT JOIN DICT_MANUFACTURER DM ON ac.MANUFACTURER = DM.ID LEFT JOIN DICT_DEVICE_TYPE dt on ac.DEVICE_TYPE= dt.id
        where  (ac.device_type = 202 or ac.device_type = 203)

        <if test="entity.deviceSn !=null and entity.deviceSn != ''">
            AND ac.SN LIKE CONCAT(CONCAT('%', #{entity.deviceSn}),'%')
        </if>

        <if test="entity.deviceType !=null and entity.deviceType != ''">
            AND ac.device_type = #{entity.deviceType}
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

</mapper>