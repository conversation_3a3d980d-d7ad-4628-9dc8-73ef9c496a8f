package clouesp.hes.core.DataScan.config;

import clouesp.hes.common.DataEntity.System.MdmSysService;
import clouesp.hes.common.DataRepository.RealTime.System.RtMdmSysServiceAttributeRepository;
import clouesp.hes.common.DataRepository.RealTime.System.RtMdmSysServiceRepository;
import clouesp.hes.common.logger.logger.Logger;
import clouesp.hes.common.logger.logger.LoggerLevel;
import jline.internal.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.context.WebServerInitializedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.InetAddress;
import java.sql.Connection;
import java.util.List;

@Component
public class ServerConfig implements ApplicationListener<WebServerInitializedEvent>{
	private int serverPort;
	private String serviceId;
	private String namesrvAddr;


	private boolean isOracleDb = false; //目前只两种数据库，不是oracle就是 mysql ,所以用布尔型判断更容易
	
	@Autowired
	private RtMdmSysServiceRepository rtMdmSysServiceRepository;	
	
	@Autowired
	private RtMdmSysServiceAttributeRepository rtMdmSysServiceAttributeRepository;

	@Resource(name = "persistenceJdbcTemplate")
	private JdbcTemplate jdbcTemplate;

	@Override
	public void onApplicationEvent(WebServerInitializedEvent event) {
		// TODO Auto-generated method stub
		serverPort = event.getWebServer().getPort();
	}


	public boolean isOracleDb() {
		return isOracleDb;
	}
	
	public void loadCfg() {
		List<MdmSysService> mdmSysServices = rtMdmSysServiceRepository.findByServiceType(10);
		if (mdmSysServices == null || mdmSysServices.size() == 0) {
			String logInfo = "Not find Rocket MQ service!";
			Log.info(logInfo);
			Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, getServiceId(), "0", "System", logInfo); 
			return;
		}
		MdmSysService mdmSysService = mdmSysServices.get(0);
		String namesrvIp = rtMdmSysServiceAttributeRepository.findValue(mdmSysService.getId(), "RocketMQ.IP");
		String namesrvPort = rtMdmSysServiceAttributeRepository.findValue(mdmSysService.getId(), "RocketMQ.Port");	
		setNamesrvAddr(namesrvIp + ":" + namesrvPort);

		Connection con = null;
		try {
			con = jdbcTemplate.getDataSource().getConnection();
			String url = con.getMetaData().getURL();
			if ( url == null || url.isEmpty()  ){
				String logInfo = "Not find Db URL ";
				Log.info(logInfo);
				Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, getServiceId(), "0", "System", logInfo);
			}else {
				url = url.toLowerCase();
				if ( url.contains("oracle")){
					isOracleDb = true ;
				}else if ( url.contains("mysql")){
					     isOracleDb = false ;
				     }else {
								String logInfo = "Db URL error :  " + url ;
								Log.info(logInfo);
								Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, getServiceId(), "0", "System", logInfo);

				            }
			}

		}catch (Exception e) {
				e.printStackTrace();
			    Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, getServiceId(), "0", "System", e.getMessage());
		} finally {
			try {
				if (con != null) {
					con.close();
				}
			} catch (Exception e) {
				Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, getServiceId(), "0", "System", e.getMessage());
				e.printStackTrace();
			}
		}

	}	

	public String getServerAddress() {
		InetAddress address = null;
        try {
            address = InetAddress.getLocalHost();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return address.getHostAddress();
	}

	public int getServerPort() {
		return serverPort;
	}
	
	public String getServiceId() {
		return serviceId;
	}

	public void setServiceId(String serviceId) {
		this.serviceId = serviceId;
	}

	public String getNamesrvAddr() {
		return namesrvAddr;
	}

	public void setNamesrvAddr(String namesrvAddr) {
		this.namesrvAddr = namesrvAddr;
	}	

}
