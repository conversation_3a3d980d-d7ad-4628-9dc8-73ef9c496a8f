import clouesp.hes.common.CommonUtils.ObjectUtils
import clouesp.hes.common.DataEntity.Data.DataReg
import clouesp.hes.common.DataEntity.Data.DataVEEEvent

def regTotalSubFeeUnequal(Map<String, Object> ruleInfo) {
    Integer result = 0;
    DataVEEEvent event = null;
    if (ruleInfo == null ||
            !ruleInfo.containsKey("datas") ||
            !ruleInfo.containsKey("params")) {
        return null;
    }

    List<Map<String, Object>> ruleDatas = ruleInfo.get("datas");
    Map<String, Object> ruleParams = ruleInfo.get("params");
    double paramValue = 0;
    if(ruleParams != null && ruleParams.containsKey("paramValue")) {
        paramValue = ruleParams.get("paramValue");
    }

    for(Map<String, Object> ruleData : ruleDatas) {
        String targetClass = ruleData.get("targetClass");
        //List<Map<String, Object>> dataList = ruleData.get("dataListMonth");
        //获取数据，根据数据周期取值
        String dataKey = "dataListDay";
        Integer schemeType = new Integer(ruleInfo.get("schemeType").toString());
        if(schemeType == 1)
        {
            dataKey = "dataListMinute";
        }
        else if(schemeType == 2)
        {
            dataKey = "dataListDay";
        }
        else if(schemeType == 3)
        {
            dataKey = "dataListMonth";
        }
        List<Map<String, Object>> dataList = ruleData.get(dataKey);
        if(dataList == null) {
            return null;
        }
        if("REG" == targetClass) {
            if(dataList.size() > 0) {
                Map<String, Object> data = dataList.get(0);
                DataReg dataReg = ObjectUtils.convertMapToObject(data, DataReg.class);
                Double r0p1a1 = dataReg.getR0P1A1();
                Double r1p1a1 = dataReg.getR1P1A1();
                Double r2p1a1 = dataReg.getR2P1A1();
                Double r3p1a1 = dataReg.getR3P1A1();
                Double r4p1a1 = dataReg.getR4P1A1();
                if(r0p1a1 != null &&
                        r1p1a1 != null &&
                        r2p1a1 != null &&
                        r3p1a1 != null &&
                        r4p1a1 != null) {

                    BigDecimal num1 = new BigDecimal(r0p1a1.doubleValue());
                    num1 = num1.setScale(2, BigDecimal.ROUND_HALF_UP);

                    BigDecimal num2 = new BigDecimal((r1p1a1.doubleValue() +
                            r2p1a1.doubleValue() +
                            r3p1a1.doubleValue() +
                            r4p1a1.doubleValue()));
                    num2 = num2.setScale(2, BigDecimal.ROUND_HALF_UP);

                    if(Math.abs(num1 -
                            num2) > paramValue) {
                        result = 1;
                    }
                }
            }
        }
    }

    if(result.intValue() == 1) {
        event = (DataVEEEvent) ruleInfo.get("event");
    }
    return event;
}