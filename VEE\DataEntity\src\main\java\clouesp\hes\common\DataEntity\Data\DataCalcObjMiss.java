package clouesp.hes.common.DataEntity.Data;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.IdClass;
import javax.persistence.Table;

@Data
@Entity
@Table(name= "mdm_data_calc_obj_miss")
@IdClass(DataCalcObjMiss.class)
public class DataCalcObjMiss implements Serializable{

	@Id
	@Column(name = "calc_obj_id", nullable = false, columnDefinition = "varchar(32)")
	private String calcObjId;
	
	@Id
	@Column(name = "tv")
	private Date tv;
	
	@Id
	@Column(name = "scheme_id", columnDefinition = "varchar(32)")
	private String schemeId;
	

	@Id
	@Column(name = "sdp_id", columnDefinition = "varchar(32)")
	private String sdpId;

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		DataCalcObjMiss that = (DataCalcObjMiss) o;
		return Objects.equals(calcObjId, that.calcObjId) &&
				Objects.equals(tv, that.tv) &&
				Objects.equals(schemeId, that.schemeId) &&
				Objects.equals(sdpId, that.sdpId);
	}

	@Override
	public int hashCode() {
		return Objects.hash(sdpId, tv, schemeId, sdpId);
	}

}
