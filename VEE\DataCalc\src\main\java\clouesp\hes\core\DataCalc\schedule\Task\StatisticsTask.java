package clouesp.hes.core.DataCalc.schedule.Task;

import clouesp.hes.common.logger.logger.Logger;
import clouesp.hes.common.logger.logger.LoggerLevel;
import clouesp.hes.core.DataCalc.config.ServerConfig;
import clouesp.hes.core.DataCalc.service.Statistics.DashboardDataStatistics;
import jline.internal.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.text.SimpleDateFormat;

@Component("statisticsTask")
public class StatisticsTask {
    @Autowired
    private ServerConfig serverConfig;

    @Autowired
    private DashboardDataStatistics dashboardDataStatistics ;

    private boolean executing = false;

    private SimpleDateFormat sdflog = new SimpleDateFormat("MM-dd HH:mm:ss");

    public void init(String serviceId) {
        dashboardDataStatistics.Init();
        Log.info("StatisticsTask init finish ");
    }

    public void dispatch() {
        String logInfo = null;
        if (executing) {
            return;
        }
        executing = true;

        logInfo = "Start Statistics Object(Task)";
        Log.info(logInfo);
        Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "Calculation", logInfo);

        dashboardDataStatistics.execute();
        executing = false;
        logInfo = "End Statistics Object";
        Log.info(logInfo);
        Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "Calculation", logInfo);
    }
}
