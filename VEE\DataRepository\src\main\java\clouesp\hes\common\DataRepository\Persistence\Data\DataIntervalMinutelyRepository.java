package clouesp.hes.common.DataRepository.Persistence.Data;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;

import clouesp.hes.common.DataEntity.Data.DataIntervalMinutely;
import clouesp.hes.common.DataEntity.Data.DataRegPK;

public interface DataIntervalMinutelyRepository extends JpaRepository<DataIntervalMinutely, DataRegPK>{
	List<DataIntervalMinutely> findByDataRegPKSdpIdAndDataRegPKTvIn(
			String sdpId, 
			List<Date> tvs
			);
	
	List<DataIntervalMinutely> findByDataRegPKSdpIdAndDataRegPKTvBetween(
			String sdpId, 
			Date startTv,
			Date endTv
			);		
}
