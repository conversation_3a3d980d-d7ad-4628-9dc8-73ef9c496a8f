package clouesp.hes.core.DataCalc.handler.Calculation;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;

import clouesp.hes.common.CommonUtils.ObjectUtils;
import clouesp.hes.common.CommonUtils.StringUtils;
import clouesp.hes.common.DataEntity.Asset.MdmAssetMeterReplacement;
import clouesp.hes.common.DataEntity.Data.*;
import clouesp.hes.common.DataRepository.Persistence.Data.*;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetMeterReplacementRepository;
import clouesp.hes.common.DataRepository.RealTime.Data.*;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;

import clouesp.hes.common.DataEntity.Asset.MdmAssetCalcScheme;
import clouesp.hes.common.DataEntity.Asset.MdmAssetServicePoint;
import clouesp.hes.common.DataEntity.Dict.DictDetail;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetCalcSchemeRepository;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetServicePointRepository;
import clouesp.hes.common.DataRepository.RealTime.Dict.RtDictDetailRepository;
import clouesp.hes.common.MqBus.MQMsg;
import clouesp.hes.common.logger.logger.Logger;
import clouesp.hes.common.logger.logger.LoggerLevel;
import clouesp.hes.core.DataCalc.handler.BaseHandler;
import clouesp.hes.core.DataCalc.service.Data.DataAccessService;
import clouesp.hes.core.DataCalc.service.Data.RtDataAccessService;
import clouesp.hes.core.DataCalc.Utils.SpringBeanUtils;
import clouesp.hes.core.DataCalc.config.ServerConfig;
import jline.internal.Log;

public class CalcEnergyHandler implements BaseHandler {
    private RtMdmAssetCalcSchemeRepository rtMdmAssetCalcSchemeRepository = SpringBeanUtils.getBean(RtMdmAssetCalcSchemeRepository.class);
    private RtMdmAssetServicePointRepository rtMdmAssetServicePointRepository = SpringBeanUtils.getBean(RtMdmAssetServicePointRepository.class);

    private DataRegMinutelyRepository dataRegMinutelyRepository = SpringBeanUtils.getBean(DataRegMinutelyRepository.class);
    private DataRegDailyRepository dataRegDailyRepository = SpringBeanUtils.getBean(DataRegDailyRepository.class);
    private DataRegMonthlyRepository dataRegMonthlyRepository = SpringBeanUtils.getBean(DataRegMonthlyRepository.class);


    private RtDataRegMinutelyRepository rtDataRegMinutelyRepository = SpringBeanUtils.getBean(RtDataRegMinutelyRepository.class);
    private RtDataRegDailyRepository rtDataRegDailyRepository = SpringBeanUtils.getBean(RtDataRegDailyRepository.class);
    private RtDataRegMonthlyRepository rtDataRegMonthlyRepository = SpringBeanUtils.getBean(RtDataRegMonthlyRepository.class);


    private DataAccessService dataAccessService = SpringBeanUtils.getBean(DataAccessService.class);
    private RtDataAccessService rtDataAccessService = SpringBeanUtils.getBean(RtDataAccessService.class);

    private RtDictDetailRepository rtDictDetailRepository = SpringBeanUtils.getBean(RtDictDetailRepository.class);
    private ServerConfig serverConfig = SpringBeanUtils.getBean(ServerConfig.class);

    private DataCalcProgressRepository dataCalcProgressRepository = SpringBeanUtils.getBean(DataCalcProgressRepository.class);

    private RtMdmAssetMeterReplacementRepository rtMdmAssetMeterReplacementRepository = SpringBeanUtils.getBean(RtMdmAssetMeterReplacementRepository.class);

    private DataSaveProc dataSaveProc = null;
    private Thread dataSaveProcThread = null;

    private long lastSaveCalcProgressTime = System.currentTimeMillis();
    private long lastSaveMinutelyTime = System.currentTimeMillis();
    private long lastSaveDailyTime = System.currentTimeMillis();
    private long lastSaveMonthlyTime = System.currentTimeMillis();

    private LinkedBlockingQueue<DataEnergy> dataEnergyMinutelyQueue = new LinkedBlockingQueue<DataEnergy>();
    private LinkedBlockingQueue<DataEnergy> dataEnergyDailyQueue = new LinkedBlockingQueue<DataEnergy>();
    private LinkedBlockingQueue<DataEnergy> dataEnergyMonthlyQueue = new LinkedBlockingQueue<DataEnergy>();

    private LinkedBlockingQueue<DataCalcProgress> dataCalcProgressQueue = new LinkedBlockingQueue<>();

    private HandlerProc handlerProc = null;
    private Thread handlerProcThread = null;
    private LinkedBlockingQueue<DataReg> dataRegQueue = new LinkedBlockingQueue<DataReg>();

    private SimpleDateFormat sdflog = new SimpleDateFormat("MM-dd HH:mm:ss");
    private Map<String, Date> mapDataCalcProgress = new ConcurrentHashMap<>();

    @Override
    public void init(String serviceId) {
        Log.info("CalcEnergyHandler init");
        List<DataCalcProgress> calcProgressList = dataCalcProgressRepository.findAll();
        mapDataCalcProgress.clear();
        for (DataCalcProgress calcProgress : calcProgressList) {
            String key = calcProgress.getSdpId();
            key += "#";
            key += calcProgress.getSchemeId();
            key += "#";
            key += calcProgress.getLoadType();
            mapDataCalcProgress.put(key, calcProgress.getTv());
        }
        if (handlerProcThread == null) {
            Log.info("handlerProc start");
            handlerProc = new HandlerProc();
            handlerProcThread = new Thread(handlerProc);
            handlerProcThread.start();
        }
        if (dataSaveProcThread == null) {
            Log.info("dataSaveProc start");
            dataSaveProc = new DataSaveProc();
            dataSaveProcThread = new Thread(dataSaveProc);
            dataSaveProcThread.start();
        }
    }

    @Override
    public <T> void handler(MQMsg<T> mqMsg) {
        if (mqMsg == null)
            return;
        if (mqMsg.getLoad() == null)
            return;
        String jsonStr = mqMsg.getLoad().toString();
        try {
            if ("Register".equals(mqMsg.getLoadType())) {
                DataReg data = JSONObject.parseObject(jsonStr, new TypeReference<DataReg>() {
                });
                dataRegQueue.put(data);
            }
        } catch (InterruptedException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    private class HandlerProc implements Runnable {
        private DataReg getLastDataReg(String objectId, MdmAssetCalcScheme scheme, Date refTv) {
            int field = Calendar.MINUTE;
            if (scheme.getType() == 2) {
                field = Calendar.DAY_OF_MONTH;
            } else if (scheme.getType() == 3) {
                field = Calendar.MONTH;
            }
            int amount = scheme.getCycleNum();
            Calendar cal = Calendar.getInstance();
            cal.setTime(refTv);
            if (scheme.getType() == 1) {
                int minute = cal.get(Calendar.MINUTE);
                if ((minute % amount) != 0)
                    return null;
            }
            cal.add(field, -amount);
            List<Date> tvs = new ArrayList<Date>();
            tvs.add(cal.getTime());
            if (scheme.getType() == 2) {
                List<DataRegDaily> datas = rtDataRegDailyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
                if (datas != null && datas.size() > 0) {
                    return datas.get(0);
                }
                datas = dataRegDailyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
                if (datas != null && datas.size() > 0) {
                    try {
                        rtDataRegDailyRepository.save(datas.get(0));
                    } catch (Exception e) {

                    }
                    return datas.get(0);
                }
            } else if (scheme.getType() == 3) {
                List<DataRegMonthly> datas = rtDataRegMonthlyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
                if (datas != null && datas.size() > 0) {
                    return datas.get(0);
                }
                datas = dataRegMonthlyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
                if (datas != null && datas.size() > 0) {
                    try {
                        rtDataRegMonthlyRepository.save(datas.get(0));
                    } catch (Exception e) {

                    }
                    return datas.get(0);
                }
            } else if (scheme.getType() == 1) {
                List<DataRegMinutely> datas = rtDataRegMinutelyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
                if (datas != null && datas.size() > 0) {
                    return datas.get(0);
                }
                datas = dataRegMinutelyRepository.findByDataRegPKSdpIdAndDataRegPKTvIn(objectId, tvs);
                if (datas != null && datas.size() > 0) {
                    try {
                        rtDataRegMinutelyRepository.save(datas.get(0));
                    } catch (Exception e) {

                    }
                    return datas.get(0);
                }
            }

            return null;
        }

        private void calcEnergyReg(Double pt, Double ct, DataReg curData, DataReg lastData, MdmAssetCalcScheme scheme) {
            try {
                DataEnergy data = new DataEnergy();
                data.getDataEnergyPK().setSdpId(lastData.getDataRegPK().getSdpId());
                data.getDataEnergyPK().setTv(lastData.getDataRegPK().getTv());
                data.getDataEnergyPK().setSchemeId(scheme.getId());
                data.setUpdateTv(new Date());

                Class cls = DataReg.class;
                Class clsDataEnergy = DataEnergy.class;

                Field[] fields = cls.getDeclaredFields();

                //Class<?> clsMeterReplacement = MdmAssetMeterReplacement.class;

                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String sdpId = curData.getDataRegPK().getSdpId();
                //这里调整一下，通过当前寄存器中的日期，到现在这个时间段，传入到计算函数
                //计算函数或根据这段时间查找电表的replace最近的一条，然后根据是只换cptp还是换了电表进行判断
                //1:Installation Meter
                //2:Replace Meter and CT 更换表及cptp
                //3:Remove Meter
                //4:Replace PT and CT 只更换cptp
                //String startTv = sdf.format(lastData.getDataRegPK().getTv());
                //String endTv = sdf.format(curData.getDataRegPK().getTv());

                Date dateStartTv = lastData.getDataRegPK().getTv();
                Date dateEndTv = curData.getDataRegPK().getTv();

                List<Integer> operationTypeList = new ArrayList<>();
                operationTypeList.add(1);
                operationTypeList.add(2);
                operationTypeList.add(3);
                operationTypeList.add(4);
                List<Integer> processingTypeList = new ArrayList<>();
                processingTypeList.add(1);


                //获取指定时段的变更记录
                List<MdmAssetMeterReplacement> meterReplacementList = null;


                meterReplacementList = rtMdmAssetMeterReplacementRepository.getMeterReplacement(
                        sdpId, dateStartTv, dateEndTv, operationTypeList, processingTypeList);

                for (Field field : fields) {

                    String name = field.getName();
                    if (!name.startsWith("R")) {
                        continue;
                    }
                    String energyFieldName = ObjectUtils.getFieldName(data, name);
                    if (energyFieldName == null) {
                        continue;
                    }

                    field.setAccessible(true);
                    try {
                        String typeName = field.getType().getName();
                        if (!"java.lang.Double".equals(typeName)) {
                            continue;
                        }
                        Object curObj = field.get(curData);
                        Object lastObj = field.get(lastData);
                        if (curObj == null || lastObj == null) {
                            continue;
                        }
                        double energyValue = 0;
                        double lastEnergyValue = 0;
                        double currEnergyValue = 0;


                        if(meterReplacementList == null || meterReplacementList.size() == 0)
                        {
                            //查找准确的ctpt
                            List<MdmAssetMeterReplacement> tmpMeterReplacementList = rtMdmAssetMeterReplacementRepository.getMeterReplacementInfo(
                                    sdpId, dateEndTv, operationTypeList, processingTypeList);
                            if(tmpMeterReplacementList != null && tmpMeterReplacementList.size() > 0)
                            {
                               int tmpctid = tmpMeterReplacementList.get(0).getOldCt();
                               int tmpptid = tmpMeterReplacementList.get(0).getOldPt();

                               //获取缩放值
                                Double scalect = getScaleValue("1102", tmpctid);
                                if(scalect != null) {
                                    ct = scalect.doubleValue();
                                }

                                Double scalept = getScaleValue("1101", tmpptid);
                                if(scalept != null) {
                                    pt = scalept.doubleValue();
                                }

                            }
                            //在本次时段内都没有更换记录，用传入的ctpt做计算
                            energyValue = ((double) curObj - (double) lastObj) * ct * pt;


                        }
                        else
                        {


                            //如果存在更换记录，循环所有更换记录，然后分段计算各个段的电能量再累加

                            String oldFieldName = StringUtils.lineToHump("old_" + energyFieldName.toLowerCase());
                            String newFieldName = StringUtils.lineToHump("new_" + energyFieldName.toLowerCase());

                            //最开始一段
                            Class<?> tmpMeterReplacement = meterReplacementList.get(0).getClass();


                            Field oldField = null;
                            try
                            {
                                 oldField = tmpMeterReplacement.getDeclaredField(oldFieldName);
                            }
                            catch (Exception ex)
                            {

                            }
                            if(oldField == null)
                            {
                                continue;
                            }


                            oldField.setAccessible(true);


                            //得到填写的老的值
                            Object oldFieldValue = oldField.get(meterReplacementList.get(0));

                            //任意一个没填写值，跳过此属性
                            if(oldFieldValue != null)
                            {
                                int tmpctid = meterReplacementList.get(0).getOldCt();
                                int tmpptid = meterReplacementList.get(0).getOldPt();

                                Double tmpct =0d;
                                Double tmppt =0d;
                                //缩放值
                                Double scalect = getScaleValue("1102", tmpctid);
                                if(scalect != null) {
                                    tmpct = scalect.doubleValue();
                                }

                                Double scalept = getScaleValue("1101", tmpptid);
                                if(scalept != null) {
                                    tmppt = scalept.doubleValue();
                                }

                                energyValue = energyValue + ((double) oldFieldValue - (double) lastObj) *tmpct*tmppt;
                            }

                            //中间段
                            if(meterReplacementList.size() >= 2)
                            {
                                //用后一个点的老码值-上一个点的新表码值，就是当前这个段的实际表码差值
                                for(int i=0; i < meterReplacementList.size(); i++)
                                {
                                    if(i+1 == meterReplacementList.size())
                                    {
                                        break;

                                    }
                                    Class<?> tmpMeterReplacement_i = meterReplacementList.get(i).getClass();
                                    Class<?> tmpMeterReplacement_i_1 = meterReplacementList.get(i+1).getClass();

                                    Field oldField_i_1 = tmpMeterReplacement_i_1.getDeclaredField(oldFieldName);
                                    Field newField_i = tmpMeterReplacement_i.getDeclaredField(newFieldName);

                                    oldField_i_1.setAccessible(true);
                                    newField_i.setAccessible(true);

                                    //得到后一个更换点老的值
                                    Object oldFieldValue_i_1 = oldField_i_1.get(meterReplacementList.get(i+1));

                                    //得到当前点新填写的值
                                    Object newFieldValue_i = newField_i.get(meterReplacementList.get(i));

                                    //任意一个没填写值，跳过此属性
                                    if(oldFieldValue_i_1 != null
                                            && newFieldValue_i != null)
                                    {

                                        int tmpctid = meterReplacementList.get(i+1).getOldCt();
                                        int tmpptid = meterReplacementList.get(i+1).getOldPt();

                                        Double tmpct =0d;
                                        Double tmppt =0d;
                                        //缩放值
                                        Double scalect = getScaleValue("1102", tmpctid);
                                        if(scalect != null) {
                                            tmpct = scalect.doubleValue();
                                        }

                                        Double scalept = getScaleValue("1101", tmpptid);
                                        if(scalept != null) {
                                            tmppt = scalept.doubleValue();
                                        }

                                        energyValue = energyValue + ((double) oldFieldValue_i_1 - (double) newFieldValue_i) *tmpct*tmppt;
                                    }

                                }


                            }

                            //最后一段
                            tmpMeterReplacement = meterReplacementList.get(meterReplacementList.size()-1).getClass();

                            Field newField = tmpMeterReplacement.getDeclaredField(newFieldName);
                            newField.setAccessible(true);

                            Object newFieldValue = newField.get(meterReplacementList.get(meterReplacementList.size()-1));
                            if(newFieldValue != null)
                            {
                                int tmpctid = meterReplacementList.get(meterReplacementList.size()-1).getNewCt();
                                int tmpptid = meterReplacementList.get(meterReplacementList.size()-1).getNewPt();

                                Double tmpct =0d;
                                Double tmppt =0d;
                                //缩放值
                                Double scalect = getScaleValue("1102", tmpctid);
                                if(scalect != null) {
                                    tmpct = scalect.doubleValue();
                                }

                                Double scalept = getScaleValue("1101", tmpptid);
                                if(scalept != null) {
                                    tmppt = scalept.doubleValue();
                                }

                                energyValue = energyValue + ((double) curObj - (double) newFieldValue) *tmpct*tmppt;
                            }

                        }

                        //放入电能量相应字段
                        Field energyField = clsDataEnergy.getDeclaredField(name);
                        energyField.setAccessible(true);
                        energyField.set(data,energyValue);


                        /*

                        boolean isMeterReplacement = false;
                        if (meterReplacementList.size() > 0) {
                            try {

                                //取最近一条
                                MdmAssetMeterReplacement meterReplacement = meterReplacementList.get(0);


                                    String oldFieldName = StringUtils.lineToHump("old_" + energyFieldName.toLowerCase());
                                    String newFieldName = StringUtils.lineToHump("new_" + energyFieldName.toLowerCase());
                                    Field oldField = clsMeterReplacement.getDeclaredField(oldFieldName);
                                    Field newField = clsMeterReplacement.getDeclaredField(newFieldName);

                                    Field oldCtField = clsMeterReplacement.getDeclaredField("oldCt");
                                    Field oldPtField = clsMeterReplacement.getDeclaredField("oldPt");
                                    Field newCtField = clsMeterReplacement.getDeclaredField("newCt");
                                    Field newPtField = clsMeterReplacement.getDeclaredField("newPt");

                                    if (oldField != null && newField != null) {
                                        oldField.setAccessible(true);
                                        newField.setAccessible(true);
                                        if (oldCtField != null) {
                                            oldCtField.setAccessible(true);
                                        }
                                        if (oldPtField != null) {
                                            oldPtField.setAccessible(true);
                                        }
                                        if (newCtField != null) {
                                            newCtField.setAccessible(true);
                                        }
                                        if (newPtField != null) {
                                            newPtField.setAccessible(true);
                                        }
                                        Object oldObj = oldField.get(meterReplacement);
                                        Object newObj = newField.get(meterReplacement);

                                        if(oldObj != null || newObj != null) {

                                            if(newObj == null && oldObj != null) {
                                                newObj = oldObj;
                                            }
                                            if(oldObj == null && newObj != null) {
                                                oldObj = newObj;
                                            }

                                            Object oldCtObj = null;
                                            Object newCtObj = null;
                                            Object oldPtObj = null;
                                            Object newPtObj = null;

                                            if (oldCtField != null) {
                                                oldCtObj = oldCtField.get(meterReplacement);
                                            }
                                            if (oldPtField != null) {
                                                oldPtObj = oldPtField.get(meterReplacement);
                                            }
                                            if (newCtField != null) {
                                                newCtObj = newCtField.get(meterReplacement);
                                            }
                                            if (newPtField != null) {
                                                newPtObj = newPtField.get(meterReplacement);
                                            }
                                            if(oldCtObj == null && newCtObj != null) {
                                                oldCtObj = newCtObj;
                                            }
                                            if(newCtObj == null && oldCtObj != null) {
                                                newCtObj = oldCtObj;
                                            }
                                            if(oldPtObj == null && newPtObj != null) {
                                                oldPtObj = newPtObj;
                                            }
                                            if(newPtObj == null && oldPtObj != null) {
                                                newPtObj = oldPtObj;
                                            }

                                            double oldCt = ct;
                                            double oldPt = ct;
                                            double newCt = pt;
                                            double newPt = pt;

                                            if (oldCtObj != null) {
                                                int oldCtId = (int) oldCtObj;
                                                Double scale = getScaleValue("1102", oldCtId);
                                                if(scale != null) {
                                                    oldCt = scale.doubleValue();
                                                }
                                            }
                                            if (oldPtObj != null) {
                                                int oldPtId = (int) oldPtObj;
                                                Double scale = getScaleValue("1101", oldPtId);
                                                if(scale != null) {
                                                    oldPt = scale.doubleValue();
                                                }
                                            }
                                            if (newCtObj != null) {
                                                int newCtId = (int) newCtObj;
                                                Double scale = getScaleValue("1102", newCtId);
                                                if(scale != null) {
                                                    newCt = scale.doubleValue();
                                                }
                                            }
                                            if (newPtObj != null) {
                                                int newPtId = (int) newPtObj;
                                                Double scale = getScaleValue("1101", newPtId);
                                                if(scale != null) {
                                                    newPt = scale.doubleValue();
                                                }
                                            }

                                            if (oldObj != null) {
                                                energyValue = (((double) oldObj - (double) lastObj) * oldCt * oldPt);
                                            }
                                            if (newObj != null) {
                                                energyValue += (((double) curObj - (double) newObj) * newCt * newPt);
                                            }
                                            if(energyValue >= 0) {
                                                isMeterReplacement = true;
                                            }
                                        }
                                    }

                            } catch (Exception e) {

                            }
                        }
						if(!isMeterReplacement) {
							energyValue = ((double) curObj - (double) lastObj) * ct * pt;
						}

                        ObjectUtils.invokeSet(data, energyFieldName, energyValue + "");

                         */

                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }


                data.setDataSource(0);
                data.setDataVersion(0);

                String key = data.getDataEnergyPK().getSdpId();
                key += "#";
                key += data.getDataEnergyPK().getSchemeId();
                key += "#";
                key += "Register";
                boolean updateProgress = true;
                if (mapDataCalcProgress.containsKey(key)) {
                    Date lastTv = mapDataCalcProgress.get(key);
                    if (lastTv.getTime() >= data.getDataEnergyPK().getTv().getTime()) {
                        updateProgress = false;
                    }
                }
                if (updateProgress) {
                    DataCalcProgress dataCalcProgress = new DataCalcProgress();
                    dataCalcProgress.setSdpId(data.getDataEnergyPK().getSdpId());
                    dataCalcProgress.setSchemeId(data.getDataEnergyPK().getSchemeId());
                    dataCalcProgress.setTv(data.getDataEnergyPK().getTv());
                    dataCalcProgress.setUpdateTv(new Date());
                    dataCalcProgress.setLoadType("Register");
                    dataCalcProgressQueue.put(dataCalcProgress);
                    mapDataCalcProgress.put(key, dataCalcProgress.getTv());
                }

                if (scheme.getType() == 2) {
                    dataEnergyDailyQueue.put(data);
                } else if (scheme.getType() == 3) {
                    dataEnergyMonthlyQueue.put(data);
                } else if (scheme.getType() == 1) {
                    dataEnergyMinutelyQueue.put(data);
                }


            } catch (InterruptedException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }

        private Double getScaleValue(String dictId, int innerValue) {
            try {
                DictDetail dictDetail = rtDictDetailRepository.findByDictIdAndInnerValue(dictId, innerValue);
                if(dictDetail == null) {
                    return null;
                }
                String name =  dictDetail.getGuiDisplayName();
                if (name != null) {
                    String[] splitName = name.split("/");
                    if (splitName.length == 2) {
                        return Double.parseDouble(splitName[0]) / Double.parseDouble(splitName[1]);
                    }
                }
            } catch (Exception e) {

            }
            return null;
        }

        private int handleReg(int count) {
            if (dataRegQueue.size() == 0)
                return 0;
            if (count > dataRegQueue.size())
                count = dataRegQueue.size();
            List<DataReg> dataRegs = new ArrayList<DataReg>();
            dataRegQueue.drainTo(dataRegs, count);

            Log.info("handleReg Count: " + dataRegs.size());

            for (DataReg data : dataRegs) {
                if (data.getDataRegPK().getSdpId() == null) {
                    continue;
                }

                Optional<MdmAssetServicePoint> OptionalSdp = rtMdmAssetServicePointRepository.findById(data.getDataRegPK().getSdpId());
                if (!OptionalSdp.isPresent()) {
                    continue;
                }
                MdmAssetServicePoint sdp = OptionalSdp.get();
                String schemeIds = sdp.getSchemeIds();
                if (schemeIds == null) {
                    continue;
                }

                double pt = 1d;

                Double ptValue = getScaleValue("1101", sdp.getPt());
                if(ptValue != null) {
                    pt = ptValue.doubleValue();
                }

                double ct = 1d;
                Double ctValue = getScaleValue("1102", sdp.getCt());
                if(ctValue != null) {
                    ct = ctValue.doubleValue();
                }

                String[] schemeIdSplit = schemeIds.split(",");
                List<String> listSchemeId = Arrays.asList(schemeIdSplit);
                List<MdmAssetCalcScheme> schemes = rtMdmAssetCalcSchemeRepository.findAllById(listSchemeId);
                if (schemes == null || schemes.size() == 0)
                    continue;
                MdmAssetCalcScheme curScheme = null;
                if ("DAY".equals(data.getTimeType())) {
                    for (MdmAssetCalcScheme scheme : schemes) {
                        if (scheme.getType() == 2) {
                            curScheme = scheme;
                            break;
                        }
                    }
                } else if ("MONTH".equals(data.getTimeType())) {
                    for (MdmAssetCalcScheme scheme : schemes) {
                        if (scheme.getType() == 3) {
                            curScheme = scheme;
                            break;
                        }
                    }
                } else if ("MINUTE".equals(data.getTimeType())) {
                    for (MdmAssetCalcScheme scheme : schemes) {
                        if (scheme.getType() == 1) {
                            curScheme = scheme;
                            break;
                        }
                    }
                }
                if (curScheme == null)
                    continue;

                DataReg lastData = getLastDataReg(data.getDataRegPK().getSdpId(), curScheme, data.getDataRegPK().getTv());
                if (lastData == null)
                    continue;
                calcEnergyReg(pt, ct, data, lastData, curScheme);

            }
            return count;
        }

        @Override
        public void run() {
            while (true) {
                int count = handleReg(10000);
                if (count == 0) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        // TODO Auto-generated catch block
                        e.printStackTrace();
                    }
                }
            }
        }

    }

    private class DataSaveProc implements Runnable {

        private int dataCalcProgressSave(int count) {
            if (dataCalcProgressQueue.size() == 0)
                return 0;
            if (System.currentTimeMillis() - lastSaveCalcProgressTime < 15000
                    && dataCalcProgressQueue.size() < 100)
                return 0;
            if (count > dataCalcProgressQueue.size())
                count = dataCalcProgressQueue.size();
            lastSaveCalcProgressTime = System.currentTimeMillis();
            List<DataCalcProgress> datas = new ArrayList<DataCalcProgress>();
            dataCalcProgressQueue.drainTo(datas, count);
            Collections.sort(datas, new Comparator<DataCalcProgress>() {
                @Override
                public int compare(DataCalcProgress o1, DataCalcProgress o2) {
                    if (o1.getTv().getTime() > o2.getTv().getTime()) {
                        return 1;
                    } else if (o1.getTv().getTime() < o2.getTv().getTime()) {
                        return -1;
                    } else {
                        return 0;
                    }
                }
            });

            try {
                long startTime = System.currentTimeMillis();
                String loggerInfo = "Update";
                boolean ret = dataAccessService.batchUpdateCalcProgressSave("mdm_data_calc_progress", datas);

                loggerInfo += (" mdm_data_calc_progress[count: " + count + ", time: "
                        + (System.currentTimeMillis() - startTime) + "]");
                Log.info(loggerInfo);
                Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "Database", loggerInfo);
            } catch (Exception e) {
                e.printStackTrace();
                dataCalcProgressQueue.addAll(datas);
            }
            return count;
        }


        private int dataMinutelySave(int count) {
            if (dataEnergyMinutelyQueue.size() == 0)
                return 0;
            if (System.currentTimeMillis() - lastSaveMinutelyTime < 15000
                    && dataEnergyMinutelyQueue.size() < 100)
                return 0;
            if (count > dataEnergyMinutelyQueue.size())
                count = dataEnergyMinutelyQueue.size();
            lastSaveMinutelyTime = System.currentTimeMillis();
            List<DataEnergy> datas = new ArrayList<DataEnergy>();
            dataEnergyMinutelyQueue.drainTo(datas, count);
            try {
                long startTime = System.currentTimeMillis();
                boolean ret = rtDataAccessService.batchSave("mdm_data_energy_minutely", datas);
                if (!ret) {
                    rtDataAccessService.batchUpdateSave("mdm_data_energy_minutely", datas);
                }
                String loggerInfo = "Insert";
                ret = dataAccessService.batchSave("mdm_data_energy_minutely", datas);
                if (!ret) {
                    loggerInfo = "Update";
                    dataAccessService.batchUpdateSave("mdm_data_energy_minutely", datas);
                }

                loggerInfo += (" mdm_data_energy_minutely[count: " + count + ", time: "
                        + (System.currentTimeMillis() - startTime) + "]");
                Log.info(loggerInfo);
                Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "Database", loggerInfo);
            } catch (Exception e) {
                e.printStackTrace();
                dataEnergyMinutelyQueue.addAll(datas);
            }
            return count;
        }

        private int dataDailySave(int count) {
            if (dataEnergyDailyQueue.size() == 0)
                return 0;
            if (System.currentTimeMillis() - lastSaveDailyTime < 15000
                    && dataEnergyDailyQueue.size() < 100)
                return 0;
            if (count > dataEnergyDailyQueue.size())
                count = dataEnergyDailyQueue.size();
            lastSaveDailyTime = System.currentTimeMillis();
            List<DataEnergy> datas = new ArrayList<DataEnergy>();
            dataEnergyDailyQueue.drainTo(datas, count);
            try {
                long startTime = System.currentTimeMillis();
                boolean ret = rtDataAccessService.batchSave("mdm_data_energy_dayly", datas);
                if (!ret) {
                    rtDataAccessService.batchUpdateSave("mdm_data_energy_dayly", datas);
                }
                String loggerInfo = "Insert";
                ret = dataAccessService.batchSave("mdm_data_energy_dayly", datas);
                if (!ret) {
                    loggerInfo = "Update";
                    dataAccessService.batchUpdateSave("mdm_data_energy_dayly", datas);
                }


                loggerInfo += (" mdm_data_energy_dayly[count: " + count + ", time: "
                        + (System.currentTimeMillis() - startTime) + "]");
                Log.info(loggerInfo);
                Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "Database", loggerInfo);
            } catch (Exception e) {
                e.printStackTrace();
                dataEnergyDailyQueue.addAll(datas);
            }
            return count;
        }

        private int dataMonthlySave(int count) {
            if (dataEnergyMonthlyQueue.size() == 0)
                return 0;
            if (System.currentTimeMillis() - lastSaveMonthlyTime < 15000
                    && dataEnergyMonthlyQueue.size() < 100)
                return 0;
            if (count > dataEnergyMonthlyQueue.size())
                count = dataEnergyMonthlyQueue.size();
            lastSaveMonthlyTime = System.currentTimeMillis();
            List<DataEnergy> datas = new ArrayList<DataEnergy>();
            dataEnergyMonthlyQueue.drainTo(datas, count);
            try {

                long startTime = System.currentTimeMillis();
                boolean ret = rtDataAccessService.batchSave("mdm_data_energy_monthly", datas);
                if (!ret) {
                    rtDataAccessService.batchUpdateSave("mdm_data_energy_monthly", datas);
                }

                String loggerInfo = "Insert";
                ret = dataAccessService.batchSave("mdm_data_energy_monthly", datas);
                if (!ret) {
                    loggerInfo = "Update";
                    dataAccessService.batchUpdateSave("mdm_data_energy_monthly", datas);
                }

                loggerInfo += (" Into mdm_data_energy_monthly[count: " + count + ", time: "
                        + (System.currentTimeMillis() - startTime) + "]");
                Log.info(loggerInfo);
                Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "Database", loggerInfo);
            } catch (Exception e) {
                e.printStackTrace();
                dataEnergyMonthlyQueue.addAll(datas);
            }
            return count;
        }

        @Override
        public void run() {
            while (true) {
                int retCount = 0;
                retCount = dataCalcProgressSave(1000);
                retCount += dataMinutelySave(1000);
                retCount += dataDailySave(1000);
                retCount += dataMonthlySave(1000);
                if (retCount == 0) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        // TODO Auto-generated catch block
                        e.printStackTrace();
                    }
                }
            }
        }
    }
}
