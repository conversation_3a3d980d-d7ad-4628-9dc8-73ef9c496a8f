<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="DictMapper">

    <select id="getDictProfileList" resultType="DictProfile"  parameterType="Request">
        select
        *
        from
        dict_profile
        where
        1 = 1
        <if test="entity">
            <if test="entity.id !=null and entity.id != ''">
                AND ID  LIKE CONCAT(CONCAT('%', #{entity.id}),'%')
            </if>

            <if test="entity.protocolCode !=null and entity.protocolCode != ''">
                AND PROTOCOL_CODE LIKE CONCAT(CONCAT('%', #{entity.protocolCode}),'%')
            </if>
            <if test="entity.name != null and entity.name != ''">
                and name like concat(concat('%', #{entity.name}),'%')
            </if>
            <if test="entity.protocolId != null and entity.protocolId != ''">
                and protocol_id = #{entity.protocolId}
            </if>
            <if test="entity.profileType">
                and profile_type = #{entity.profileType}
            </if>
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <select id="getDictDataitemGroupList" resultType="DictDataitemGroup"  parameterType="Request">
        select
        *
        from
        dict_dataitem_group
        where
        sort_id > 0
        <if test="entity">
            <if test="entity.name != null and entity.name != ''">
                and name like concat(concat('%', #{entity.name}),'%')
            </if>
            <if test="entity.protocolId != null and entity.protocolId != ''">
                and protocol_id = #{entity.protocolId}
            </if>
            <if test="entity.appType != null and entity.appType != ''">
                and app_type = #{entity.appType}
            </if>
             <if test="entity.assetMeterGroupId != null and entity.assetMeterGroupId != ''">
                  and ( asset_meter_group_id = #{entity.assetMeterGroupId}  or asset_meter_group_id is null )
            </if>
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <select id="getDataGroupList" resultType="DictDataitemGroup"  parameterType="Request">
        select
        *
        from
        dict_dataitem_group,
        where
        sort_id > 0
        <if test="entity">
            <if test="entity.name != null and entity.name != ''">
                and name like concat(concat('%', #{entity.name}),'%')
            </if>
            <if test="entity.protocolId != null and entity.protocolId != ''">
                and protocol_id = #{entity.protocolId}
            </if>
            <if test="entity.appType != null and entity.appType != ''">
                and app_type = #{entity.appType}
            </if>
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <select id="getDictDataitemGroupMapList" resultType="DictDataitemGroupMap"  parameterType="Request">
        select
        ddgm.*,
        dd.name
        from
        dict_dataitem dd,
        dict_dataitem_group_map ddgm
        where
        ddgm.dataitem_id = dd.id
        and ddgm.sort_id > 0
        <if test="entity">
            <if test="entity.groupId != null and entity.groupId != ''">
                and ddgm.group_id = #{entity.groupId}
            </if>
            <if test="entity.dataitemId != null and entity.dataitemId != ''">
                and ddgm.dataitem_id = #{entity.dataitemId}
            </if>
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <select id="getDictEvtDataitemGroupMapList" resultType="DictDataitemGroupMap"  parameterType="PagingRequest">
        select
        ddgm.*,
        dd.name
        from
        dict_dataitem dd,
        dict_dataitem_group_map ddgm
        where
        ddgm.dataitem_id = dd.id and ddgm.group_id in (select id  from DICT_DATAITEM_GROUP where app_type = 2)
        <if test="entity">
            <if test="entity.groupId != null and entity.groupId != ''">
                and ddgm.group_id = #{entity.groupId}
            </if>
            <if test="entity.dataitemId != null and entity.dataitemId != ''">
                and ddgm.dataitem_id = #{entity.dataitemId}
            </if>
            <if test="entity.name != null and entity.name != ''">
                and dd.name like concat(concat('%', #{entity.name}),'%')
            </if>
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <update id="updateDictDataitemGroupMapOrderId"
            parameterType="DictDataitemGroupMap">
        update dict_dataitem_group_map
        <set>

           sort_id = #{entity.sortId}

        </set>
        where 1 = 1
        <if test="entity">
            <if test="entity.groupId != null and entity.groupId != ''">
                and group_Id = #{entity.groupId}
            </if>
            <if test="entity.dataitemId != null and entity.dataitemId != ''">
                and dataitem_Id = #{entity.dataitemId}
            </if>


        </if>
    </update>

    <select id="getDictDataitemList" resultType="DictDataitem"  parameterType="Request">
        select
        dd.*
        from
        dict_dataitem dd
        left join dict_dataitem_group_map ddgm on dd.id = ddgm.dataitem_id
        where
        1 = 1
        <if test="entity">
            <if test="entity.id != null and entity.id != ''">
                and dd.id like concat(concat('%', #{entity.id}),'%')
            </if>
            <if test="entity.name != null and entity.name != ''">
                and dd.name like concat(concat('%', #{entity.name}),'%')
            </if>
            <if test="entity.protocolCode != null and entity.protocolCode != ''">
                and dd.protocol_code like concat(concat('%', #{entity.protocolCode}),'%')
            </if>
            <if test="entity.protocolId != null and entity.protocolId != ''">
                and dd.protocol_id = #{entity.protocolId}
            </if>
            <if test="entity.groupId != null and entity.groupId != ''">
                and ddgm.group_id = #{entity.groupId}
                and ddgm.sort_id > 0
            </if>
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <select id="getOriDictDataitemList" resultType="DictDataitem"  parameterType="Request">
        select
        dd.*
        from
        dict_dataitem dd
        where
        1 = 1
        <if test="entity">
            <if test="entity.id != null and entity.id != ''">
                and dd.id like concat(concat('%', #{entity.id}),'%')
            </if>
            <if test="entity.name != null and entity.name != ''">
                and dd.name like concat(concat('%', #{entity.name}),'%')
            </if>
            <if test="entity.protocolCode != null and entity.protocolCode != ''">
                and dd.protocol_code like concat(concat('%', #{entity.protocolCode}),'%')
            </if>
            <if test="entity.protocolId != null and entity.protocolId != ''">
                and dd.protocol_id = #{entity.protocolId}
            </if>
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>

    <select id="getDictDeviceModelList" resultType="DictDeviceModel"  parameterType="Request">
        select
        *
        from
        dict_device_model
        where
        1 = 1
        <if test="entity">
            <if test="entity.name != null and entity.name != ''">
                and name like concat(concat('%', #{entity.name}),'%')
            </if>
            <if test="entity.protocolId != null and entity.protocolId != ''">
                and protocol_id = #{entity.protocolId}
            </if>
            <if test="entity.manufacturerId != null and entity.manufacturerId != ''">
                and manufacturer_id = #{entity.manufacturerId}
            </if>
            <if test="entity.deviceType != null">
                and device_type = #{entity.deviceType}
            </if>
        </if>

        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>

    </select>


</mapper>