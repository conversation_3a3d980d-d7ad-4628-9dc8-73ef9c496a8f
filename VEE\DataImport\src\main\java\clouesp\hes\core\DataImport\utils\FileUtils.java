package clouesp.hes.core.DataImport.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
@Slf4j
public class FileUtils {
	private static FileUtils instance = null;
	private FileUtils() {
		
	}
	public static FileUtils getInstance() {
		if (instance == null) {
			instance = new FileUtils();
		}
		return instance;
	}		
	
//	private DateFormat sdflog = new SimpleDateFormat("MM-dd HH:mm:ss");
	
	public void backupFile(String filePath) {


		try {

			//log.info("Start backup file : " + filePath);

			File startFile = new File(filePath);
			int pos = filePath.lastIndexOf("/");
			if(pos == -1) {
				pos = filePath.lastIndexOf("\\");
			}

			String endDir = filePath.substring(0, pos) + "/Backup";
			File endDirection = new File(endDir);
			if (!endDirection.exists()) {
				endDirection.mkdirs();
			}
			File endFile = new File(endDirection + File.separator + startFile.getName());
			if (endFile.exists()) {
				if ( !endFile.delete()){
					log.warn("Delete file failed : " + filePath);
				}
			}

			if (startFile.renameTo(endFile)) {
//				System.out.println("File is moved successful!"); 
//				String loggerInfo = endFile.getName() + " has been backed up successfully";
 				log.info(endFile.getName() + " has been backed up successfully");
				//System.out.println("[" + sdflog.format(new Date()) + "] " + loggerInfo);
			} else {
				//System.out.println(endFile.getName() + " is failed to move!");
				log.warn(endFile.getName() + " is failed to move!");
			}
			//log.info("End backup file : " + filePath);
		}catch (Exception e){
			log.error("DataImport Error : ", e);
		}
	}
}
