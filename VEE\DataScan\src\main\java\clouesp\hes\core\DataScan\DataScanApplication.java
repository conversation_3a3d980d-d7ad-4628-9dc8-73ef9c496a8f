package clouesp.hes.core.DataScan;

import java.io.File;
import java.io.IOException;

import org.apache.rocketmq.client.log.ClientLogger;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.client.RestOperations;

@PropertySource("file:${user.dir}/config/application.yml") 
@SpringBootApplication
@EnableScheduling
@EnableAutoConfiguration
@ComponentScan(basePackages={
		"clouesp.hes.core.DataScan", 
		"clouesp.hes.common.MqBus"
		})
public class DataScanApplication {

	public static void main(String[] args) {
		System.setProperty(ClientLogger.CLIENT_LOG_USESLF4J,"true");
		try {
			File directory = new File("");// 参数为空
			String courseFile;
			courseFile = directory.getCanonicalPath();
			File parent = new File(courseFile);
			String parentPath = parent.getParent();
			String addClassPath = "spring.config.additional-location:classpath:/";
			addClassPath += "," +parentPath + "/config/";
			new SpringApplicationBuilder(DataScanApplication.class).properties("spring.config.name:application", addClassPath).build().run(args);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	@Bean
	public RestOperations  restTemplate (RestTemplateBuilder builder) {
		return builder.build();

	}

}
