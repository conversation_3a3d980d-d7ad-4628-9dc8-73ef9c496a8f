package clouesp.hes.core.DataCalc.service.Storage;

import clouesp.hes.common.CommonUtils.StringUtils;
import clouesp.hes.common.DataEntity.Data.DataStealEvent;
import clouesp.hes.common.DataEntity.Data.DataVEEEvent;
import clouesp.hes.common.DataRepository.Persistence.Data.DataStealEventRepository;
import clouesp.hes.common.DataRepository.Persistence.Data.DataVEEEventRepository;
import clouesp.hes.common.logger.logger.Logger;
import clouesp.hes.common.logger.logger.LoggerLevel;
import clouesp.hes.core.DataCalc.config.ServerConfig;
import jline.internal.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;

@Service("eventStorageService")
public class EventStorageService {
    private EventSaveProc eventSaveProc = null;
    private Thread eventSaveProcThread = null;
    private long lastSaveTime = System.currentTimeMillis();
    private long lastStealSaveTime = System.currentTimeMillis();
    private SimpleDateFormat sdflog = new SimpleDateFormat("MM-dd HH:mm:ss");

    private LinkedBlockingQueue<DataVEEEvent> eventQueue = new LinkedBlockingQueue<DataVEEEvent>();
    private LinkedBlockingQueue<DataStealEvent> stealQueue = new LinkedBlockingQueue<DataStealEvent>();

    @Autowired
    private DataVEEEventRepository dataVEEEventRepository;

    @Autowired
    private DataStealEventRepository dataStealEventRepository;

    @Autowired
    private ServerConfig serverConfig;

    public void init() {
        if(eventSaveProcThread == null) {
            eventSaveProc = new EventSaveProc();
            eventSaveProcThread = new Thread(eventSaveProc);
            eventSaveProcThread.start();
        }
    }

    public void putEvent(DataVEEEvent event, String sourceEventType) {

        try {
            event.setUpdateTv(new Date());
            eventQueue.put(event);
            if (!StringUtils.isEmpty(sourceEventType)) {
                DataStealEvent dataStealEvent = new DataStealEvent();
                dataStealEvent.getDataStealEventPK().setSdpId(event.getDataVEEEventPK().getObjectId());
                dataStealEvent.getDataStealEventPK().setEventId(sourceEventType);
                dataStealEvent.getDataStealEventPK().setTv(event.getDataVEEEventPK().getTv());
                stealQueue.put(dataStealEvent);
            }
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }


    private class EventSaveProc implements Runnable{
        private int eventSave(int count) {
            if(eventQueue.size() == 0)
                return 0;

            if(System.currentTimeMillis() - lastSaveTime < 15000
                    && eventQueue.size() < 1000)
                return 0;
            if(count > eventQueue.size())
                count = eventQueue.size();
            lastSaveTime = System.currentTimeMillis();
            List<DataVEEEvent> events = new ArrayList<DataVEEEvent>();
            eventQueue.drainTo(events, count);
            try {
                long startTime = System.currentTimeMillis();
                dataVEEEventRepository.saveAll(events);

                String loggerInfo = "Into mdm_data_vee_event[count: " + count + ", time: "
                        + (System.currentTimeMillis() - startTime) + "]";
                Log.info(loggerInfo);
                Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "Database", loggerInfo);
            } catch(Exception e) {
                eventQueue.addAll(events);
            }
            return count;
        }

        private int stealSave(int count) {
            if(stealQueue.size() == 0)
                return 0;

            if(System.currentTimeMillis() - lastStealSaveTime < 15000
                    && stealQueue.size() < 1000)
                return 0;
            if(count > stealQueue.size())
                count = stealQueue.size();
            lastStealSaveTime = System.currentTimeMillis();
            List<DataStealEvent> steals = new ArrayList<DataStealEvent>();
            stealQueue.drainTo(steals, count);
            try {
                long startTime = System.currentTimeMillis();
                dataStealEventRepository.saveAll(steals);
                String loggerInfo = "Into mdm_data_steal_event[count: " + count + ", time: "
                        + (System.currentTimeMillis() - startTime) + "]";
                Log.info(loggerInfo);
                Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "Database", loggerInfo);
            } catch(Exception e) {
                e.printStackTrace();
                stealQueue.addAll(steals);
            }
            return count;
        }

        @Override
        public void run() {
            while(true){
                int retCount = 0;
                retCount = eventSave(1000);
                retCount += stealSave(1000);
                if(retCount == 0){
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        // TODO Auto-generated catch block
                        e.printStackTrace();
                    }
                }
            }
        }

    }

}
