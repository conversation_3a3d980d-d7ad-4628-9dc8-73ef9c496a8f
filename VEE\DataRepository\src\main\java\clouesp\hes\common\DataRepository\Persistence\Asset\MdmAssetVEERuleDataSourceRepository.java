package clouesp.hes.common.DataRepository.Persistence.Asset;

import org.springframework.data.jpa.repository.JpaRepository;

import clouesp.hes.common.DataEntity.Asset.MdmAssetVEERuleDataSource;
import clouesp.hes.common.DataEntity.Asset.MdmAssetVEERuleDataSourcePK;

import java.util.List;

public interface MdmAssetVEERuleDataSourceRepository extends JpaRepository<MdmAssetVEERuleDataSource, MdmAssetVEERuleDataSourcePK>{

    List<MdmAssetVEERuleDataSource> findAllByRuleId(String ruleId);
}
