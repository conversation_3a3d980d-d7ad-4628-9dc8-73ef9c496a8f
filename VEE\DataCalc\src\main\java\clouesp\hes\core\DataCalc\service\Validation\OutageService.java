package clouesp.hes.core.DataCalc.service.Validation;

import clouesp.hes.common.DataEntity.Data.DataVEEEvent;
import clouesp.hes.common.DataEntity.Data.DataVEEEventPK;
import clouesp.hes.common.DataRepository.Persistence.Data.DataVEEEventRepository;
import clouesp.hes.common.DataRepository.RealTime.Data.RtDataVEEEventRepository;
import clouesp.hes.core.DataCalc.config.ServerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service("outageService")
public class OutageService {
    @Autowired
    private DataVEEEventRepository dataVEEEventRepository;
    @Autowired
    private RtDataVEEEventRepository rtDataVEEEventRepository;
    @Autowired
    private ServerConfig serverConfig;

    public List<DataVEEEvent> handleOutage(
            String sdpId,
            Date tv,
            int schemeType
    ) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DataVEEEvent powerDownEvent = rtDataVEEEventRepository.findEvent(sdpId, "20001001", sdf.format(tv));
        if (powerDownEvent == null) {
            if ( serverConfig.isOracleDb() )
                powerDownEvent = dataVEEEventRepository.oracleFindEvent(sdpId, "20001001", sdf.format(tv));
            else
                powerDownEvent = dataVEEEventRepository.mysqlFindEvent(sdpId, "20001001", sdf.format(tv));
        }
        if (powerDownEvent == null) {
            return null;
        }

        List<DataVEEEvent> dataVEEEvents = new ArrayList<DataVEEEvent>();
        DataVEEEvent dataVEEEvent = new DataVEEEvent();
        DataVEEEventPK dataVEEEventPK = dataVEEEvent.getDataVEEEventPK();

        dataVEEEventPK.setTv(powerDownEvent.getDataVEEEventPK().getTv());
        dataVEEEventPK.setEventId("1007003");
        dataVEEEventPK.setSchemeType(schemeType);
        dataVEEEventPK.setObjectId(sdpId);

        dataVEEEvent.setUpdateTv(new Date());
        dataVEEEvent.setDataSource(3);
        dataVEEEvent.setObjectType(7);
        dataVEEEvent.setDataType(106);

        dataVEEEvent.setEndTv(tv);
        long durationTime = tv.getTime() - dataVEEEventPK.getTv().getTime();
        durationTime = durationTime / (1000 * 60);
        dataVEEEvent.setDurationTime(durationTime);

        dataVEEEvents.add(dataVEEEvent);

        return dataVEEEvents;
    }

}
