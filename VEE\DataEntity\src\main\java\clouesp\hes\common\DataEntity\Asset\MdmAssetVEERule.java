package clouesp.hes.common.DataEntity.Asset;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name= "mdm_asset_vee_rule                                                 ")
public class MdmAssetVEERule {
	@Id
	@Column(name = "id", nullable = false, columnDefinition = "varchar(32)")
	private String id;
	
	@Column(name = "name", columnDefinition = "varchar(128)")
	private String name;
	
	@Column(name = "mg_id", columnDefinition = "varchar(32)")
	private String mgId;
	
	@Column(name = "class_id")
	private Integer classId;
	
	@Column(name = "rule_detail", columnDefinition = "varchar(256)")
	private String ruleDetail;
	
	@Column(name = "event_id", columnDefinition = "varchar(32)")
	private String eventId;
	
	@Column(name = "rule_status")
	private Integer ruleStatus;
	
	@Column(name = "rule_type")
	private Integer ruleType;	
	
	@Column(name = "estimation_flag")
	private Integer estimationFlag;	
	
	@Column(name = "method")
	private String method;	
	
	@Column(name = "mult_datasource")
	private Integer multDatasource;	
	
	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getMgId() {
		return mgId;
	}

	public void setMgId(String mgId) {
		this.mgId = mgId;
	}

	public Integer getClassId() {
		return classId;
	}

	public void setClassId(Integer classId) {
		this.classId = classId;
	}

	public String getRuleDetail() {
		return ruleDetail;
	}

	public void setRuleDetail(String ruleDetail) {
		this.ruleDetail = ruleDetail;
	}

	public String getEventId() {
		return eventId;
	}

	public void setEventId(String eventId) {
		this.eventId = eventId;
	}

	public Integer getRuleStatus() {
		return ruleStatus;
	}

	public void setRuleStatus(Integer ruleStatus) {
		this.ruleStatus = ruleStatus;
	}

	public Integer getRuleType() {
		return ruleType;
	}

	public void setRuleType(Integer ruleType) {
		this.ruleType = ruleType;
	}

	public Integer getEstimationFlag() {
		return estimationFlag;
	}

	public void setEstimationFlag(Integer estimationFlag) {
		this.estimationFlag = estimationFlag;
	}

	public String getMethod() {
		return method;
	}

	public void setMethod(String method) {
		this.method = method;
	}

	public Integer getMultDatasource() {
		return multDatasource;
	}

	public void setMultDatasource(Integer multDatasource) {
		this.multDatasource = multDatasource;
	}	
}
