package clouesp.hes.core.RealTimeDB.service;

import clouesp.hes.common.DataRepository.Persistence.Asset.*;
import clouesp.hes.common.DataRepository.Persistence.Data.DataDiExportProgressRepository;
import clouesp.hes.common.DataRepository.Persistence.Data.MdmDataUpdateLogRepository;
import clouesp.hes.common.DataRepository.Persistence.Dict.DictDetailRepository;
import clouesp.hes.common.DataRepository.Persistence.Dict.MeterDataStorageInfoRepository;
import clouesp.hes.common.DataRepository.Persistence.Dict.MeterDataStorageTableRepository;
import clouesp.hes.common.DataRepository.Persistence.Dict.VEEMethodRepository;
import clouesp.hes.common.DataRepository.Persistence.System.*;
import clouesp.hes.common.DataRepository.RealTime.Asset.*;
import clouesp.hes.common.DataRepository.RealTime.Data.*;
import clouesp.hes.common.DataRepository.RealTime.Dict.RtDictDetailRepository;
import clouesp.hes.common.DataRepository.RealTime.Dict.RtMeterDataStorageInfoRepository;
import clouesp.hes.common.DataRepository.RealTime.Dict.RtMeterDataStorageTableRepository;
import clouesp.hes.common.DataRepository.RealTime.Dict.RtVEEMethodRepository;
import clouesp.hes.common.DataRepository.RealTime.System.*;
import clouesp.hes.common.logger.logger.Logger;
import clouesp.hes.common.logger.logger.LoggerLevel;
import clouesp.hes.core.RealTimeDB.config.ServerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

@Service("reloadRTDBService")
public class ReloadRTDBService {
    @Autowired
    private AssetMeterRepository assetMeterRepository;

    @Autowired
    private MdmAssetHesRepository mdmAssetHesRepository;

    @Autowired
    private RtMdmAssetHesRepository rtMdmAssetHesRepository;

    @Autowired
    private RtAssetMeterRepository rtAssetMeterRepository;

    @Autowired
    private MdmAssetServicePointRepository mdmAssetServicePointRepository;

    @Autowired
    private RtMdmAssetServicePointRepository rtMdmAssetServicePointRepository;


    @Autowired
    private MdmAssetVEERuleRepository mdmAssetVEERuleRepository;

    @Autowired
    private RtMdmAssetVEERuleRepository rtMdmAssetVEERuleRepository;

    @Autowired
    private MdmAssetVEERuleDataSourceRepository mdmAssetVEERuleDataSourceRepository;

    @Autowired
    private RtMdmAssetVEERuleDataSourceRepository rtMdmAssetVEERuleDataSourceRepository;

    @Autowired
    private MdmAssetVEERuleParamRepository mdmAssetVEERuleParamRepository;

    @Autowired
    private RtMdmAssetVEERuleParamRepository rtMdmAssetVEERuleParamRepository;

    @Autowired
    private VEEMethodRepository veeMethodRepository;

    @Autowired
    private RtVEEMethodRepository rtVEEMethodRepository;

    @Autowired
    private MeterDataStorageTableRepository meterDataStorageTableRepository;

    @Autowired
    private RtMeterDataStorageTableRepository rtMeterDataStorageTableRepository;

    @Autowired
    private MdmAssetCalcSchemeRepository mdmAssetCalcSchemeRepository;

    @Autowired
    private RtMdmAssetCalcSchemeRepository rtMdmAssetCalcSchemeRepository;

    @Autowired
    private MdmAssetCalcObjRepository mdmAssetCalcObjRepository;

    @Autowired
    private RtMdmAssetCalcObjRepository rtMdmAssetCalcObjRepository;

    @Autowired
    private RtDataRegDailyRepository rtDataRegDailyRepository;
    @Autowired
    private RtDataRegMinutelyRepository rtDataRegMinutelyRepository;
    @Autowired
    private RtDataRegMonthlyRepository rtDataRegMonthlyRepository;
    @Autowired
    private RtDataIntervalDailyRepository rtDataIntervalDailyRepository;
    @Autowired
    private RtDataIntervalMinutelyRepository rtDataIntervalMinutelyRepository;
    @Autowired
    private RtDataIntervalMonthlyRepository rtDataIntervalMonthlyRepository;
    @Autowired
    private RtDataInstMinutelyRepository rtDataInstMinutelyRepository;
    @Autowired
    private RtDataVEEEventRepository rtDataVEEEventRepository;

    @Autowired
    private MeterDataStorageInfoRepository meterDataStorageInfoRepository;

    @Autowired
    private RtMeterDataStorageInfoRepository rtMeterDataStorageInfoRepository;

    @Autowired
    private SysDataitemExportRepository sysDataitemExportRepository;

    @Autowired
    private RtSysDataitemExportRepository rtSysDataitemExportRepository;

    @Autowired
    private DataDiExportProgressRepository dataDiExportProgressRepository;

    @Autowired
    private RtDataDiExportProgressRepository rtDataDiExportProgressRepository;

    @Autowired
    private SysDataitemExportKeyRepository sysDataitemExportKeyRepository;

    @Autowired
    private RtSysDataitemExportKeyRepository rtSysDataitemExportKeyRepository;

    @Autowired
    private DictDetailRepository dictDetailRepository;

    @Autowired
    private RtDictDetailRepository rtDictDetailRepository;

    @Autowired
    private MdmDataUpdateLogRepository mdmDataUpdateLogRepository;

    @Autowired
    private RtMdmDataUpdateLogRepository rtMdmDataUpdateLogRepository;

    @Autowired
    private MdmAssetMeterReplacementRepository mdmAssetMeterReplacementRepository;

    @Autowired
    private RtMdmAssetMeterReplacementRepository rtMdmAssetMeterReplacementRepository;

    private boolean isloadRealTimeDB = false;

    @Autowired
    private ServerConfig serverConfig;

    private SimpleDateFormat sdflog = new SimpleDateFormat("MM-dd HH:mm:ss");

    @Scheduled(initialDelay = 600000, fixedDelay = 6 * 3600 * 1000)
    public void loadRealTimeDB() {
        String logInfo;
        logInfo = "Not load database";
        if (serverConfig.getInitServiceRet() == -1) {
            System.out.println("[" + sdflog.format(new Date()) + "] " + logInfo);
            return;
        }
        if(isloadRealTimeDB) {
            return;
        }

        isloadRealTimeDB = true;
        logInfo = "Load database start";
        clearExpiredRTDB();
        System.out.println("[" + sdflog.format(new Date()) + "] " + logInfo);
        Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "System", logInfo);

        rtVEEMethodRepository.deleteAll();
        rtMdmAssetVEERuleParamRepository.deleteAll();
        rtMdmAssetVEERuleDataSourceRepository.deleteAll();
        rtMdmAssetVEERuleRepository.deleteAll();
        rtMdmAssetVEERuleRepository.saveAll(mdmAssetVEERuleRepository.findAll());
        rtMdmAssetVEERuleDataSourceRepository.saveAll(mdmAssetVEERuleDataSourceRepository.findAll());
        rtMdmAssetVEERuleParamRepository.saveAll(mdmAssetVEERuleParamRepository.findAll());
        rtVEEMethodRepository.saveAll(veeMethodRepository.findAll());

        rtMeterDataStorageInfoRepository.deleteAll();
        rtMeterDataStorageTableRepository.deleteAll();
        rtMeterDataStorageTableRepository.saveAll(meterDataStorageTableRepository.findAll());
        rtMeterDataStorageInfoRepository.saveAll(meterDataStorageInfoRepository.findAll());

        rtSysDataitemExportKeyRepository.deleteAll();
        rtDataDiExportProgressRepository.deleteAll();
        rtSysDataitemExportRepository.deleteAll();
        rtSysDataitemExportRepository.saveAll(sysDataitemExportRepository.findAll());
        rtDataDiExportProgressRepository.saveAll(dataDiExportProgressRepository.findAll());
        rtSysDataitemExportKeyRepository.saveAll(sysDataitemExportKeyRepository.findAll());

        rtDictDetailRepository.deleteAll();
        rtDictDetailRepository.saveAll(dictDetailRepository.findAll());

        rtMdmAssetCalcSchemeRepository.deleteAll();
        rtMdmAssetCalcSchemeRepository.saveAll(mdmAssetCalcSchemeRepository.findAll());
        rtMdmAssetCalcObjRepository.deleteAll();
        rtMdmAssetCalcObjRepository.saveAll(mdmAssetCalcObjRepository.findAll());

        rtMdmDataUpdateLogRepository.deleteAll();
        rtMdmAssetServicePointRepository.deleteAll();
        rtAssetMeterRepository.deleteAll();
        
        // Clear and reload HES asset data
        rtMdmAssetHesRepository.deleteAll();
        rtMdmAssetHesRepository.saveAll(mdmAssetHesRepository.findAll());
        
        rtAssetMeterRepository.saveAll(assetMeterRepository.findAll());
        rtMdmAssetServicePointRepository.saveAll(mdmAssetServicePointRepository.findAll());
        rtMdmDataUpdateLogRepository.saveAll(mdmDataUpdateLogRepository.findAll());

        rtMdmAssetMeterReplacementRepository.saveAll(mdmAssetMeterReplacementRepository.findAll());

        logInfo = "Load Database end";
        System.out.println("[" + sdflog.format(new Date()) + "] " + logInfo);
        Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "System", logInfo);
        isloadRealTimeDB = false;
    }

    @Scheduled(cron="0 0 23 * * ?")
    private void clearExpiredRTDB() {
        Date curTv = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(curTv);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        cal.add(Calendar.DAY_OF_MONTH, -15);
        Date dayTv = cal.getTime();
        rtDataRegDailyRepository.clearExpired(dayTv);
        rtDataIntervalDailyRepository.clearExpired(dayTv);

        cal.setTime(curTv);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        cal.add(Calendar.MONTH, -12);
        Date monthTv = cal.getTime();
        rtDataRegMonthlyRepository.clearExpired(monthTv);
        rtDataIntervalMonthlyRepository.clearExpired(monthTv);

        cal.setTime(curTv);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        cal.add(Calendar.DAY_OF_MONTH, -3);

        Date minuteTv = cal.getTime();
        rtDataRegMinutelyRepository.clearExpired(minuteTv);
        rtDataIntervalMinutelyRepository.clearExpired(minuteTv);
        rtDataInstMinutelyRepository.clearExpired(minuteTv);

        cal.setTime(curTv);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        cal.add(Calendar.MONTH, -3);

        Date eventTv = cal.getTime();
        rtDataVEEEventRepository.clearExpired(eventTv);
    }
}
