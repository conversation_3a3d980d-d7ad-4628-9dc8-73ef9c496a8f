---
title: "DataScan 模块设计文档"
author: "ClouESP研发团队"
date: "2025年3月20日"
documentclass: article
papersize: a4
geometry:
  - margin=2.5cm
toc: true
toc-depth: 3
numbersections: true
mainfont: SimSun
monofont: Consolas
sansfont: Microsoft YaHei
fontsize: 11pt
linestretch: 1.5
header-includes:
  - \usepackage{fancyhdr}
  - \pagestyle{fancy}
  - \fancyhead[L]{ClouESP VEE}
  - \fancyhead[R]{DataScan模块}
  - \fancyfoot[C]{\thepage}
output:
  word_document:
    reference_docx: custom-reference.docx
---

# DataScan 模块设计文档

## 1. 项目概述

### 1.1 项目背景

DataScan是ClouESP平台的核心组件之一，属于主数据管理(MDM)系统的验证、估算和编辑(VEE)子系统的数据扫描模块。该模块负责定期扫描数据库中的各类电力数据记录和事件，并将其发送到消息队列以供其他服务处理和分析。

### 1.2 项目目标

- 定期扫描不同时间粒度的数据记录(分钟级、日级、月级)
- 定期扫描VEE事件记录
- 将扫描到的数据通过消息队列发送至其他模块进行处理
- 支持Oracle和MySQL两种数据库系统
- 保证数据扫描的高效性和可靠性

### 1.3 系统版本

当前版本: 2024092310

## 2. 技术架构

### 2.1 开发技术

| 技术类型 | 使用技术 | 版本 | 说明 |
|---------|---------|------|------|
| 编程语言 | Java | 1.8 | 核心开发语言 |
| 框架 | Spring Boot | 2.4.0 | 应用开发框架 |
| ORM框架 | MyBatis | 2.1.4 | 数据库对象映射框架 |
| 消息队列 | RocketMQ | - | 系统间通信 |
| 数据库 | H2/Oracle/MySQL | - | 支持多种数据库系统 |
| API文档 | Swagger | 2.9.2 | API接口文档 |
| 脚本语言 | Groovy | 3.0.2 | 业务规则扩展 |

### 2.2 系统架构

DataScan作为独立的微服务组件运行，其系统架构如下：

```
+----------------+      +----------------+      +----------------+
|                |      |                |      |                |
|  关系型数据库   |<---->|    DataScan    |<---->|    RocketMQ    |
| (MySQL/Oracle) |      |     服务       |      |   消息队列     |
|                |      |                |      |                |
+----------------+      +----------------+      +----------------+
                               ^
                               |
                               v
                        +----------------+
                        |                |
                        |  H2内存数据库  |
                        |                |
                        +----------------+
```

### 2.3 项目依赖

项目的主要依赖包括：

- **Spring Boot相关**
  - spring-boot-starter-web
  - spring-boot-starter-data-jpa
  - spring-boot-configuration-processor
  - spring-boot-starter-test

- **数据库驱动**
  - h2
  - ojdbc8 (Oracle)
  - mysql-connector-java

- **MyBatis**
  - mybatis-spring-boot-starter

- **文档**
  - springfox-swagger2
  - springfox-swagger-ui

- **内部依赖模块**
  - CommonUtils
  - DataRepository
  - DataModel
  - MqBus
  - Logger

## 3. 系统设计

### 3.1 模块划分

DataScan系统划分为以下主要模块：

| 模块名称 | 路径 | 功能描述 |
|---------|------|---------|
| 配置模块 | config/ | 负责系统配置的加载和管理 |
| 控制器模块 | controller/ | 提供对外HTTP接口 |
| 数据访问模块 | dao/ | 处理与数据库的交互 |
| 定时任务模块 | schedule/ | 负责定时执行扫描任务 |
| 服务模块 | service/ | 包含核心业务逻辑 |
| 工具模块 | Utils/ | 提供通用工具类 |
| 应用入口 | DataScanApplication.java | 系统启动入口 |
| 初始化 | InitCommandLineRunner.java | 启动时运行的初始化任务 |

### 3.2 功能设计

#### 3.2.1 数据扫描功能

系统能够扫描以下类型的数据：

1. **月度数据**
   - 数据表：mdm_data_reg_monthly
   - 扫描范围：过去6个月

2. **日度数据**
   - 数据表：mdm_data_reg_dayly
   - 扫描范围：过去30天

3. **分钟级数据**
   - 数据表：mdm_data_reg_minutely
   - 扫描范围：过去7天

4. **VEE事件数据**
   - 数据表：mdm_data_vee_event
   - 扫描范围：过去1个月

**扫描流程**：

1. 根据配置的时间范围确定扫描的起始时间
2. 根据数据库类型选择不同的查询策略(Oracle或MySQL)
3. 分批次(每批100000条)查询数据
4. 将查询到的数据转换为对应的实体对象(DataReg或DataVEEEvent)
5. 通过RocketMQ消息队列发送给其他服务处理

#### 3.2.2 服务状态维护功能

系统定期(每60秒)更新自身在系统服务表中的状态信息，确保系统监控正常。主要流程包括：

1. 从实时数据库查询服务信息
2. 更新服务状态和在线时间
3. 保存更新后的服务信息

#### 3.2.3 日志功能

系统集成了自定义日志系统，包含以下功能：

- 将日志写入文件系统
- 支持不同的日志级别(INFO, ERROR等)
- 记录系统操作和异常信息
- 日志路径配置和管理

### 3.3 数据模型

主要涉及以下数据实体：

1. **DataReg**
   - 功能：表示各类数据记录(分钟级、日级、月级)
   - 主要属性：sdpId, tv, timeType等

2. **DataVEEEvent**
   - 功能：表示VEE事件记录
   - 主要属性：objectId, tv, eventId, schemeType等

3. **DataCalcProgress**
   - 功能：表示数据计算进度记录
   - 主要属性：sdpId, schemeId, loadType, tv等

4. **MdmSysService**
   - 功能：表示系统服务信息
   - 主要属性：id, serviceType, serverAddress, onlineTime等

### 3.4 消息队列设计

使用RocketMQ作为消息队列，主要配置：

- Producer Group: VEE_IMPORT
- Topic: VEE_DATAIMPORT
- Tags: VEE_DATA

发送的消息包含：

- 加载类型(loadType)：Register或VeeEvent
- 源服务ID(fromServiceId)
- 源服务类型(fromServiceType)：DATAIMPORT
- 源ID(fromId)：sdpId
- 源类型(fromType)：SDPID
- 数据负载(load)：具体的数据对象

### 3.5 业务流程图

#### 3.5.1 系统总体业务流程

```mermaid
graph TD
    A[系统启动] --> B[初始化服务]
    B --> C[启动日志系统]
    C --> D[加载配置信息]
    D --> E[注册并启动定时任务]
    E --> F[更新服务状态]
    F --> G[定时执行数据扫描]
    
    G --> H{是否有新数据?}
    H -->|是| I[处理数据并发送到MQ]
    H -->|否| J[等待下次扫描]
    I --> J
    J --> G
```

#### 3.5.2 数据扫描流程

```mermaid
graph TD
    A[开始扫描] --> B{检查数据库类型}
    B -->|Oracle| C[使用Oracle查询策略]
    B -->|MySQL| D[使用MySQL查询策略]
    
    C --> E[扫描月度数据]
    D --> E
    E --> F[扫描日度数据]
    F --> G[扫描分钟级数据]
    G --> H[扫描VEE事件数据]
    
    H --> I[结束扫描]
```

#### 3.5.3 数据处理流程

```mermaid
graph TD
    A[获取数据批次] --> B[转换为实体对象]
    B --> C{是否已处理过?}
    C -->|否| D[构建消息]
    C -->|是| E[跳过数据]
    D --> F[发送到RocketMQ]
    E --> G[处理下一批]
    F --> G
    G --> H{是否还有更多批次?}
    H -->|是| A
    H -->|否| I[完成数据处理]
```

#### 3.5.4 服务状态维护流程

```mermaid
graph TD
    A[启动服务状态定时器] --> B[每60秒触发一次]
    B --> C[从实时数据库获取服务信息]
    C --> D[更新服务状态和在线时间]
    D --> E[保存更新后的服务信息]
    E --> B
```

#### 3.5.5 与其他系统的交互流程

```mermaid
graph TD
    subgraph 数据源
        A1[Oracle数据库]
        A2[MySQL数据库]
    end
    
    subgraph DataScan服务
        B1[数据扫描模块]
        B2[消息发送模块]
        B3[服务状态管理]
    end
    
    subgraph 消息队列
        C1[RocketMQ]
    end
    
    subgraph 其他服务
        D1[DataCalc服务]
        D2[其他消费者服务]
    end
    
    A1 --> B1
    A2 --> B1
    B1 --> B2
    B2 --> C1
    C1 --> D1
    C1 --> D2
    B3 -.-> A1
    B3 -.-> A2
```

### 3.6 类对象关系图

#### 3.6.1 核心类图

```mermaid
classDiagram
    class DataScanApplication {
        +main(String[] args) void
        +restTemplate(RestTemplateBuilder builder) RestOperations
    }
    
    class InitCommandLineRunner {
        -ScheduleService scheduleService
        -MdmSysServiceRepository mdmSysServiceRepository
        -MdmSysServiceAttributeRepository mdmSysServiceAttributeRepository
        -int updateServiceCount
        -String scanStartTimeStr
        -int scanCycle
        -ServerConfig serverConfig
        -RtMdmSysServiceRepository rtMdmSysServiceRepository
        +run(String... args) void
        -initService(int serviceType) int
        -startLogger() int
        -scheduledServiceState() void
        -updateServiceState() void
    }
    
    class ServerConfig {
        -String serviceId
        -String serverAddress
        -int serverPort
        -String namesrvAddr
        +getServiceId() String
        +setServiceId(String serviceId) void
        +getServerAddress() String
        +getServerPort() int
        +getNamesrvAddr() String
        +loadCfg() void
        +isOracleDb() boolean
    }
    
    class ScheduleService {
        -CronTaskRegistrar cronTaskRegistrar
        +startSchedule(String id, String startTimeStr, int cycle, String beanName) void
    }
    
    class CronTaskRegistrar {
        -Map~String, ScheduledTask~ scheduledTasks
        +addCronTask(Runnable task, String cronExpression) void
        +removeCronTask(String key) void
        +destroy() void
    }
    
    class ScheduledTask {
        -ScheduledFuture~?~ future
        +cancel() void
        +task() ScheduledFuture
    }
    
    class SchedulingRunnable {
        -String beanName
        -String methodName
        -String params
        -ApplicationContext applicationContext
        +run() void
        +toString() String
    }
    
    class ScheduleScanTask {
        -JdbcTemplate jdbcTemplate
        -ServerConfig serverConfig
        -DataCalcProgressRepository DataCalcProgressRepository
        -ServiceProducer serviceProducer
        -String producerGroup
        -String producerTopic
        -String producerTags
        -DefaultMQProducer producer
        -boolean executing
        -SimpleDateFormat sdflog
        -Map~String, Date~ mapDataScanProgress
        -int scanMinute
        -int scanDay
        -int scanMonth
        -int scanEvent
        +init(String serviceId) void
        +dispatch() void
        -handleDataReg(ResultSet rs, String schemeId, int field, String timeType) long
        -scanDataOracle(String tableName, Date startTv, String schemeId, int field, String timeType) void
        -scanDataMysql(String tableName, Date startTv, String schemeId, int field, String timeType) void
        -handleEvent(ResultSet rs) long
        -scanEventOracle(Date startTv) void
        -scanEventMysql(Date startTv) void
        -sendMsg(String sdpId, String loadType, Object data) void
    }
    
    class DatabaseUtils {
        -static DatabaseUtils instance
        +getInstance() DatabaseUtils
        +queryForFetch(Connection conn, Statement st, String sql, int fetchSize) ResultSet
    }
    
    DataScanApplication ..> InitCommandLineRunner: creates
    InitCommandLineRunner --> ScheduleService: uses
    InitCommandLineRunner --> ServerConfig: configures
    InitCommandLineRunner --> MdmSysServiceRepository: queries
    InitCommandLineRunner --> RtMdmSysServiceRepository: queries
    ScheduleService --> CronTaskRegistrar: manages
    CronTaskRegistrar --> ScheduledTask: creates & manages
    ScheduledTask --> SchedulingRunnable: executes
    SchedulingRunnable ..> ScheduleScanTask: invokes
    ScheduleScanTask --> ServerConfig: uses
    ScheduleScanTask --> ServiceProducer: sends messages
    ScheduleScanTask --> DatabaseUtils: executes queries
    ScheduleScanTask --> DataCalcProgressRepository: queries
```

#### 3.6.2 数据实体关系图

```mermaid
classDiagram
    class DataReg {
        -DataRegPK dataRegPK
        -String timeType
        -String valueString
        -BigDecimal value
        -String status
        -String type
        -String sflag
        -int version
        -Date edt
        -String eby
        +getter/setter methods
    }
    
    class DataRegPK {
        -String sdpId
        -Date tv
        +getter/setter methods
    }
    
    class DataVEEEvent {
        -DataVEEEventPK dataVEEEventPK
        -String valueString
        -BigDecimal value
        -String status
        -int duration
        -String sflag
        -String remark
        -int version
        -Date edt
        -String eby
        +getter/setter methods
    }
    
    class DataVEEEventPK {
        -String objectId
        -Date tv
        -String eventId
        -String schemeType
        +getter/setter methods
    }
    
    class DataCalcProgress {
        -String sdpId
        -String schemeId
        -String loadType
        -Date tv
        -Date loadTime
        -String status
        +getter/setter methods
    }
    
    class MdmSysService {
        -String id
        -int serviceType
        -String serverAddress
        -int serverPort
        -Date onlineTime
        -String status
        +getter/setter methods
    }
    
    class MQMsg~T~ {
        -String loadType
        -String fromServiceId
        -String fromServiceType
        -String fromId
        -String fromType
        -String topic
        -String tags
        -T load
        +getter/setter methods
    }
    
    DataReg *-- DataRegPK: contains
    DataVEEEvent *-- DataVEEEventPK: contains
    ScheduleScanTask ..> DataReg: processes
    ScheduleScanTask ..> DataVEEEvent: processes
    ScheduleScanTask ..> DataCalcProgress: tracks
    ScheduleScanTask ..> MQMsg: creates
    InitCommandLineRunner ..> MdmSysService: updates
```

#### 3.6.3 存储库关系图

```mermaid
classDiagram
    class Repository {
        <<interface>>
    }
    
    class CrudRepository {
        <<interface>>
        +save(S entity) S
        +findById(ID id) Optional~T~
        +findAll() List~T~
        +delete(T entity) void
    }
    
    class DataCalcProgressRepository {
        <<interface>>
        +findAll() List~DataCalcProgress~
    }
    
    class MdmSysServiceRepository {
        <<interface>>
        +save(MdmSysService entity) MdmSysService
    }
    
    class MdmSysServiceAttributeRepository {
        <<interface>>
        +findValue(String serviceId, String attributeName) String
    }
    
    class RtMdmSysServiceRepository {
        <<interface>>
        +findById(String id) Optional~MdmSysService~
        +findServiceId(int serviceType, String serverAddress) String
        +findByServiceType(int serviceType) List~MdmSysService~
    }
    
    Repository <|-- CrudRepository
    CrudRepository <|-- DataCalcProgressRepository
    CrudRepository <|-- MdmSysServiceRepository
    CrudRepository <|-- RtMdmSysServiceRepository
    
    ScheduleScanTask --> DataCalcProgressRepository: uses
    InitCommandLineRunner --> MdmSysServiceRepository: uses
    InitCommandLineRunner --> MdmSysServiceAttributeRepository: uses
    InitCommandLineRunner --> RtMdmSysServiceRepository: uses
```

## 4. 接口设计

### 4.1 内部接口

#### 4.1.1 定时任务接口

```java
// 初始化定时任务
public void init(String serviceId)

// 执行扫描任务
public void dispatch()
```

#### 4.1.2 数据库接口

```java
// 扫描Oracle数据库中的数据
private void scanDataOracle(String tableName, Date startTv, String schemeId, int field, String timeType)

// 扫描MySQL数据库中的数据
private void scanDataMysql(String tableName, Date startTv, String schemeId, int field, String timeType)

// 扫描Oracle数据库中的事件
private void scanEventOracle(Date startTv)

// 扫描MySQL数据库中的事件
private void scanEventMysql(Date startTv)
```

#### 4.1.3 消息发送接口

```java
// 发送消息到RocketMQ
private void sendMsg(String sdpId, String loadType, Object data)
```

### 4.2 外部接口

通过Spring Boot提供RESTful API接口，具体接口由controller包中的类定义。

## 5. 配置管理

### 5.1 系统配置

系统配置主要存储在`application.yml`文件中，关键配置项：

```yaml
spring:
  application:
    name: DataScan  # 服务名称
  profiles:
    include: common        
  realtime:      
    datasource:
      jdbc-url: jdbc:h2:tcp://localhost:9799/mem:realtimedb;DB_CLOSE_ON_EXIT=FALSE
      driver-class-name: org.h2.Driver
      username: root
      password: 123 
server:
  port: 9806  # 端口
schedule:
  scanStartTime: 2020-01-01 00:57:00  # 扫描起始时间
  scanCycle: 1  # 扫描周期
  scanMinute: 7  # 分钟数据扫描范围(天)
  scanDay: 30  # 日数据扫描范围(天)
  scanMonth: 6  # 月数据扫描范围(月)
  scanEvent: 1  # 事件扫描范围(月)
```

### 5.2 配置项说明

| 配置项 | 说明 | 默认值 |
|-------|------|--------|
| spring.application.name | 服务名称 | DataScan |
| server.port | 服务端口 | 9806 |
| schedule.scanStartTime | 扫描起始时间 | 2020-01-01 00:57:00 |
| schedule.scanCycle | 扫描周期 | 1 |
| schedule.scanMinute | 分钟数据扫描范围(天) | 7 |
| schedule.scanDay | 日数据扫描范围(天) | 30 |
| schedule.scanMonth | 月数据扫描范围(月) | 6 |
| schedule.scanEvent | 事件扫描范围(月) | 1 |

## 6. 部署架构

### 6.1 部署环境要求

- JDK 1.8或以上
- MySQL 5.7或Oracle 11g以上
- RocketMQ 4.x
- 内存：至少4GB
- 磁盘空间：根据数据规模确定，建议至少50GB

### 6.2 部署步骤

1. 安装JDK 1.8+
2. 安装并配置数据库(MySQL或Oracle)
3. 安装并配置RocketMQ
4. 配置application.yml文件
5. 运行install.bat脚本
6. 检查服务是否正常启动

### 6.3 部署架构图

```
+----------------+      +----------------+      +----------------+
|                |      |                |      |                |
|  应用服务器    |----->|   数据库服务器  |      |  消息队列服务器 |
| (DataScan)     |      | (MySQL/Oracle) |      |  (RocketMQ)   |
|                |<-----|                |      |                |
+----------------+      +----------------+      +----------------+
        ^                                              ^
        |                                              |
        +----------------------------------------------+
```

## 7. 安全设计

### 7.1 数据安全

- 数据库访问使用加密的用户名和密码
- 敏感数据传输采用加密方式
- 定期备份关键数据

### 7.2 访问控制

- 服务间通信采用认证机制
- API接口调用需要权限验证
- 系统操作记录完整日志便于审计

## 8. 异常处理

### 8.1 常见异常

| 异常类型 | 处理方式 |
|---------|---------|
| 数据库连接异常 | 记录日志，尝试重连 |
| 消息发送失败 | 记录日志，设置重试机制 |
| 数据格式错误 | 记录日志，跳过错误数据 |
| 服务启动失败 | 记录日志，自动重启 |

### 8.2 异常恢复机制

- 对于临时性故障，实现自动重试机制
- 对于持久性故障，通过日志告警通知管理员
- 实现服务监控和自动恢复功能

## 9. 性能考虑

### 9.1 性能优化措施

- 数据库查询分批处理(每批100000条)
- 针对不同数据库(Oracle/MySQL)优化的查询语句
- 使用消息队列进行异步处理，避免阻塞
- 定时任务的串行化执行(避免并发冲突)
- 利用数据库索引提高查询效率

### 9.2 性能指标

| 指标 | 目标值 |
|------|-------|
| 单次数据扫描处理能力 | ≥100万条/分钟 |
| 系统CPU使用率 | <70% |
| 系统内存使用率 | <80% |
| 响应时间 | <1秒 |
| 系统可用性 | >99.9% |

## 10. 测试计划

### 10.1 测试类型

- **单元测试**：验证各模块功能的正确性
- **集成测试**：验证与其他服务的交互
- **性能测试**：验证大数据量下的系统性能
- **发布测试**：验证部署和配置的正确性

### 10.2 测试用例

| 测试场景 | 预期结果 |
|---------|---------|
| 系统启动 | 服务正常启动，初始化成功 |
| 数据扫描 | 按照指定周期和范围完成扫描 |
| 消息发送 | 成功发送消息到队列 |
| 异常处理 | 系统能正确处理各类异常 |
| 大数据量测试 | 系统在大数据量下稳定运行 |

## 11. 维护计划

### 11.1 日常维护

- 系统日志监控和分析
- 数据库性能监控
- 服务状态监控
- 定期数据备份

### 11.2 计划性维护

- 定期代码审查和优化
- 系统版本升级
- 安全漏洞修复
- 性能调优

## 12. 变更历史

| 版本号 | 日期 | 变更说明 | 责任人 |
|-------|------|---------|-------|
| 2024092310 | 2024-09-23 | 初始版本 | - |
| 2024092410 | 2024-09-24 | 增加业务流程图 | - |
| 2025032010 | 2025-03-20 | 增加类对象关系图 | - |

## 13. 附录

### 13.1 术语表

| 术语 | 全称 | 说明 |
|------|------|------|
| VEE | Validation, Estimation, and Editing | 验证、估算和编辑 |
| SDP | Service Delivery Point | 服务交付点 |
| MDM | Meter Data Management | 表计数据管理 |
| ClouESP | Cloud Energy Service Platform | 云能源服务平台 |

### 13.2 参考文档

- Spring Boot官方文档：https://spring.io/projects/spring-boot
- MyBatis官方文档：https://mybatis.org/mybatis-3/
- RocketMQ官方文档：http://rocketmq.apache.org/docs/
- Oracle/MySQL数据库文档

---

*文档创建日期：2024年9月23日*
*文档版本：1.0*