import clouesp.hes.common.CommonUtils.ObjectUtils
import clouesp.hes.common.DataEntity.Data.DataReg
import clouesp.hes.common.DataEntity.Data.DataVEEEvent

def regCurveDayDiff(Map<String, Object> ruleInfo) {
    Integer result = 0;
    DataVEEEvent event = null;
    if (ruleInfo == null ||
            !ruleInfo.containsKey("datas") ||
            !ruleInfo.containsKey("params")) {
        return null;
    }

    List<Map<String, Object>> ruleDatas = ruleInfo.get("datas");
    Map<String, Object> ruleParams = ruleInfo.get("params");
    double resultValue = 0;
    if(ruleParams != null && ruleParams.containsKey("resultValue")) {
        resultValue = ruleParams.get("resultValue");
    }
    for(Map<String, Object> ruleData : ruleDatas) {
        String targetClass = ruleData.get("targetClass");
        List<Map<String, Object>> dataDayList = ruleData.get("dataListDay");
        if(dataDayList == null) {
            return null;
        }
        List<Map<String, Object>> dataMinuteList = ruleData.get("dataListMinute");
        if(dataMinuteList == null) {
            return null;
        }
        if("REG" == targetClass) {
            if(dataDayList.size() > 0 &&
                    dataMinuteList.size() > 0) {
                Map<String, Object> dataDay = dataDayList.get(0);
                DataReg dataDayReg = ObjectUtils.convertMapToObject(dataDay, DataReg.class);
                Map<String, Object> dataMinute = dataMinuteList.get(0);
                DataReg dataMinuteReg = ObjectUtils.convertMapToObject(dataMinute, DataReg.class);
                Double r0p1Day = dataDayReg.getR0P1();
                Double r0p1Minute = dataMinuteReg.getR0P1();
                if(r0p1Day != null && r0p1Minute != null) {
                    if(Math.abs(r0p1Day.doubleValue() - r0p1Minute.doubleValue()) > resultValue) {
                        result = 1
                    }
                }
            }
        }
    }

    if(result.intValue() == 1) {
        event = (DataVEEEvent) ruleInfo.get("event");
    }
    return event;
}