package clouesp.hes.core.DataImport.handler;

import clouesp.hes.common.CommonUtils.ObjectUtils;
import clouesp.hes.common.CommonUtils.StringUtils;
import clouesp.hes.common.DataEntity.Data.DataInstMinutely;
import clouesp.hes.common.DataEntity.Data.DataReg;
import clouesp.hes.common.DataEntity.Data.MdmDataUpdateLog;
import clouesp.hes.common.DataEntity.Data.MdmDataUpdateLogPK;

import clouesp.hes.common.logger.logger.Logger;
import clouesp.hes.common.logger.logger.LoggerLevel;
import clouesp.hes.core.DataImport.config.ServerConfig;
import clouesp.hes.core.DataImport.service.Data.DataAccessService;
import clouesp.hes.core.DataImport.service.Data.MeterSdpMapService;
import clouesp.hes.core.DataImport.service.Data.RtDataAccessService;
import clouesp.hes.core.DataImport.utils.FileUtils;
import com.csvreader.CsvReader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.LinkedBlockingQueue;


/**
 * CSV文件数据导入处理器
 * 负责解析CSV文件并将数据保存到数据库中
 * 
 * 数据转换流程:
 * 1. CSV文件格式:
 *    - 文件名格式: {TIME_TYPE}_{TIMESTAMP}_DATA_{DATA_TYPE}_{VERSION}.csv
 *    - 时间类型: MINUTE(分钟)/DAY(日)/MONTH(月)
 *    - 数据类型: MD(表码)/LB(增量)/SL(瞬时量)
 * 
 * 2. 数据表对应关系:
 *    - 表码数据: mdm_data_reg_minutely/dayly/monthly
 *    - 增量数据: mdm_data_interval_minutely/dayly/monthly  
 *    - 瞬时量数据: mdm_data_inst_minutely
 *    - 更新日志: mdm_data_update_log
 * 
 * 3. 列映射规则:
 *    - 通用列: sdp_id(服务点ID), tv(时间戳)
 *    - 表码列: REG_前缀
 *    - 增量列: INTERVAL_前缀
 *    - 瞬时量列: INST_前缀
 */
@Slf4j
@Component
public class DataFileHandler {

	/**
	 * CSV文件表头信息内部类
	 * 用于存储列名和列索引的映射关系
	 */
	private class HeaderInfo {
		private int index;// 列索引
		private String name; // 列名(不含前缀)
		public int getIndex() {
			return index;
		}
		public void setIndex(int index) {
			this.index = index;
		}
		public String getName() {
			return name;
		}
		public void setName(String name) {
			this.name = name;
		}
	}
	
	// 数据访问服务，用于数据库操作
	@Resource(name="dataAccessService")
	private DataAccessService dataAccessService;

	// 实时数据访问服务，用于实时数据库操作
	@Resource(name="rtDataAccessService")
	private RtDataAccessService rtDataAccessService;

     // 服务器配置信息
	@Autowired
    private ServerConfig serverConfig;

	// 表计与数据点映射服务，用于将表计序列号转换为服务点ID
	@Autowired
	private MeterSdpMapService meterSdpMapService ;

	// 各类数据队列，用于存储待处理的数据
	// 数据保存采用队列+后台线程的方式，避免阻塞主处理流程
	private LinkedBlockingQueue<DataReg> dataRegMinutelyQueue = new LinkedBlockingQueue<DataReg>();  // 分钟级表码数据队列
	private LinkedBlockingQueue<DataReg> dataRegDailyQueue = new LinkedBlockingQueue<DataReg>();    // 日级表码数据队列
	private LinkedBlockingQueue<DataReg> dataRegMonthlyQueue = new LinkedBlockingQueue<DataReg>();  // 月级表码数据队列
	private LinkedBlockingQueue<DataReg> dataIntervalMinutelyQueue = new LinkedBlockingQueue<DataReg>();  // 分钟级增量数据队列
	private LinkedBlockingQueue<DataReg> dataIntervalDailyQueue = new LinkedBlockingQueue<DataReg>();    // 日级增量数据队列
	private LinkedBlockingQueue<DataReg> dataIntervalMonthlyQueue = new LinkedBlockingQueue<DataReg>();  // 月级增量数据队列
	private LinkedBlockingQueue<DataInstMinutely> dataInstMinutelyQueue = new LinkedBlockingQueue<DataInstMinutely>();  // 分钟级瞬时量数据队列
	private LinkedBlockingQueue<MdmDataUpdateLog> mdmDataUpdateLogQueue = new LinkedBlockingQueue<MdmDataUpdateLog>();  // 数据更新日志队列
	
	// 数据保存处理线程相关变量
	private DataSaveProc dataSaveProc = null;
	private Thread dataSaveProcThread = null;	

	// 记录各类数据最后保存时间，用于控制保存频率
	private long lastSaveRegMinutelyTime = System.currentTimeMillis();
	private long lastSaveRegDailyTime = System.currentTimeMillis();
	private long lastSaveRegMonthlyTime = System.currentTimeMillis();
	
	private long lastSaveIntervalMinutelyTime = System.currentTimeMillis();
	private long lastSaveIntervalDailyTime = System.currentTimeMillis();
	private long lastSaveIntervalMonthlyTime = System.currentTimeMillis();
	
	private long lastSaveInstTime = System.currentTimeMillis();
	
	private long lastSaveLogTime = System.currentTimeMillis();
				
    /**
	 * 根据文件路径判断时间类型
	 * 通过文件名中的时间类型标识(MINUTE/DAY/MONTH)来确定
	 * 
	 * @param filePath 文件路径
	 * @return 时间类型: DAY(日)/MINUTE(分钟)/MONTH(月)
	 * 
	 * 示例:
	 * 输入: /path/to/MINUTE_1234567890_DATA_MD_1.csv
	 * 输出: MINUTE
	 */
	private String getTimeType(String filePath) {
		String timeType = null;
		if(filePath.indexOf("DAY_") != -1)
			timeType = "DAY";
		else if(filePath.indexOf("MINUTE_") != -1)
			timeType = "MINUTE";
		else if(filePath.indexOf("MONTH_") != -1)
			timeType = "MONTH";
		return timeType;
	}
	
		/**
	 * 初始化数据保存处理线程
	 * 使用单例模式确保只有一个保存线程在运行
	 * 
	 * 数据保存策略:
	 * 1. 每种数据类型使用独立的队列
	 * 2. 当队列数据量达到阈值或时间间隔达到阈值时触发保存
	 * 3. 保存失败时会将数据重新入队
	 */
	public void init(String serviceId) {	
		if(dataSaveProcThread == null) {
			dataSaveProc = new DataSaveProc();
			dataSaveProcThread = new Thread(dataSaveProc);
			dataSaveProcThread.start();				
		}
	}
	
	/**
	 * 保存表码数据到数据库
	 * 根据时间类型选择对应的数据表进行保存
	 * 
	 * @param timeType 时间类型: MINUTE/DAY/MONTH
	 * @param dataRegs 表码数据列表
	 * 
	 * 数据保存流程:
	 * 1. 数据去重
	 * 2. 根据时间类型选择数据表
	 * 3. 将数据加入对应队列
	 * 4. 尝试批量插入
	 * 5. 插入失败时尝试更新
	 * 
	 * 列映射示例:
	 * CSV列 -> 数据库字段
	 * REG_voltage_a -> voltageA
	 * REG_voltage_b -> voltageB
	 * REG_voltage_c -> voltageC
	 * REG_current_a -> currentA
	 * REG_current_b -> currentB
	 * REG_current_c -> currentC
	 */
	  
	private void saveRegDB(String timeType, List<DataReg> dataRegs) {
		dataRegs = removeDuplicate(dataRegs);
		try {
			// 根据时间类型选择对应的数据表
			if ("DAY".equals(timeType)) {
				dataRegDailyQueue.addAll(dataRegs);
				boolean ret = rtDataAccessService.batchSaveReg("mdm_data_reg_dayly", dataRegs);
				if (!ret) {
					rtDataAccessService.batchUpdateSaveReg("mdm_data_reg_dayly", dataRegs);
				}
			} else if ("MINUTE".equals(timeType)) {
				dataRegMinutelyQueue.addAll(dataRegs);
				boolean ret = rtDataAccessService.batchSaveReg("mdm_data_reg_minutely", dataRegs);
				if (!ret) {
					rtDataAccessService.batchUpdateSaveReg("mdm_data_reg_minutely", dataRegs);
				}
			} else if ("MONTH".equals(timeType)) {
				dataRegMonthlyQueue.addAll(dataRegs);
				boolean ret = rtDataAccessService.batchSaveReg("mdm_data_reg_monthly", dataRegs);
				if (!ret) {
					rtDataAccessService.batchUpdateSaveReg("mdm_data_reg_monthly", dataRegs);
				}
			}
		} catch (Exception e) {
			log.error("DataImport Error : " , e);
		}
	}
	
	/**
	 * 保存增量数据到数据库
	 * 根据时间类型选择对应的数据表进行保存
	 * 
	 * @param timeType 时间类型: MINUTE/DAY/MONTH
	 * @param dataRegs 增量数据列表
	 * 
	 * 数据保存流程:
	 * 1. 数据去重
	 * 2. 根据时间类型选择数据表
	 * 3. 将数据加入对应队列
	 * 4. 尝试批量插入
	 * 5. 插入失败时尝试更新
	 * 
	 * 列映射示例:
	 * CSV列 -> 数据库字段
	 * INTERVAL_voltage_a -> voltageA
	 * INTERVAL_voltage_b -> voltageB
	 * INTERVAL_voltage_c -> voltageC
	 */
	private void saveIntervalDB(String timeType, List<DataReg> dataIntervals) {
		dataIntervals = removeDuplicate(dataIntervals);
		try {
			// 根据时间类型选择对应的数据表
			if ("DAY".equals(timeType)) {
				dataIntervalDailyQueue.addAll(dataIntervals);
				boolean ret = rtDataAccessService.batchSaveReg("mdm_data_interval_dayly", dataIntervals);
				if (!ret) {
					rtDataAccessService.batchUpdateSaveReg("mdm_data_interval_dayly", dataIntervals);
				}
			} else if ("MINUTE".equals(timeType)) {
				dataIntervalMinutelyQueue.addAll(dataIntervals);
				boolean ret = rtDataAccessService.batchSaveReg("mdm_data_interval_minutely", dataIntervals);
				if (!ret) {
					rtDataAccessService.batchUpdateSaveReg("mdm_data_interval_minutely", dataIntervals);
				}
			} else if ("MONTH".equals(timeType)) {
				dataIntervalMonthlyQueue.addAll(dataIntervals);
				boolean ret = rtDataAccessService.batchSaveReg("mdm_data_interval_monthly", dataIntervals);
				if (!ret) {
					rtDataAccessService.batchUpdateSaveReg("mdm_data_interval_monthly", dataIntervals);
				}
			}
		} catch (Exception e) {
			log.error("DataImport Error : " , e);
		}
	}	
	
	/**
	 * 保存瞬时量数据到数据库
	 * 瞬时量数据只保存到分钟级表
	 * 
	 * @param dataInstMinutelys 瞬时量数据列表
	 * 
	 * 数据保存流程:
	 * 1. 数据去重
	 * 2. 将数据加入队列
	 * 3. 尝试批量插入
	 * 4. 插入失败时尝试更新
	 * 
	 * 列映射示例:
	 * CSV列 -> 数据库字段
	 * INST_voltage_a -> voltageA
	 * INST_voltage_b -> voltageB
	 * INST_voltage_c -> voltageC
	 */
	private void saveInstDB(List<DataInstMinutely> dataInstMinutelys) {
		// 使用TreeSet进行数据去重，基于sdpId和tv构建唯一键
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Set<DataInstMinutely> set = new TreeSet<DataInstMinutely>(new Comparator<DataInstMinutely>() {
			@Override
			public int compare(DataInstMinutely o1, DataInstMinutely o2) {
				String o1String = o1.getDataInstPK().getSdpId() + "#";
				o1String += (sdf.format(o1.getDataInstPK().getTv()) + "#");
				
				String o2String = o2.getDataInstPK().getSdpId() + "#";
				o2String += (sdf.format(o2.getDataInstPK().getTv()) + "#");
				return o1String.compareTo(o2String); 
			}
        
        });
        set.addAll(dataInstMinutelys);
        dataInstMinutelys = new ArrayList<DataInstMinutely>(set);	
        try {
			dataInstMinutelyQueue.addAll(dataInstMinutelys);
			boolean ret = rtDataAccessService.batchSaveInsts("mdm_data_inst_minutely", dataInstMinutelys);
			if (!ret) {
				rtDataAccessService.batchUpdateSaveInsts("mdm_data_inst_minutely", dataInstMinutelys);
			}
		} catch (Exception e) {
			log.error("DataImport Error : " , e);
		}
	}

	/**
	 * 从CSV行数据构造DataReg对象
	 * 将CSV列值映射到DataReg对象的属性
	 * 
	 * @param headerInfos 表头信息列表
	 * @param values CSV行数据
	 * @param sdpId 服务点ID
	 * @param timeType 时间类型
	 * @param dataType 数据类型
	 * @return DataReg对象
	 * 
	 * 数据转换流程:
	 * 1. 创建DataReg对象
	 * 2. 设置主键信息(sdpId, tv)
	 * 3. 根据列前缀(REG_/INTERVAL_)映射数据字段
	 * 4. 设置更新时间
	 * 
	 * 列映射示例:
	 * CSV列 -> DataReg属性
	 * REG_voltage_a -> voltageA
	 * REG_current_a -> currentA
	 * REG_power_factor_a -> powerFactorA
	 */
	private DataReg getDataReg(
			List<HeaderInfo> headerInfos, 
			String[] values, 
			String sdpId, 
			String timeType,
			String dataType) {
		DataReg dataReg = new DataReg();
		dataReg.setTimeType(timeType);
		// 遍历表头信息，设置DataReg对象的属性
		for (HeaderInfo headerInfo : headerInfos) {
			String fieldName = StringUtils.lineToHump(headerInfo.getName());
			String value = values[headerInfo.getIndex()];
			if("sdpId".equals(fieldName) || "tv".equals(fieldName)) {
				ObjectUtils.invokeSet(dataReg.getDataRegPK(), fieldName, value);
			} 
			else {
				ObjectUtils.invokeSet(dataReg, fieldName, value);
			}
		}
		dataReg.getDataRegPK().setSdpId(sdpId);
		dataReg.setUpdateTv(new Date());
		return dataReg;
	}
	
	/**
	 * 从CSV行数据构造DataInstMinutely对象
	 * 将CSV列值映射到DataInstMinutely对象的属性
	 * 
	 * @param headerInfos 表头信息列表
	 * @param values CSV行数据
	 * @param sdpId 服务点ID
	 * @return DataInstMinutely对象
	 * 
	 * 数据转换流程:
	 * 1. 创建DataInstMinutely对象
	 * 2. 设置主键信息(sdpId, tv)
	 * 3. 根据列前缀(INST_)映射数据字段
	 * 4. 设置更新时间
	 * 
	 * 列映射示例:
	 * CSV列 -> DataInstMinutely属性
	 * INST_voltage_a -> voltageA
	 * INST_current_a -> currentA
	 */
	private DataInstMinutely getDataInstMinutely(List<HeaderInfo> headerInfos, String[] values, String sdpId,
			String dataType) {
		DataInstMinutely dataInstMinutely = new DataInstMinutely();
		// 遍历表头信息，设置DataInstMinutely对象的属性
		for (HeaderInfo headerInfo : headerInfos) {
			String fieldName = StringUtils.lineToHump(headerInfo.getName());
			String value = values[headerInfo.getIndex()];
			if ("sdpId".equals(fieldName) || "tv".equals(fieldName) || "dataType".equals(fieldName)) {
				ObjectUtils.invokeSet(dataInstMinutely.getDataInstPK(), fieldName, value);
			} else {
				ObjectUtils.invokeSet(dataInstMinutely, fieldName, value);
			}
		}
		dataInstMinutely.getDataInstPK().setSdpId(sdpId);
		dataInstMinutely.setUpdateTv(new Date());
		return dataInstMinutely;
	}
	
	/**
	 * 获取sdp_id列在CSV表头中的索引位置
	 * 
	 * @param headers CSV表头数组
	 * @return sdp_id列的索引位置
	 * 
	 * 示例:
	 * 输入: ["sdp_id", "tv", "REG_voltage_a", ...]
	 * 输出: 0
	 */
	private int getSdpIdPos(String[] headers) {
		int sdpIdPos = 0;
		int index = 0;
		for (String header: headers) {
			if("sdp_id".equals(header) || "meter_sn".equals(header)) {
				sdpIdPos = index;
				break;
			}
		}
		return sdpIdPos;
	}
	
	/**
	 * 解析CSV表头信息，将列按类型分类
	 * @param headers CSV表头数组
	 * @param dataType 数据类型
	 * @return 按类型分类的表头信息Map
	 * 
	 * 表头分类规则:
	 * 1. 通用列(无前缀):
	 *    - sdp_id
	 *    - tv
	 *    - update_tv
	 * 
	 * 2. 表码列(REG_前缀):
	 *    - REG_voltage_a
	 *    - REG_current_a
	 *    - REG_power_total
	 *    ...
	 */
	private Map<String, List<HeaderInfo>> getMapHeaderInfo(String[] headers, String dataType) {
		List<HeaderInfo> commonHeaderInfos = new ArrayList<HeaderInfo>();
		Map<String, List<HeaderInfo>> mapHeaderInfo = new HashMap<String, List<HeaderInfo>>();
		int index = 0;
	
		for (String header: headers) {
			HeaderInfo headerInfo = new HeaderInfo();
			headerInfo.setIndex(index);
			String[] headerSplits = new String[2];
			int pos = header.indexOf("_");
			if (pos != -1) {
				headerSplits[0] = header.substring(0, pos);
				headerSplits[1] = header.substring(pos + 1);
			}
			// 根据列名前缀分类处理
			if (pos != -1
				&& (!"sdp".equals(headerSplits[0]) 
				&& !"update".equals(headerSplits[0]))) {
					headerInfo.setName(headerSplits[1]);
					if (mapHeaderInfo.containsKey(headerSplits[0])) {
						mapHeaderInfo.get(headerSplits[0]).add(headerInfo);
					} else {
						List<HeaderInfo> headerInfos = new ArrayList<HeaderInfo>();
						headerInfos.add(headerInfo);
						mapHeaderInfo.put(headerSplits[0], headerInfos);
					}
				
			} else {
				headerInfo.setName(header);
				commonHeaderInfos.add(headerInfo);
			}
			index++;
		}
		// 将通用列添加到每个类型中
		if (mapHeaderInfo.size() > 0) {
			for (Entry<String, List<HeaderInfo>> entry : mapHeaderInfo.entrySet()) {
				List<HeaderInfo> headerInfos = entry.getValue();
				headerInfos.addAll(0, commonHeaderInfos);
			}
		} else if (dataType != null) {
			List<HeaderInfo> headerInfos = new ArrayList<HeaderInfo>();
			headerInfos.addAll(0, commonHeaderInfos);
			mapHeaderInfo.put(dataType, headerInfos);
		}
		
		return mapHeaderInfo;
	}
	
		/**
	 * 处理CSV文件
	 * @param filePath 文件路径
	 * @param dataType 数据类型
	 */
	public void handleCsvFiles(String filePath, String dataType) {
		try {

			log.info("Start processing data files : " + filePath);
			List<String> errorMeterSdps = new ArrayList<>() ;

			// 获取时间类型
			String timeType = getTimeType(filePath);
			// 读取CSV文件
			CsvReader csvReader = new CsvReader(filePath, ',', Charset.forName("UTF-8"));
			csvReader.readHeaders();
			String[] headers = csvReader.getHeaders();
			int sdpIdPos = getSdpIdPos(headers);
			Map<String, List<HeaderInfo>> mapHeaderInfo = getMapHeaderInfo(headers, dataType);
			
			// 初始化数据列表
			List<DataReg> dataRegs = new ArrayList<DataReg>();	
			List<DataReg> dataIntervals = new ArrayList<DataReg>();
			List<DataInstMinutely> dataInstMinutelys = new ArrayList<DataInstMinutely>();
			
			// 设置方案类型
			int schemeType = 0;
			if("MINUTE".equals(timeType)) {
				schemeType = 1;
			}
			else if("DAY".equals(timeType)) { 
				schemeType = 2;
			}
			else if("MONTH".equals(timeType)) {
				schemeType = 3;
			}

			int totalRecord = 0 ;

			// 逐行读取CSV数据
			String curSdpId = null ;
			while (csvReader.readRecord()) {
				totalRecord++ ;
				String[] values = csvReader.getValues();
				String sn = values[sdpIdPos];

				curSdpId = meterSdpMapService.getSdpIdbyMeterSn(sn) ;
				if ( curSdpId == null ){
					if ( errorMeterSdps.isEmpty() || Collections.binarySearch(errorMeterSdps , sn  ) < 0 ) {
						errorMeterSdps.add(sn);
						Collections.sort(errorMeterSdps);
					}
					continue;
				}

				// 根据数据类型处理数据
				for (Entry<String, List<HeaderInfo>> entry : mapHeaderInfo.entrySet()) {
					List<HeaderInfo> headerInfos = entry.getValue();
					if(values.length < headerInfos.size()) {
						continue;
					}

					switch(entry.getKey()) {
					case "REG":
						DataReg dataReg = getDataReg(
								headerInfos, values, curSdpId, timeType, dataType);
						dataRegs.add(dataReg);
						break;
					case "INTERVAL":
						DataReg dataInterval = getDataReg(
								headerInfos, values, curSdpId, timeType, dataType);
						dataIntervals.add(dataInterval);	
						break;
					case "INST":
						DataInstMinutely dataInstMinutely = getDataInstMinutely(
								headerInfos, values,curSdpId, dataType);
						dataInstMinutelys.add(dataInstMinutely);
						break;

						default:
							log.warn("Can not find data type :  Key = " + entry.getKey() + " , headerInfos.Size = " + headerInfos.size());
					}
				}
			}

			if ( errorMeterSdps.size() >0 ){
				log.warn("Can not find data ServicePoint: FileName = " + filePath + " , Meter Count = " + errorMeterSdps.size() + "( ValidDateCount = " + (totalRecord  - errorMeterSdps.size()));
				for (String sn : errorMeterSdps) {
					log.warn("Meter SN = " + sn);
				}
			}
           // 记录处理结果
			int importRecord = dataRegs.size() +  dataIntervals.size() + dataInstMinutelys.size();
			int pos = filePath.lastIndexOf("/");
			if(pos == -1) {
				pos = filePath.lastIndexOf("\\");
			}

			String fileName = filePath.substring(pos + 1);
			String loggerInfo = "File Name = " + fileName  + ", " + "Total Record = " +  totalRecord + ", " + "Import Record = " +  importRecord;
			log.info(loggerInfo);

			// 关闭CSV读取器并备份文件
			csvReader.close();	
			FileUtils.getInstance().backupFile(filePath);

			// 保存表码数据
			if (dataRegs.size() > 0) {
				saveRegDB(timeType, dataRegs);
				// 记录数据更新日志
				Map<String, Date> mapLog = new HashMap<String, Date>();
				
				for (DataReg dataReg : dataRegs) {
					String key = dataReg.getDataRegPK().getSdpId();
					key += "\r\n";
					key += timeType;
					mapLog.put(key, dataReg.getDataRegPK().getTv());
				}
				Date lastUpdateTime = new Date();
				for(Entry<String, Date> entry : mapLog.entrySet()) {
					String[] splitKeys = entry.getKey().split("\r\n");
					String sdpId = splitKeys[0];
					
					MdmDataUpdateLog mdmDataUpdateLog = new MdmDataUpdateLog();
					MdmDataUpdateLogPK mdmDataUpdateLogPK = mdmDataUpdateLog.getMdmDataUpdateLogPK();
					mdmDataUpdateLogPK.setSdpId(sdpId);
					mdmDataUpdateLogPK.setSchemeType(schemeType);
					mdmDataUpdateLogPK.setDataType(101);
					mdmDataUpdateLog.setLastUpdateTime(lastUpdateTime);
					mdmDataUpdateLog.setLastDataTime(entry.getValue());
					mdmDataUpdateLogQueue.put(mdmDataUpdateLog);
				}
			}

			// 保存增量数据
			if (dataIntervals.size() > 0) {
				saveIntervalDB(timeType, dataIntervals);
				Map<String, Date> mapLog = new HashMap<String, Date>();
				for (DataReg dataInterval : dataIntervals) {
					String key = dataInterval.getDataRegPK().getSdpId();
					key += "\r\n";
					key += timeType;
					mapLog.put(key, dataInterval.getDataRegPK().getTv());					
				}
				Date lastUpdateTime = new Date();
				for(Entry<String, Date> entry : mapLog.entrySet()) {
					String[] splitKeys = entry.getKey().split("\r\n");
					String sdpId = splitKeys[0];
					
					MdmDataUpdateLog mdmDataUpdateLog = new MdmDataUpdateLog();
					MdmDataUpdateLogPK mdmDataUpdateLogPK = mdmDataUpdateLog.getMdmDataUpdateLogPK();
					mdmDataUpdateLogPK.setSdpId(sdpId);
					mdmDataUpdateLogPK.setSchemeType(schemeType);
					mdmDataUpdateLogPK.setDataType(102);
					mdmDataUpdateLog.setLastUpdateTime(lastUpdateTime);
					mdmDataUpdateLog.setLastDataTime(entry.getValue());
					mdmDataUpdateLogQueue.put(mdmDataUpdateLog);
				}		
			}

			// 保存瞬时量数据
			if (dataInstMinutelys.size() > 0) {
				saveInstDB(dataInstMinutelys);
				// 记录数据更新日志
				Map<String, Date> mapLog = new HashMap<String, Date>();
				for (DataInstMinutely dataInstMinutely : dataInstMinutelys) {
					String key = dataInstMinutely.getDataInstPK().getSdpId();
					key += "\r\n";
					key += timeType;
					mapLog.put(key, dataInstMinutely.getDataInstPK().getTv());						
				}
				Date lastUpdateTime = new Date();
				for(Entry<String, Date> entry : mapLog.entrySet()) {
					String[] splitKeys = entry.getKey().split("\r\n");
					String sdpId = splitKeys[0];
					
					MdmDataUpdateLog mdmDataUpdateLog = new MdmDataUpdateLog();
					MdmDataUpdateLogPK mdmDataUpdateLogPK = mdmDataUpdateLog.getMdmDataUpdateLogPK();
					mdmDataUpdateLogPK.setSdpId(sdpId);
					mdmDataUpdateLogPK.setSchemeType(schemeType);
					mdmDataUpdateLogPK.setDataType(103);
					mdmDataUpdateLog.setLastUpdateTime(lastUpdateTime);
					mdmDataUpdateLog.setLastDataTime(entry.getValue());
					mdmDataUpdateLogQueue.put(mdmDataUpdateLog);
				}	
			}
			log.info("End processing data files : " + filePath);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			//e.printStackTrace();
			log.error("DataImport Error : " , e);
		}
	}
	
	/**
	 * 对DataReg列表进行去重处理
	 * @param datas 待去重的DataReg列表
	 * @return 去重后的DataReg列表
	 * 
	 * 去重规则:
	 * 1. 基于sdpId和tv构建唯一键
	 * 2. 使用TreeSet进行去重
	 * 3. 保持数据的时间顺序
	 * 
	 * 示例:
	 * 输入: [
	 *   {sdpId: "001", tv: "2025-01-07 01:47:16", ...},
	 *   {sdpId: "001", tv: "2025-01-07 01:47:16", ...},  // 重复数据
	 *   {sdpId: "002", tv: "2025-01-07 01:47:16", ...}
	 * ]
	 * 输出: [
	 *   {sdpId: "001", tv: "2025-01-07 01:47:16", ...},
	 *   {sdpId: "002", tv: "2025-01-07 01:47:16", ...}
	 * ]
	 */
	private List<DataReg> removeDuplicate(List<DataReg> datas) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Set<DataReg> set = new TreeSet<DataReg>(new Comparator<DataReg>() {
			@Override
			public int compare(DataReg o1, DataReg o2) {
				String o1String = o1.getDataRegPK().getSdpId() + "#";
				o1String += (sdf.format(o1.getDataRegPK().getTv()) + "#");
				
				String o2String = o2.getDataRegPK().getSdpId() + "#";
				o2String += (sdf.format(o2.getDataRegPK().getTv()) + "#");
				return o1String.compareTo(o2String); 
			}
        
        });
        set.addAll(datas);
        datas = new ArrayList<DataReg>(set);	
        return datas;
	}
	

	/**
	 * 数据保存处理线程内部类
	 * 负责从各个数据队列中获取数据并保存到数据库
	 * 
	 * 数据保存策略:
	 * 1. 每种数据类型使用独立的队列
	 * 2. 当队列数据量达到阈值或时间间隔达到阈值时触发保存
	 * 3. 保存失败时会将数据重新入队
	 * 4. 使用批量操作提高性能
	 * 
	 * 保存触发条件:
	 * 1. 队列数据量达到阈值(默认1000条)
	 * 2. 距离上次保存时间超过阈值(默认5秒)
	 */
	private class DataSaveProc implements Runnable{
		/**
		 * 保存分钟级表码数据
		 * @param count 保存数量
		 * @return 实际保存数量
		 */
		private int dataMinutelySaveReg(int count) {
			if(dataRegMinutelyQueue.size() == 0)
				return 0;
			if(System.currentTimeMillis() - lastSaveRegMinutelyTime < 15000
				&& dataRegMinutelyQueue.size() < 100)
				return 0;
			if(count > dataRegMinutelyQueue.size()) 
				count = dataRegMinutelyQueue.size();
			lastSaveRegMinutelyTime = System.currentTimeMillis();
			List<DataReg> datas = new ArrayList<DataReg>();
			dataRegMinutelyQueue.drainTo(datas, count);
			try {
				long startTime = System.currentTimeMillis();		
				datas = removeDuplicate(datas);

				String loggerInfo = "Insert";
				boolean ret = dataAccessService.batchSaveReg("mdm_data_reg_minutely", datas);
				if(!ret) {
					loggerInfo = "Update";
					ret = dataAccessService.batchUpdateSaveReg("mdm_data_reg_minutely", datas);
				}

				loggerInfo	+= (" mdm_data_reg_minutely[count: " + count + ", time: " + (System.currentTimeMillis() - startTime) + "]");

				log.info(loggerInfo);
				Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "Database", loggerInfo); 

			} catch (Exception e) {
//				e.printStackTrace();
				log.error("DataImport Error : " , e);
				dataRegMinutelyQueue.addAll(datas);
			}
			return count;
		}
		
		/**
		 * 保存日级表码数据
		 * @param count 保存数量
		 * @return 实际保存数量
		 */
		private int dataDailySaveReg(int count) {
			if (dataRegDailyQueue.size() == 0)
				return 0;
			if (System.currentTimeMillis() - lastSaveRegDailyTime < 15000
					&& dataRegDailyQueue.size() < 100)
				return 0;

			if(count > dataRegDailyQueue.size()) 
				count = dataRegDailyQueue.size();
			lastSaveRegDailyTime = System.currentTimeMillis();
			List<DataReg> datas = new ArrayList<DataReg>();
			dataRegDailyQueue.drainTo(datas, count);
			try {
				long startTime = System.currentTimeMillis();
				datas = removeDuplicate(datas);

				String loggerInfo = "Insert";
				boolean ret = dataAccessService.batchSaveReg("mdm_data_reg_dayly", datas);
				if(!ret) {
					loggerInfo = "Update";
					dataAccessService.batchUpdateSaveReg("mdm_data_reg_dayly", datas);
				}

				loggerInfo += (" mdm_data_reg_dayly[count: " + count + ", time: "
						+ (System.currentTimeMillis() - startTime) + "]");

				log.info(loggerInfo);

				Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "Database", loggerInfo); 
			} catch (Exception e) {
				log.error("DataImport Error : " , e);
				dataRegDailyQueue.addAll(datas);
			}
			return count;
		}		
		
		/**
		 * 保存月级表码数据
		 * @param count 保存数量
		 * @return 实际保存数量
		 */
		private int dataMonthlySaveReg(int count) {
			if(dataRegMonthlyQueue.size() == 0)
				return 0;
			if(System.currentTimeMillis() - lastSaveRegMonthlyTime < 15000
				&& dataRegMonthlyQueue.size() < 100)
				return 0;
			if(count > dataRegMonthlyQueue.size()) 
				count = dataRegMonthlyQueue.size();
			lastSaveRegMonthlyTime = System.currentTimeMillis();
			List<DataReg> datas = new ArrayList<DataReg>();
			dataRegMonthlyQueue.drainTo(datas, count);
			try {
				long startTime = System.currentTimeMillis();
				datas = removeDuplicate(datas);
				String loggerInfo = "Insert";
				boolean ret = dataAccessService.batchSaveReg("mdm_data_reg_monthly", datas);
				if(!ret) {
					loggerInfo = "Update";
					dataAccessService.batchUpdateSaveReg("mdm_data_reg_monthly", datas);
				}

				loggerInfo += (" mdm_data_reg_monthly[count: " + count + ", time: "
						+ (System.currentTimeMillis() - startTime) + "]");
				log.info(loggerInfo);

				Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "Database", loggerInfo); 
			} catch (Exception e) {
				log.error("DataImport Error : " , e);
				dataRegMonthlyQueue.addAll(datas);
			}
			return count;
		}
		
		/**
		 * 保存分钟级增量数据
		 * @param count 保存数量
		 * @return 实际保存数量
		 */
		private int dataMinutelySaveInterval(int count) {
			if(dataIntervalMinutelyQueue.size() == 0)
				return 0;
			if(System.currentTimeMillis() - lastSaveIntervalMinutelyTime < 15000
				&& dataIntervalMinutelyQueue.size() < 100)
				return 0;
			if(count > dataIntervalMinutelyQueue.size()) 
				count = dataIntervalMinutelyQueue.size();
			lastSaveIntervalMinutelyTime = System.currentTimeMillis();
			List<DataReg> datas = new ArrayList<DataReg>();
			dataIntervalMinutelyQueue.drainTo(datas, count);
			try {
				long startTime = System.currentTimeMillis();
				datas = removeDuplicate(datas);

				String loggerInfo = "Insert";
				boolean ret = dataAccessService.batchSaveReg("mdm_data_interval_minutely", datas);
				if(!ret) {
					loggerInfo = "Update";
					dataAccessService.batchUpdateSaveReg("mdm_data_interval_minutely", datas);
				}

				loggerInfo += (" mdm_data_interval_minutely[count: " + count + ", time: "
						+ (System.currentTimeMillis() - startTime) + "]");
				log.info(loggerInfo);
				Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "Database", loggerInfo);
			} catch (Exception e) {
				log.error("DataImport Error : " , e);
				dataIntervalMinutelyQueue.addAll(datas);
			}
			return count;
		}
		
		/**
		 * 保存日级增量数据
		 * @param count 保存数量
		 * @return 实际保存数量
		 */
		private int dataDailySaveInterval(int count) {
			if(dataIntervalDailyQueue.size() == 0)
				return 0;
			if(System.currentTimeMillis() - lastSaveIntervalDailyTime < 15000
				&& dataIntervalDailyQueue.size() < 100)
				return 0;
			if(count > dataIntervalDailyQueue.size()) 
				count = dataIntervalDailyQueue.size();
			lastSaveIntervalDailyTime = System.currentTimeMillis();
			List<DataReg> datas = new ArrayList<DataReg>();
			dataIntervalDailyQueue.drainTo(datas, count);
			try {
				long startTime = System.currentTimeMillis();
				datas = removeDuplicate(datas);
				String loggerInfo = "Insert";
				boolean ret = dataAccessService.batchSaveReg("mdm_data_interval_dayly", datas);
				if(!ret) {
					loggerInfo = "Update";
					dataAccessService.batchUpdateSaveReg("mdm_data_interval_dayly", datas);
				}

				loggerInfo += (" mdm_data_interval_dayly[count: " + count + ", time: "
						+ (System.currentTimeMillis() - startTime) + "]");
				log.info(loggerInfo);
				Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "Database", loggerInfo);
			} catch (Exception e) {
				log.error("DataImport Error : " , e);
				dataIntervalDailyQueue.addAll(datas);
			}
			return count;
		}		
		
		/**
		 * 保存月级增量数据
		 * @param count 保存数量
		 * @return 实际保存数量
		 */
		private int dataMonthlySaveInterval(int count) {
			if(dataIntervalMonthlyQueue.size() == 0)
				return 0;
			if(System.currentTimeMillis() - lastSaveIntervalMonthlyTime < 15000
				&& dataIntervalMonthlyQueue.size() < 100)
				return 0;
			if(count > dataIntervalMonthlyQueue.size()) 
				count = dataIntervalMonthlyQueue.size();
			lastSaveIntervalMonthlyTime = System.currentTimeMillis();
			List<DataReg> datas = new ArrayList<DataReg>();
			dataIntervalMonthlyQueue.drainTo(datas, count);
			try {
				long startTime = System.currentTimeMillis();
				datas = removeDuplicate(datas);
				String loggerInfo = "Insert";
				boolean ret = dataAccessService.batchSaveReg("mdm_data_interval_monthly", datas);
				if(!ret) {
					loggerInfo = "Update";
					dataAccessService.batchUpdateSaveReg("mdm_data_interval_monthly", datas);
				}

				loggerInfo += (" mdm_data_interval_monthly[count: " + count + ", time: "
						+ (System.currentTimeMillis() - startTime) + "]");
				log.info(loggerInfo);
				Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "Database", loggerInfo); 
			} catch (Exception e) {
				log.error("DataImport Error : " , e);
				dataIntervalMonthlyQueue.addAll(datas);
			}
			return count;
		}		
		
		/**
		 * 保存瞬时量数据
		 * @param count 保存数量
		 * @return 实际保存数量
		 */
		private int dataInstSave(int count) {
			if(dataInstMinutelyQueue.size() == 0)
				return 0;
			if(System.currentTimeMillis() - lastSaveInstTime < 15000
				&& dataInstMinutelyQueue.size() < 100)
				return 0;
			if(count > dataInstMinutelyQueue.size()) 
				count = dataInstMinutelyQueue.size();
			lastSaveInstTime = System.currentTimeMillis();
			List<DataInstMinutely> datas = new ArrayList<DataInstMinutely>();
			dataInstMinutelyQueue.drainTo(datas, count);
			try {
				long startTime = System.currentTimeMillis();
				
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				Set<DataInstMinutely> set = new TreeSet<DataInstMinutely>(new Comparator<DataInstMinutely>() {
					@Override
					public int compare(DataInstMinutely o1, DataInstMinutely o2) {
						String o1String = o1.getDataInstPK().getSdpId() + "#";
						o1String += (sdf.format(o1.getDataInstPK().getTv()) + "#");
						
						String o2String = o2.getDataInstPK().getSdpId() + "#";
						o2String += (sdf.format(o2.getDataInstPK().getTv()) + "#");
						return o1String.compareTo(o2String); 
					}
		        
		        });
		        set.addAll(datas);
		        datas = new ArrayList<DataInstMinutely>(set);

				String loggerInfo = "Insert";
				boolean ret = dataAccessService.batchSaveInsts("mdm_data_inst_minutely", datas);
				if(!ret) {
					loggerInfo = "Update";
					dataAccessService.batchUpdateSaveInsts("mdm_data_inst_minutely", datas);
				}

				loggerInfo += (" mdm_data_inst_minutely[count: " + count + ", time: "
						+ (System.currentTimeMillis() - startTime) + "]");
				log.info(loggerInfo);
				Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "Database", loggerInfo);
			} catch (Exception e) {
				log.error("DataImport Error : " , e);
				dataInstMinutelyQueue.addAll(datas);
			}
			return count;
		}
		
		/**
		 * 保存数据更新日志
		 * @param count 保存数量
		 * @return 实际保存数量
		 */
		private int dataLogSave(int count) {
			if(mdmDataUpdateLogQueue.size() == 0)
				return 0;
			if(System.currentTimeMillis() - lastSaveLogTime < 15000
				&& mdmDataUpdateLogQueue.size() < 100)
				return 0;
			if(count > mdmDataUpdateLogQueue.size()) 
				count = mdmDataUpdateLogQueue.size();
			lastSaveLogTime = System.currentTimeMillis();
			List<MdmDataUpdateLog> datas = new ArrayList<MdmDataUpdateLog>();
			mdmDataUpdateLogQueue.drainTo(datas, count);
			// 对日志数据进行去重处理
			Map<String, MdmDataUpdateLog> mapDatas = new HashMap<String, MdmDataUpdateLog>();
			for (MdmDataUpdateLog data : datas) {
				String key = data.getMdmDataUpdateLogPK().getSdpId();
				key += "\r\n";
				key += data.getMdmDataUpdateLogPK().getDataType();
				key += "\r\n";
				key += data.getMdmDataUpdateLogPK().getSchemeType();
				mapDatas.put(key, data);
			}
			datas.clear();
			for(Entry<String, MdmDataUpdateLog> entry : mapDatas.entrySet()) {
				datas.add(entry.getValue());
			}
			count = datas.size();
			
			try {
				long startTime = System.currentTimeMillis();
				dataAccessService.batchSaveLog(datas);
				String loggerInfo = "Into mdm_data_update_log[count: " + count + ", time: "
						+ (System.currentTimeMillis() - startTime) + "]";
				log.info(loggerInfo);
				Logger.getInstance().writeLogInfo(LoggerLevel.INFO, serverConfig.getServiceId(), "0", "Database", loggerInfo);
			} catch (Exception e) {
				log.error("DataImport Error : " , e);
				mdmDataUpdateLogQueue.addAll(datas);
			}
			return count;
		}		
		
		/**
		 * 线程运行方法
		 * 循环处理各类数据队列中的数据
		 */
		@Override
		public void run() {
			while(true){
				int retCount = 0;
				// 处理表码数据
				retCount = dataMinutelySaveReg(1000);
				retCount += dataDailySaveReg(1000);
				retCount += dataMonthlySaveReg(1000);
				
				// 处理增量数据
				retCount = dataMinutelySaveInterval(1000);
				retCount += dataDailySaveInterval(1000);
				retCount += dataMonthlySaveInterval(1000);
				// 处理瞬时量数据
				retCount += dataInstSave(1000);
				
				// 处理数据更新日志
				retCount += dataLogSave(1000);
				
				// 如果没有数据需要处理，则等待1秒
				if(retCount == 0){
					try {
						Thread.sleep(1000);
					} catch (InterruptedException e) {
						// TODO Auto-generated catch block
						//e.printStackTrace();
						log.error("DataImport Error : " , e);
					}
				}
			}
		}
		
	}
	
}
