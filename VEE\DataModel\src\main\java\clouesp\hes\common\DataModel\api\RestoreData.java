package clouesp.hes.common.DataModel.api;

import java.util.Date;

public class RestoreData {
	private String dataExceptionId;
	private Integer dataType;
	private Integer dataVersion;
	private String schemeId;
	private Integer schemeType;
	private String sdpId;
	private Date tv;
	public String getDataExceptionId() {
		return dataExceptionId;
	}
	public void setDataExceptionId(String dataExceptionId) {
		this.dataExceptionId = dataExceptionId;
	}
	public Integer getDataType() {
		return dataType;
	}
	public void setDataType(Integer dataType) {
		this.dataType = dataType;
	}
	public Integer getDataVersion() {
		return dataVersion;
	}
	public void setDataVersion(Integer dataVersion) {
		this.dataVersion = dataVersion;
	}
	public String getSchemeId() {
		return schemeId;
	}
	public void setSchemeId(String schemeId) {
		this.schemeId = schemeId;
	}
	public Integer getSchemeType() {
		return schemeType;
	}
	public void setSchemeType(Integer schemeType) {
		this.schemeType = schemeType;
	}
	public String getSdpId() {
		return sdpId;
	}
	public void setSdpId(String sdpId) {
		this.sdpId = sdpId;
	}
	public Date getTv() {
		return tv;
	}
	public void setTv(Date tv) {
		this.tv = tv;
	}	
}
