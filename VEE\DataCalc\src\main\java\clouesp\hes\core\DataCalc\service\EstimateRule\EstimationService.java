package clouesp.hes.core.DataCalc.service.EstimateRule;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import javax.annotation.Resource;

import jline.internal.Log;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import clouesp.hes.common.DataEntity.Asset.MdmAssetCalcScheme;
import clouesp.hes.common.DataEntity.Asset.MdmAssetServicePoint;
import clouesp.hes.common.DataEntity.Asset.MdmAssetVEERule;
import clouesp.hes.common.DataEntity.Asset.MdmAssetVEERuleDataSource;
import clouesp.hes.common.DataEntity.Data.DataIntervalDaily;
import clouesp.hes.common.DataEntity.Data.DataIntervalHistory;
import clouesp.hes.common.DataEntity.Data.DataIntervalMinutely;
import clouesp.hes.common.DataEntity.Data.DataIntervalMonthly;
import clouesp.hes.common.DataEntity.Data.DataReg;
import clouesp.hes.common.DataEntity.Data.DataRegDaily;
import clouesp.hes.common.DataEntity.Data.DataRegHistory;
import clouesp.hes.common.DataEntity.Data.DataRegHistoryPK;
import clouesp.hes.common.DataEntity.Data.DataRegMinutely;
import clouesp.hes.common.DataEntity.Data.DataRegMonthly;
import clouesp.hes.common.DataEntity.Dict.VEEMethod;
import clouesp.hes.common.DataModel.api.AjaxJson;
import clouesp.hes.common.DataModel.api.MdmDataVeeBase;
import clouesp.hes.common.DataModel.api.RestoreData;
import clouesp.hes.common.DataRepository.Persistence.Data.DataIntervalDailyRepository;
import clouesp.hes.common.DataRepository.Persistence.Data.DataIntervalHistoryRepository;
import clouesp.hes.common.DataRepository.Persistence.Data.DataIntervalMinutelyRepository;
import clouesp.hes.common.DataRepository.Persistence.Data.DataIntervalMonthlyRepository;
import clouesp.hes.common.DataRepository.Persistence.Data.DataRegDailyRepository;
import clouesp.hes.common.DataRepository.Persistence.Data.DataRegHistoryRepository;
import clouesp.hes.common.DataRepository.Persistence.Data.DataRegMinutelyRepository;
import clouesp.hes.common.DataRepository.Persistence.Data.DataRegMonthlyRepository;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetCalcSchemeRepository;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetServicePointRepository;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetVEERuleDataSourceRepository;
import clouesp.hes.common.DataRepository.RealTime.Asset.RtMdmAssetVEERuleRepository;
import clouesp.hes.common.DataRepository.RealTime.Dict.RtVEEMethodRepository;
import clouesp.hes.core.DataCalc.Utils.DataUtils;
import clouesp.hes.core.DataCalc.service.Data.DataSourceService;
import groovy.lang.GroovyObject;
import groovy.util.GroovyScriptEngine;

@Service("estimationService")
public class EstimationService {
	@Autowired
	private RtMdmAssetVEERuleRepository rtMdmAssetVEERuleRepository;
	
	@Autowired
	private RtMdmAssetVEERuleDataSourceRepository rtMdmAssetVEERuleDataSourceRepository;
	
	@Autowired
	private RtVEEMethodRepository rtVEEMethodRepository;
	
	@Autowired
	private RtMdmAssetServicePointRepository rtMdmAssetServicePointRepository;
	
	@Autowired
	private DataRegMinutelyRepository dataRegMinutelyRepository;
	
	@Autowired
	private DataRegDailyRepository dataRegDailyRepository;
	
	@Autowired
	private DataRegMonthlyRepository dataRegMonthlyRepository;
	
	@Autowired
	private DataIntervalMinutelyRepository dataIntervalMinutelyRepository;
	
	@Autowired
	private DataIntervalDailyRepository dataIntervalDailyRepository;
	
	@Autowired
	private DataIntervalMonthlyRepository dataIntervalMonthlyRepository;	
	
	@Autowired
	private DataRegHistoryRepository dataRegHistoryRepository;
	
	@Autowired
	private DataIntervalHistoryRepository dataIntervalHistoryRepository;
	
	@Autowired
	private RtMdmAssetCalcSchemeRepository rtMdmAssetCalcSchemeRepository;
	
	@Resource(name="dataSourceService")
	private DataSourceService dataSourceService;
	
	private GroovyScriptEngine groovyScriptEngine;
		
	public void startService(String serviceId) {
		String path = System.getProperty("user.dir") + "/EstimationScript/";
		String[] root = new String[] {path};
		try {
			groovyScriptEngine = new GroovyScriptEngine(root);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
		
	@SuppressWarnings("rawtypes")
	public Object invokeMethod(String scriptName, String methodName, Object... params) throws Exception {
		Object ret = null;
		Class scriptClass = groovyScriptEngine.loadScriptByName(scriptName);
		GroovyObject scriptInstance = (GroovyObject) scriptClass.newInstance();
		ret = scriptInstance.invokeMethod(methodName, params);
		return ret;
	}
	
	public int restoreDatas(List<RestoreData> datas) {
		int count = 0;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		for (RestoreData data : datas) {
			String sdpId = data.getSdpId();
			int dataVersion = data.getDataVersion();
			int dataType = data.getDataType();
			String schemeId = data.getSchemeId();
			int schemeType = data.getSchemeType();
			Date tv = data.getTv();
			String strTv = sdf.format(data.getTv());
						
			Log.info(
					"Restore data[Data type:" + dataType 
					+ ", Scheme id:" + schemeId
					+ ", Scheme type:" + schemeType  
					+ ", TV:" + strTv  
					+ "]");
			switch (schemeType) {
			case 2:
				if (dataType == 101) {
					DataRegHistoryPK dataRegHistoryPK = new DataRegHistoryPK();
					dataRegHistoryPK.setSdpId(sdpId);
					dataRegHistoryPK.setDataVersion(dataVersion);
					dataRegHistoryPK.setSchemeType(schemeType);
					dataRegHistoryPK.setTv(tv);
					
					Optional<DataRegHistory> optionalDataRegHistory = dataRegHistoryRepository
							.findById(dataRegHistoryPK);
					if (!optionalDataRegHistory.isPresent()) {
						Log.info("Not find data[" + data.getSdpId() + "]");
						continue;
					}
					DataRegHistory dataRegHistory = optionalDataRegHistory.get();
					DataRegDaily dataRegDaily = new DataRegDaily();
					BeanUtils.copyProperties(dataRegHistory, dataRegDaily);
					dataRegDaily.getDataRegPK().setSdpId(dataRegHistory.getDataRegHistoryPK().getSdpId());
					dataRegDaily.getDataRegPK().setTv(dataRegHistory.getDataRegHistoryPK().getTv());
					dataRegDaily.setDataVersion(dataRegHistory.getDataRegHistoryPK().getDataVersion());
					dataRegDailyRepository.save(dataRegDaily);
					count++;
				}
				break;
			}
		}
		return count;
	}
	
	public int saveDatas(List<MdmDataVeeBase> datas) {
		int count = 0;
		for (MdmDataVeeBase data : datas) {
			Integer dataType = data.getDataType();
							
			DataReg dataReg = DataUtils.mdmDataVeeBase2DataReg(data);
			Integer schemeType = null;
			
			if (data.getSchemeType() != null) {
				schemeType = data.getSchemeType().intValue();
			}
			if (schemeType == null) {
				Optional<MdmAssetCalcScheme> optionalScheme = rtMdmAssetCalcSchemeRepository
						.findById(data.getSchemeId());
				if (!optionalScheme.isPresent()) {
					continue;
				}
				schemeType = optionalScheme.get().getType();
			}
	
			switch(schemeType) {
			case 1:
				if(dataType == 101) {
					DataRegMinutely dataRegMinutely = new DataRegMinutely();
					BeanUtils.copyProperties(dataReg, dataRegMinutely);
					
					Optional<DataRegMinutely> optionCurData = dataRegMinutelyRepository.findById(dataReg.getDataRegPK());
					if (optionCurData.isPresent()) {
						DataRegHistory dataHistory = new DataRegHistory();
						dataHistory.setOperatorId(data.getOperatorId());
						dataHistory.setOperatorIp(data.getOperatorIp());
						dataHistory.setOperatorName(data.getOperatorName());
						dataHistory.setOperatorReason(data.getOperatorReason());
						
						DataRegMinutely curData = optionCurData.get();
						BeanUtils.copyProperties(curData, dataHistory);
						dataHistory.getDataRegHistoryPK().setDataVersion(curData.getDataVersion());
						dataHistory.getDataRegHistoryPK().setSchemeType(schemeType);
						dataRegHistoryRepository.save(dataHistory);
						
						dataRegMinutely.setDataVersion(curData.getDataVersion() + 1);
					}
					
					dataRegMinutelyRepository.save(dataRegMinutely);
				} else if(dataType == 102) {
					DataIntervalMinutely dataIntervalMinutely = new DataIntervalMinutely();
					BeanUtils.copyProperties(dataReg, dataIntervalMinutely);
					
					Optional<DataIntervalMinutely> optionCurData = dataIntervalMinutelyRepository.findById(dataReg.getDataRegPK());
					if (optionCurData.isPresent()) {
						DataIntervalHistory dataHistory = new DataIntervalHistory();
						dataHistory.setOperatorId(data.getOperatorId());
						dataHistory.setOperatorIp(data.getOperatorIp());
						dataHistory.setOperatorName(data.getOperatorName());
						dataHistory.setOperatorReason(data.getOperatorReason());
						
						
						DataIntervalMinutely curData = optionCurData.get();
						BeanUtils.copyProperties(curData, dataHistory);
						dataHistory.getDataRegHistoryPK().setDataVersion(curData.getDataVersion());
						dataHistory.getDataRegHistoryPK().setSchemeType(schemeType);
						dataIntervalHistoryRepository.save(dataHistory);
						
						dataIntervalMinutely.setDataVersion(curData.getDataVersion() + 1);
					}					
					
					dataIntervalMinutelyRepository.save(dataIntervalMinutely);
				}
				break;
			case 2:
				if(dataType == 101) {
					DataRegDaily dataRegDaily = new DataRegDaily();
					BeanUtils.copyProperties(dataReg, dataRegDaily);
					
					Optional<DataRegDaily> optionCurData = dataRegDailyRepository.findById(dataReg.getDataRegPK());
					if (optionCurData.isPresent()) {
						DataRegHistory dataHistory = new DataRegHistory();
						dataHistory.setOperatorId(data.getOperatorId());
						dataHistory.setOperatorIp(data.getOperatorIp());
						dataHistory.setOperatorName(data.getOperatorName());
						dataHistory.setOperatorReason(data.getOperatorReason());
						
						DataRegDaily curData = optionCurData.get();
						BeanUtils.copyProperties(curData, dataHistory);
						Integer dataVersion = curData.getDataVersion();
						if (dataVersion == null) {
							dataVersion = 0;
						}
						dataHistory.getDataRegHistoryPK().setSdpId(curData.getDataRegPK().getSdpId());
						dataHistory.getDataRegHistoryPK().setTv(curData.getDataRegPK().getTv());
						dataHistory.getDataRegHistoryPK().setDataVersion(dataVersion);
						dataHistory.getDataRegHistoryPK().setSchemeType(schemeType);
						dataRegHistoryRepository.save(dataHistory);
						
						dataRegDaily.setDataVersion(dataVersion + 1);
					}									
					
					dataRegDailyRepository.save(dataRegDaily);
				} else if(dataType == 102) {
					DataIntervalDaily dataIntervalDaily = new DataIntervalDaily();
					BeanUtils.copyProperties(dataReg, dataIntervalDaily);
					
					Optional<DataIntervalDaily> optionCurData = dataIntervalDailyRepository.findById(dataReg.getDataRegPK());
					if (optionCurData.isPresent()) {
						DataRegHistory dataHistory = new DataRegHistory();
						dataHistory.setOperatorId(data.getOperatorId());
						dataHistory.setOperatorIp(data.getOperatorIp());
						dataHistory.setOperatorName(data.getOperatorName());
						dataHistory.setOperatorReason(data.getOperatorReason());	
						
						DataIntervalDaily curData = optionCurData.get();
						BeanUtils.copyProperties(curData, dataHistory);
						dataHistory.getDataRegHistoryPK().setDataVersion(curData.getDataVersion());
						dataHistory.getDataRegHistoryPK().setSchemeType(schemeType);
						dataRegHistoryRepository.save(dataHistory);
						
						dataIntervalDaily.setDataVersion(curData.getDataVersion() + 1);
					}							
					
					dataIntervalDailyRepository.save(dataIntervalDaily);
				}				
				break;
			case 3:
				if(dataType == 101) {
					DataRegMonthly dataRegMonthly = new DataRegMonthly();
					BeanUtils.copyProperties(dataReg, dataRegMonthly);	
					
					Optional<DataRegMonthly> optionCurData = dataRegMonthlyRepository.findById(dataReg.getDataRegPK());
					if (optionCurData.isPresent()) {		
						DataRegHistory dataHistory = new DataRegHistory();
						dataHistory.setOperatorId(data.getOperatorId());
						dataHistory.setOperatorIp(data.getOperatorIp());
						dataHistory.setOperatorName(data.getOperatorName());
						dataHistory.setOperatorReason(data.getOperatorReason());						
						
						DataRegMonthly curData = optionCurData.get();
						BeanUtils.copyProperties(curData, dataHistory);
						dataHistory.getDataRegHistoryPK().setDataVersion(curData.getDataVersion());
						dataHistory.getDataRegHistoryPK().setSchemeType(schemeType);
						dataRegHistoryRepository.save(dataHistory);
						
						dataRegMonthly.setDataVersion(curData.getDataVersion() + 1);
					}							
					
					dataRegMonthlyRepository.save(dataRegMonthly);
				} else if(dataType == 102) {
					DataIntervalMonthly dataIntervalMonthly = new DataIntervalMonthly();
					BeanUtils.copyProperties(dataReg, dataIntervalMonthly);	
					
					Optional<DataIntervalMonthly> optionCurData = dataIntervalMonthlyRepository.findById(dataReg.getDataRegPK());
					if (optionCurData.isPresent()) {
						DataRegHistory dataHistory = new DataRegHistory();
						dataHistory.setOperatorId(data.getOperatorId());
						dataHistory.setOperatorIp(data.getOperatorIp());
						dataHistory.setOperatorName(data.getOperatorName());
						dataHistory.setOperatorReason(data.getOperatorReason());						
						
						DataIntervalMonthly curData = optionCurData.get();
						BeanUtils.copyProperties(curData, dataHistory);
						dataHistory.getDataRegHistoryPK().setDataVersion(curData.getDataVersion());
						dataHistory.getDataRegHistoryPK().setSchemeType(schemeType);
						dataRegHistoryRepository.save(dataHistory);
						
						dataIntervalMonthly.setDataVersion(curData.getDataVersion() + 1);
					}							
					
					dataIntervalMonthlyRepository.save(dataIntervalMonthly);
				}				
				break;
			}
			count++;
		}
		return count;
	}
		
	public AjaxJson estimate(
			String ruleId, 
			String schemeId, 
			String sdpId, 
			Integer dataType,
			List<Date> dataTvs) {
		AjaxJson rsp = new AjaxJson();
		try {
			
			Optional<MdmAssetVEERule> optionalRule = rtMdmAssetVEERuleRepository.findById(ruleId);
			if (!optionalRule.isPresent()) {
				rsp.setSuccess(false);
				rsp.setErrorMsg("Failed get the rule[" + ruleId + "]");
				return rsp;
			}
			MdmAssetVEERule rule = optionalRule.get();
			String methodId = rule.getMethod();
			if (methodId == null || "".equals(methodId)) {
				rsp.setSuccess(false);
				rsp.setErrorMsg("Method is null");
				return rsp;
			}

			Optional<VEEMethod> optionalMethod = rtVEEMethodRepository.findById(methodId);
			if (!optionalMethod.isPresent()) {
				rsp.setSuccess(false);
				rsp.setErrorMsg("Failed get the method[" + methodId + "]");
				return rsp;
			}

			VEEMethod method = optionalMethod.get();
			String packageId = method.getPackageId();
			String[] splitPackageId = packageId.split("/");
			String scriptName = splitPackageId[0] + ".groovy";
			String methodName = splitPackageId[1];

			List<MdmAssetVEERuleDataSource> dataSources = rtMdmAssetVEERuleDataSourceRepository.findByRuleIdAndSchemeType(ruleId,Integer.parseInt(schemeId));
			if (dataSources == null || dataSources.size() == 0) {
				rsp.setSuccess(false);
				rsp.setErrorMsg("Failed get the data source");
				return rsp;
			}
				
			Optional<MdmAssetServicePoint> OptionalSdp = rtMdmAssetServicePointRepository.findById(sdpId);
			if(!OptionalSdp.isPresent()) {
				rsp.setSuccess(false);
				rsp.setErrorMsg("Failed get the sdp[" + sdpId + "]");
				return rsp;
			}
			MdmAssetServicePoint sdp = OptionalSdp.get();
			List<Object> retDatas = new ArrayList<Object>();
			
			
			for (Date dataTv : dataTvs) {
				Map<String, Object> params = new HashMap<String, Object>();
				params.put("dataTv", dataTv);
				params.put("sdpId", sdpId);
				params.put("schemeId", schemeId);
				params.put("dataType", dataType);
				int srcCount = 0;
				for (MdmAssetVEERuleDataSource dataSource : dataSources) {
					List<DataReg> dataRegs = dataSourceService.getEstimationDataList(
							dataSource, 
							sdp, 
							dataTv, 
							dataTvs);
					if (dataRegs == null || dataRegs.size() == 0) {
						rsp.setErrorMsg("Failed get data");
						break;				
					}
					srcCount++;
					params.put(dataSource.getDataCode(), dataRegs);
				}
				if (srcCount > 0 && srcCount == dataSources.size()) {
					Object retData = invokeMethod(scriptName, methodName, params);			
					retDatas.add(retData);
				}
			}
			rsp.setSuccess(retDatas.size() > 0);
			rsp.setObj(retDatas);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			rsp.setSuccess(false);
			rsp.setErrorMsg(e.getMessage());
			e.printStackTrace();
		}			
		return rsp;
	}

}
