/*
 * 文件名：LoggerAppenderFactory.java 版权：Copyright by Power7000 Team 描述： 修改人：jybai 修改时间：2017年10月30日
 * 跟踪单号： 修改单号： 修改内容：
 */

package clouesp.hes.common.logger.logger;


import java.util.HashMap;
import java.util.Map;


class LoggerAppenderFactory {

    private Map<LoggerAppenderType, ILoggerAppender> appenderSet = new HashMap<LoggerAppenderType, ILoggerAppender>();

    private static LoggerAppenderFactory instance = null;

    private LoggerAppenderFactory() {
        addAppender(LoggerAppenderType.Console);
        addAppender(LoggerAppenderType.Lucene);
    }

    public static LoggerAppenderFactory getInstance() {
        if (instance == null) instance = new LoggerAppenderFactory();
        return instance;
    }

    public void addAppender(LoggerAppenderType type) {
        if (appenderSet.containsKey(type)) return;
        if (type == LoggerAppenderType.Console)
            appenderSet.put(type, new LoggerConsoleAppender());
        else if (type == LoggerAppenderType.Lucene)
            appenderSet.put(type, new LoggerLuceneAppender());
    }

    public void removeAppender(LoggerAppenderType type) {
        if (appenderSet.containsKey(type)) {
            appenderSet.get(type).stop();
            appenderSet.remove(type);
        }
    }

    public void removeAllAppender() {
        for (Map.Entry<LoggerAppenderType, ILoggerAppender> entry : appenderSet.entrySet())
            entry.getValue().stop();
        appenderSet.clear();
    }

    public ILoggerAppender getAppender(LoggerAppenderType type) {
        if (appenderSet.containsKey(type)) return appenderSet.get(type);
        return null;
    }

    public void setLevel(LoggerLevel level) {
        for (Map.Entry<LoggerAppenderType, ILoggerAppender> entry : appenderSet.entrySet())
            entry.getValue().setLevel(level);
    }

    public void start() {
        for (Map.Entry<LoggerAppenderType, ILoggerAppender> entry : appenderSet.entrySet())
            entry.getValue().start();
    }

    public void stop() {
        for (Map.Entry<LoggerAppenderType, ILoggerAppender> entry : appenderSet.entrySet())
            entry.getValue().stop();
    }
}
