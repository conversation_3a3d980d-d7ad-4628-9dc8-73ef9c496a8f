<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="DataMeterEventMapper">

    <select id="getMeterEventReportList" parameterType="PagingRequest"
            resultType="com.clou.common.DataEntity.Model.Data.MeterEventDataResult">

        <if test="entity">
            <if test="entity.deviceType != null and  entity.deviceType == 1">
                select meter.sn as deviceSn , commu.sn as commuSn ,evt.tv as evtTv ,
                groupInfo.groupName as evtType ,
                item.name as evtName ,evt.event_detail as evtDetails  ,
                <choose>
                    <when test="entity.onlySubscribe == 1">
                       'Yes' as onlySubscribe
                    </when>
                    <otherwise>
                        case when alarm.alarm_id is null then 'No' else 'Yes' end as onlySubscribe
                    </otherwise>
                </choose>
                       from ASSET_METER meter
                inner join ASSET_COMMUNICATOR commu on meter.COMMUNICATOR_ID = commu.id
                <if test="entity.devSn != null and entity.devSn != ''" >
                    <if test="entity.devSnType == null  or  entity.devSnType == 1">
                       and meter.sn  = #{entity.devSn}
                    </if>
                    <if test="entity.devSnType != null  and entity.devSnType == 2">
                        and commu.sn  = #{entity.devSn}
                    </if>
                </if>
                <if test="entity.orgCode != null and entity.orgCode != ''" >
                  AND meter.org_id  in (select id from sys_org where org_code like  concat(#{entity.orgCode},'%'))
                </if>
                <if test="params.orgIdList != null and params.orgIdList.size > 0">
                    and meter.org_id in
                    <foreach collection="params.orgIdList" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>

                inner join DATA_METER_EVENT evt on meter.id = evt.device_id
            </if>

            </if>
        <if test="entity">
            <if test="entity.deviceType != null   and entity.deviceType == 2">
                select commu.sn as deviceSn , commu.sn as commuSn ,evt.tv as evtTv ,
               groupInfo.groupName as evtType ,
                item.name as evtName ,evt.event_detail as evtDetails  ,
                <choose>
                    <when test="entity.onlySubscribe == 1">
                        'Yes' as onlySubscribe
                    </when>
                    <otherwise>
                        case when alarm.alarm_id is null then 'No' else 'Yes' end as onlySubscribe
                    </otherwise>
                </choose>
                from  ASSET_COMMUNICATOR commu
                inner join DATA_METER_EVENT evt on commu.id = evt.device_id
                <if test="entity.devSn != null and entity.devSn != ''" >
                    and commu.sn = #{entity.devSn}
                </if>

                <if test="entity.orgCode != null and entity.orgCode != ''" >
                    AND commu.org_id  in (select id from sys_org where org_code like  concat(#{entity.orgCode},'%'))
                </if>

                <if test="params.orgIdList != null and params.orgIdList.size > 0">
                    and commu.org_id in
                    <foreach collection="params.orgIdList" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>

            </if>
        </if>
        <if test="entity.onlySubscribe != 1">
            left join (
            SELECT T10.ALARM_ID FROM MDM_ASSET_ALARM_USER_CONFIG T10 WHERE T10.USER_ID=#{params.userId}  AND T10.ALARM_TYPE='1' AND T10.ALARM_MODE = 1  ) alarm  on evt.EVENT_ID = alarm.alarm_id
        </if>
        left join ( select  g.id as groupId , g.name as groupName , m.dataitem_id as dataitem_id from DICT_DATAITEM_GROUP g , dict_dataitem_group_map m where g.id = m.group_id and g.app_type = 2 )   groupInfo on groupInfo.dataitem_id = evt.event_id
        left join DICT_DATAITEM item  on item.id = evt.event_id where 1= 1
        <if test="entity">
            <if test="entity.startTime != null   " >
                and  <![CDATA[evt.tv >= #{entity.startTime}  ]]>
            </if>
            <if test="entity.endTime != null   " >
                and  <![CDATA[ evt.tv<= #{entity.endTime}  ]]>
            </if>

         <choose>
                <when test="entity.onlySubscribe == 1">
                    AND evt.EVENT_ID IN (SELECT T10.ALARM_ID FROM MDM_ASSET_ALARM_USER_CONFIG T10 WHERE T10.USER_ID=#{params.userId}  AND T10.ALARM_TYPE='1' AND T10.ALARM_MODE IN ('1','3') )
                </when>
                <otherwise>
                    <if test="entity.listEventId !=null and entity.listEventId.size() &gt; 0 ">
                        AND evt.EVENT_ID IN
                        <foreach collection="entity.listEventId" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                </otherwise>
            </choose>

        </if>


        <if test="sortList != null and sortList.size > 0">
            order by
            <foreach collection="sortList" item="item" separator="," open=" " close=" ">
                ${item.field} ${item.sort}
            </foreach>
        </if>
    </select>



</mapper>